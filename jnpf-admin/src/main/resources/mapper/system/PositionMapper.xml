<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.permission.mapper.PositionMapper">

    <select id="getListByUserId" parameterType="String" resultType="jnpf.permission.entity.PositionEntity">
        SELECT * FROM base_position WHERE F_ID IN(SELECT F_OBJECT_ID FROM base_user_relation WHERE F_USER_ID = #{userId}) ORDER BY F_SORT_CODE
    </select>

</mapper>
