<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.mapper.FlowFormMapper">

    <resultMap id="flowInfo" type="jnpf.base.model.flow.FlowTempInfoModel">
        <id column="F_Id" property="id"/>
        <result column="F_EnCode" property="enCode"/>
    </resultMap>


    <select id="findFLowInfo" parameterType="String" resultMap="flowInfo">
        SELECT ft.F_En_Code as F_EnCode,ft.F_Id as F_Id,F_Enabled_Mark as enabledMark FROM  flow_template ft where ft.F_Id =#{tempId}
    </select>

</mapper>
