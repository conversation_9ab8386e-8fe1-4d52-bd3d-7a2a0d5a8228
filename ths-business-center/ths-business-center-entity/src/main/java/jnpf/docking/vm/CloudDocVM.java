package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * @author: 沈泽宇
 * @date: 2022年06月2022/6/20日
 */
public class CloudDocVM {

    private String id;

    /**
     * 空则为顶级目录
     */
    @ApiModelProperty(value = "父级目录，空则为顶级目录")
    private String parentId;

    /**
     * 是否为目录  0为文件，1为目录
     */
    @ApiModelProperty(value = "是否为文件夹  false为文件，true为文件夹")
    private Boolean isDir;

    /**
     * 是否锁定 默认值为0
     */
    @ApiModelProperty(value = "是否锁定 默认值为false")
    private Boolean isLocked;

    /**
     * 排序号  默认10
     */
    @ApiModelProperty(value = "排序号  默认10")
    private int orderNum;

    /**
     * 文件类型id
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Instant createdDate;

    /**
     * 最后修改人
     */
    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间")
    private Instant lastModifiedDate;


    @ApiModelProperty(value = "文件大小,单位 B,上传时可不传")
    private Long fileSize;

    @ApiModelProperty(value = "文件hash，上传时不传")
    private String fileHash;

    /**
     * 虚拟文件版本号  同一个list id,从1开始递增
     */
    @ApiModelProperty(value = "文件版本号")
    private int docVersion;

    @ApiModelProperty(value = "是否可下载")
    private Boolean isDownload;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer Sequence;

    /**
     * 类别(很重要) 1：建设工程 2:单项工程 3：单位工程
     */
    @ApiModelProperty(value = "类别")
    private Integer PrjType;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String Code;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "文件/目录/项目名称")
    private String Name;

    /**
     * 工程总造价
     */
    @ApiModelProperty(value = "工程总造价")
    private BigDecimal PrjFeeSum;

    /**
     * 单位工程总造价
     */
    @ApiModelProperty(value = "单位工程总造价")
    private BigDecimal ZZJ;

    /**
     * 占总造价(%)
     */
    @ApiModelProperty(value = "占总造价(%)")
    private BigDecimal SumRate;

    /**
     * 分部分项合计
     */
    @ApiModelProperty(value = "分部分项合计")
    private BigDecimal FBFXHJ;

    /**
     * 措施项目合计
     */
    @ApiModelProperty(value = "措施项目合计")
    private BigDecimal CSXMHJ;

    /**
     * 其它项目合计
     */
    @ApiModelProperty(value = "其它项目合计")
    private BigDecimal QTXMHJ;

    /**
     * 规费
     */
    @ApiModelProperty(value = "规费")
    private BigDecimal GF;

    /**
     * 税金
     */
    @ApiModelProperty(value = "税金")
    private BigDecimal SJ;

    /**
     * 工程建设其它费
     */
    @ApiModelProperty(value = "工程建设其它费")
    private BigDecimal OtherFee;

    /**
     * 其它设备购置费
     */
    @ApiModelProperty(value = "其它设备购置费")
    private BigDecimal OtherSBGZF;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private BigDecimal BuildArea;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String DW;

    /**
     * 单方造价
     */
    @ApiModelProperty(value = "单方造价")
    private BigDecimal PrjSumPrice;

    /**
     * 材料暂估价
     */
    @ApiModelProperty(value = "材料暂估价")
    private BigDecimal ZGJHJ;

    /**
     * 安全文明施工费
     */
    @ApiModelProperty(value = "安全文明施工费")
    private BigDecimal AQWMSGF;

    /**
     * 暂列金额
     */
    @ApiModelProperty(value = "暂列金额")
    private BigDecimal ZLJE;

    /**
     * 人工费合计
     */
    @ApiModelProperty(value = "人工费合计")
    private BigDecimal RGFHJ;

    /**
     * 材料费合计
     */
    @ApiModelProperty(value = "材料费合计")
    private BigDecimal CLFHJ;

    /**
     * 机械费合计
     */
    @ApiModelProperty(value = "机械费合计")
    private BigDecimal JXFHJ;

    /**
     * 管理费合计
     */
    @ApiModelProperty(value = "管理费合计")
    private BigDecimal GLFHJ;

    /**
     * 利润合计
     */
    @ApiModelProperty(value = "利润合计")
    private BigDecimal LRHJ;


//    /**
//     * 编制人
//     */
//    @ApiModelProperty(value = "编制人")
//    private String preparedBy;
//
//    /**
//     * 责任人
//     */
//    @ApiModelProperty(value = "责任人")
//    private String personLiable;
//
//    /**
//     * 编制时间
//     */
//    @ApiModelProperty(value = "编制时间")
//    private Instant preparedDate;
//
//    /**
//     * 审核时间
//     */
//    @ApiModelProperty(value = "审核时间")
//    private Instant auditDate;
//
//    /**
//     * 操作动作  工程文件操作动作  add、delete、alter
//     */
//    @ApiModelProperty(value = "操作动作  工程文件操作动作  add、delete、alter")
//    private String operationAction;

    /**
     * md5  客户端使用，云端仅保存
     */
    @ApiModelProperty(value = "md5  客户端使用，云端仅保存")
    private String checksum;

    @ApiModelProperty(value = "招投标类型  0、1、2（招标、投标、控制价）")
    private String TenderType;

    @ApiModelProperty(value = "地区名称")
    private String DQMC;

    @ApiModelProperty(value = "工程文件截止时间")
    private Instant closingDate;

    @ApiModelProperty(value = "编制人ID 多个以英文逗号','隔开")
    private String preparedBy;

    @ApiModelProperty(value = "责任人ID")
    private String personLiable;

    @ApiModelProperty(value = "审核人ID 多个以英文逗号','隔开")
    private String auditor;

    @ApiModelProperty(value = "知会人ID  多个以英文逗号','隔开")
    private String informer;

    @ApiModelProperty(value = "工程状态 0、1、2、3(进行中、待审核、审核通过、审核不通过)")
    private String engineeringStatus;

    @ApiModelProperty(value = "锁定信息  锁定时才有锁定信息")
    private FileLockedVM lockedInfo;

    @ApiModelProperty(value = "子级")
    private List<CloudDocVM> children;

    @ApiModelProperty(value = "打开模式，0-正常模式 1-评审模式")
    private Integer opentype;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "清单库ID")
    private String sVerId;

    @ApiModelProperty(value = "语种 0 中文, 1 英文")
    private Integer languageType;

    @ApiModelProperty(value = "文件保存路径")
    private String fileSavePath;

    @ApiModelProperty(value = "文件节点名称")
    private String rootName;

    @ApiModelProperty(value = "区域Id")
    private String areaId;
    @ApiModelProperty("是否可以维护审批意见")
    private Boolean operApprovalOpinions;

    @ApiModelProperty("当前流程")
    private String currentFlowDetailId;


    @ApiModelProperty(" 0：编制阶段;1: 审核阶段")
    private Integer auditRole;  //  工程状态 在2的时候在审核阶段，其他状态都是编制状态

    @ApiModelProperty("是否客户端拆分")
    private Boolean splitFileFlag;


    @ApiModelProperty("当前编辑的文件id")
    private String currentEditorFileId;

    @ApiModelProperty("阶段名称")
    private String phaseName;

    @ApiModelProperty(value = "送审文件信息")
    private CloudSendAuditVM sendAuditFile;

    @ApiModelProperty(value = "原始送审文件信息")
    private CloudOriginalSendVM originalSendFile;

    @ApiModelProperty("是否可以回复审批意见")
    private Boolean replyOpinions;

    public CloudSendAuditVM getSendAuditFile() {
        return sendAuditFile;
    }

    public void setSendAuditFile(CloudSendAuditVM sendAuditFile) {
        this.sendAuditFile = sendAuditFile;
    }

    public CloudOriginalSendVM getOriginalSendFile() {
        return originalSendFile;
    }

    public void setOriginalSendFile(CloudOriginalSendVM originalSendFile) {
        this.originalSendFile = originalSendFile;
    }

    public String getPhaseName() {
        return phaseName;
    }

    public void setPhaseName(String phaseName) {
        this.phaseName = phaseName;
    }

    public Integer getAuditRole() {
        return auditRole;
    }

    public void setAuditRole(Integer aduitRole) {
        this.auditRole = aduitRole;
    }

    public Boolean getSplitFileFlag() {
        return splitFileFlag;
    }

    public void setSplitFileFlag(Boolean splitFileFlag) {
        this.splitFileFlag = splitFileFlag;
    }

    public String getCurrentEditorFileId() {
        return currentEditorFileId;
    }

    public void setCurrentEditorFileId(String currentEditorFileId) {
        this.currentEditorFileId = currentEditorFileId;
    }

    public Boolean getOperApprovalOpinions() {
        return operApprovalOpinions;
    }

    public void setOperApprovalOpinions(Boolean operApprovalOpinions) {
        this.operApprovalOpinions = operApprovalOpinions;
    }

    public String getCurrentFlowDetailId() {
        return currentFlowDetailId;
    }

    public void setCurrentFlowDetailId(String currentFlowDetailId) {
        this.currentFlowDetailId = currentFlowDetailId;
    }


    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public List<CloudDocVM> getChildren() {
        return children;
    }

    public void setChildren(List<CloudDocVM> children) {
        this.children = children;
    }

    public FileLockedVM getLockedInfo() {
        return lockedInfo;
    }

    public void setLockedInfo(FileLockedVM lockedInfo) {
        this.lockedInfo = lockedInfo;
    }

    public String getPreparedBy() {
        return preparedBy;
    }

    public void setPreparedBy(String preparedBy) {
        this.preparedBy = preparedBy;
    }

    public String getPersonLiable() {
        return personLiable;
    }

    public void setPersonLiable(String personLiable) {
        this.personLiable = personLiable;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getInformer() {
        return informer;
    }

    public void setInformer(String informer) {
        this.informer = informer;
    }

    public Instant getClosingDate() {
        return closingDate;
    }

    public void setClosingDate(Instant closingDate) {
        this.closingDate = closingDate;
    }

    public String getTenderType() {
        return TenderType;
    }

    public void setTenderType(String tenderType) {
        TenderType = tenderType;
    }

    public String getDQMC() {
        return DQMC;
    }

    public void setDQMC(String DQMC) {
        this.DQMC = DQMC;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Boolean getIsDir() {
        return isDir;
    }

    public void setIsDir(Boolean isDir) {
        this.isDir = isDir;
    }

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Boolean isLocked) {
        this.isLocked = isLocked;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public int getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(int docVersion) {
        this.docVersion = docVersion;
    }

    public Boolean getIsDownload() {
        return isDownload;
    }

    public void setIsDownload(Boolean isDownload) {
        this.isDownload = isDownload;
    }

    public Integer getSequence() {
        return Sequence;
    }

    public void setSequence(Integer sequence) {
        Sequence = sequence;
    }

    public Integer getPrjType() {
        return PrjType;
    }

    public void setPrjType(Integer prjType) {
        PrjType = prjType;
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }

    public BigDecimal getPrjFeeSum() {
        return PrjFeeSum;
    }

    public void setPrjFeeSum(BigDecimal prjFeeSum) {
        PrjFeeSum = prjFeeSum;
    }

    public BigDecimal getZZJ() {
        return ZZJ;
    }

    public void setZZJ(BigDecimal ZZJ) {
        this.ZZJ = ZZJ;
    }

    public BigDecimal getSumRate() {
        return SumRate;
    }

    public void setSumRate(BigDecimal sumRate) {
        SumRate = sumRate;
    }

    public BigDecimal getFBFXHJ() {
        return FBFXHJ;
    }

    public void setFBFXHJ(BigDecimal FBFXHJ) {
        this.FBFXHJ = FBFXHJ;
    }

    public BigDecimal getCSXMHJ() {
        return CSXMHJ;
    }

    public void setCSXMHJ(BigDecimal CSXMHJ) {
        this.CSXMHJ = CSXMHJ;
    }

    public BigDecimal getQTXMHJ() {
        return QTXMHJ;
    }

    public void setQTXMHJ(BigDecimal QTXMHJ) {
        this.QTXMHJ = QTXMHJ;
    }

    public BigDecimal getGF() {
        return GF;
    }

    public void setGF(BigDecimal GF) {
        this.GF = GF;
    }

    public BigDecimal getSJ() {
        return SJ;
    }

    public void setSJ(BigDecimal SJ) {
        this.SJ = SJ;
    }

    public BigDecimal getOtherFee() {
        return OtherFee;
    }

    public void setOtherFee(BigDecimal otherFee) {
        OtherFee = otherFee;
    }

    public BigDecimal getOtherSBGZF() {
        return OtherSBGZF;
    }

    public void setOtherSBGZF(BigDecimal otherSBGZF) {
        OtherSBGZF = otherSBGZF;
    }

    public BigDecimal getBuildArea() {
        return BuildArea;
    }

    public void setBuildArea(BigDecimal buildArea) {
        BuildArea = buildArea;
    }

    public String getDW() {
        return DW;
    }

    public void setDW(String DW) {
        this.DW = DW;
    }

    public BigDecimal getPrjSumPrice() {
        return PrjSumPrice;
    }

    public void setPrjSumPrice(BigDecimal prjSumPrice) {
        PrjSumPrice = prjSumPrice;
    }

    public BigDecimal getZGJHJ() {
        return ZGJHJ;
    }

    public void setZGJHJ(BigDecimal ZGJHJ) {
        this.ZGJHJ = ZGJHJ;
    }

    public BigDecimal getAQWMSGF() {
        return AQWMSGF;
    }

    public void setAQWMSGF(BigDecimal AQWMSGF) {
        this.AQWMSGF = AQWMSGF;
    }

    public BigDecimal getZLJE() {
        return ZLJE;
    }

    public void setZLJE(BigDecimal ZLJE) {
        this.ZLJE = ZLJE;
    }

    public BigDecimal getRGFHJ() {
        return RGFHJ;
    }

    public void setRGFHJ(BigDecimal RGFHJ) {
        this.RGFHJ = RGFHJ;
    }

    public BigDecimal getCLFHJ() {
        return CLFHJ;
    }

    public void setCLFHJ(BigDecimal CLFHJ) {
        this.CLFHJ = CLFHJ;
    }

    public BigDecimal getJXFHJ() {
        return JXFHJ;
    }

    public void setJXFHJ(BigDecimal JXFHJ) {
        this.JXFHJ = JXFHJ;
    }

    public BigDecimal getGLFHJ() {
        return GLFHJ;
    }

    public void setGLFHJ(BigDecimal GLFHJ) {
        this.GLFHJ = GLFHJ;
    }

    public BigDecimal getLRHJ() {
        return LRHJ;
    }

    public void setLRHJ(BigDecimal LRHJ) {
        this.LRHJ = LRHJ;
    }

    public String getEngineeringStatus() {
        return engineeringStatus;
    }

    public void setEngineeringStatus(String engineeringStatus) {
        this.engineeringStatus = engineeringStatus;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public Integer getOpentype() {
        return opentype;
    }

    public void setOpentype(Integer opentype) {
        this.opentype = opentype;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getsVerId() {
        return sVerId;
    }

    public void setsVerId(String sVerId) {
        this.sVerId = sVerId;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFileSavePath() {
        return fileSavePath;
    }

    public void setFileSavePath(String fileSavePath) {
        this.fileSavePath = fileSavePath;
    }

    public String getRootName() {
        return rootName;
    }

    public void setRootName(String rootName) {
        this.rootName = rootName;
    }

    public void addChild(CloudDocVM child) {
        this.children.add(child);
    }

    public Boolean getReplyOpinions() {
        return replyOpinions;
    }

    public void setReplyOpinions(Boolean replyOpinions) {
        this.replyOpinions = replyOpinions;
    }
}
