package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @author: 沈泽宇
 * @date: 2022年06月2022/6/21日
 */
public class JWTTokenVM {

    @ApiModelProperty(value = "登录返回的token")
    private String token;

    @ApiModelProperty(value = "用户ID")
    private String id;

    @ApiModelProperty(value = "账户名")
    private String accountName;

    @ApiModelProperty(value = "token到期时间")
    private Date expiration;

    @ApiModelProperty(value = "用户昵称")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "企业子账号才有，企业ID")
    private String iamTenantId;

    @ApiModelProperty(value = "最后登录的企业ID")
    private String lastLoginTenantId;

    @ApiModelProperty(value = "最后登录的企业名称")
    private String lastLoginTenantName;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Date getExpiration() {
        return expiration;
    }

    public void setExpiration(Date expiration) {
        this.expiration = expiration;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getIamTenantId() {
        return iamTenantId;
    }

    public void setIamTenantId(String iamTenantId) {
        this.iamTenantId = iamTenantId;
    }

    public String getLastLoginTenantId() {
        return lastLoginTenantId;
    }

    public void setLastLoginTenantId(String lastLoginTenantId) {
        this.lastLoginTenantId = lastLoginTenantId;
    }

    public String getLastLoginTenantName() {
        return lastLoginTenantName;
    }

    public void setLastLoginTenantName(String lastLoginTenantName) {
        this.lastLoginTenantName = lastLoginTenantName;
    }

}
