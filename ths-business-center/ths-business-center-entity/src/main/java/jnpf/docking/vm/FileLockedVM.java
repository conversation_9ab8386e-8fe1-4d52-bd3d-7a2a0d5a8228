package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

import java.time.Instant;
import java.util.List;

/**
 * 锁定/解锁返回信息
 * @author: 沈泽宇
 * @date: 2022年07月2022/7/22日
 */
public class FileLockedVM {

    @ApiModelProperty("MD5")
    private String checksum;

    @ApiModelProperty("是否锁定")
    private Boolean isLocked;

    @ApiModelProperty("锁定客户端ID")
    private String lockedClientId;

    @ApiModelProperty("锁定用户ID")
    private String lockedUserId;

    @ApiModelProperty("锁定用户姓名")
    private String lockedUserName;

    @ApiModelProperty("锁定时间")
    private Instant lockedDate;

    @ApiModelProperty("文件版本")
    private int docVersion;

    @ApiModelProperty("是否为强制解锁")
    private Boolean isForcedUnlocked;

    @ApiModelProperty(value = "锁定客户端名称")
    private String lockedClientName;

    @ApiModelProperty(value = "工程状态")
    private String editStatus;

    @ApiModelProperty(value = "工程状态名称")
    private String editStatusName;

    @ApiModelProperty(value = "当前流程用户ID")
    private List<String> currentUserId;

    @ApiModelProperty(value = "当前流程用户名称")
    private List<String> currentUserName;

    @ApiModelProperty(value = "下一流程状态集合")
    private List<ProjectAuditTypeVM> editStatusList;

    @ApiModelProperty(value = "是否允许编制,0:不允许，1：允许")
    private String allowEdit;

    @ApiModelProperty(value = "不允许编制原因")
    private String allowEditMessage;

    @ApiModelProperty(value = "是否允许选择人,0:不允许，1：允许")
    private String chooseUser;

    @ApiModelProperty(value = "是否流程结束,0:否，1：是")
    private String projectFinish;

    public String getLockedClientName() {
        return lockedClientName;
    }

    public void setLockedClientName(String lockedClientName) {
        this.lockedClientName = lockedClientName;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public Boolean getLocked() {
        return isLocked;
    }

    public void setLocked(Boolean locked) {
        isLocked = locked;
    }

    public String getLockedClientId() {
        return lockedClientId;
    }

    public void setLockedClientId(String lockedClientId) {
        this.lockedClientId = lockedClientId;
    }

    public String getLockedUserId() {
        return lockedUserId;
    }

    public void setLockedUserId(String lockedUserId) {
        this.lockedUserId = lockedUserId;
    }

    public String getLockedUserName() {
        return lockedUserName;
    }

    public void setLockedUserName(String lockedUserName) {
        this.lockedUserName = lockedUserName;
    }

    public Instant getLockedDate() {
        return lockedDate;
    }

    public void setLockedDate(Instant lockedDate) {
        this.lockedDate = lockedDate;
    }

    public int getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(int docVersion) {
        this.docVersion = docVersion;
    }

    public Boolean getForcedUnlocked() {
        return isForcedUnlocked;
    }

    public void setForcedUnlocked(Boolean forcedUnlocked) {
        isForcedUnlocked = forcedUnlocked;
    }

    public String getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(String editStatus) {
        this.editStatus = editStatus;
    }

    public String getEditStatusName() {
        return editStatusName;
    }

    public void setEditStatusName(String editStatusName) {
        this.editStatusName = editStatusName;
    }

    public List<String> getCurrentUserId() {
        return currentUserId;
    }

    public void setCurrentUserId(List<String> currentUserId) {
        this.currentUserId = currentUserId;
    }

    public List<String> getCurrentUserName() {
        return currentUserName;
    }

    public void setCurrentUserName(List<String> currentUserName) {
        this.currentUserName = currentUserName;
    }

    public List<ProjectAuditTypeVM> getEditStatusList() {
        return editStatusList;
    }

    public void setEditStatusList(List<ProjectAuditTypeVM> editStatusList) {
        this.editStatusList = editStatusList;
    }

    public String getAllowEdit() {
        return allowEdit;
    }

    public void setAllowEdit(String allowEdit) {
        this.allowEdit = allowEdit;
    }

    public String getChooseUser() {
        return chooseUser;
    }

    public void setChooseUser(String chooseUser) {
        this.chooseUser = chooseUser;
    }

    public String getProjectFinish() {
        return projectFinish;
    }

    public void setProjectFinish(String projectFinish) {
        this.projectFinish = projectFinish;
    }

    public String getAllowEditMessage() {
        return allowEditMessage;
    }

    public void setAllowEditMessage(String allowEditMessage) {
        this.allowEditMessage = allowEditMessage;
    }
}
