package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;


public class UploadFileVM {

    @ApiModelProperty(value = "文件",required = false)
    private String file;

    @ApiModelProperty(value = "文件类型",required = false)
    private String fileType;

    @ApiModelProperty(value = "当前级别文件ID",required = true)
    @NotBlank
    private String fileId;

    @ApiModelProperty(value = "父级ID，根级目录传'-1'",required = true)
    @NotBlank
    private String parentId;

    @ApiModelProperty(value = "文件版本号  新增文件传0(请求createOrAlterProjectDir接口后返回的版本号)",required = true)
    @NotBlank
    private String docVersion;

    @ApiModelProperty(value = "最父级工程ID",required = true)
    @NotBlank
    private String projectId;

    @ApiModelProperty(value = "checkSum",required = true)
    @NotBlank
    private String checkSum;

    @ApiModelProperty(value = "客户端ID",required = true)
    @NotBlank
    private String clientId;

    @ApiModelProperty(value = "客户端名称",required = true)
    @NotBlank
    private String clientName;

    @ApiModelProperty(value = "是否解锁  默认解锁",required = false)
    private String isUnlock;

    @ApiModelProperty(value = "工程状态",required = true)
    @NotBlank
    private String editStatus;

    @ApiModelProperty(value = "备注",required = false)
    private String remark;

    @ApiModelProperty(value = "下一流程责任人ID",required = false)
    private String nextUserId;

    @ApiModelProperty(value = "忽略锁定上传",required = false)
    private String ignoreLock;

    @ApiModelProperty(value = "总造价金额",required = false)
    private String totalMoney;

    @ApiModelProperty(value = "送审造价- 原始送审文件",required = false)
    private String sendMoney;
    @ApiModelProperty(value = "审批意见",required = false)
    private String reviewComments;

    @ApiModelProperty(value = "信息价日期",required = false)
    private String informationPriceDate;

    @ApiModelProperty(value = "单位工程文件中的工程名称",required = false)
    private String projectName;

    @ApiModelProperty(value = "审核造价 审核数据的总造价",required = false)
    private String auditMoney;


    @ApiModelProperty(value = "单位工程单位",required = false)
    private String dw;


    @ApiModelProperty(value = "建设规模",required = false)
    private String buildArea;


    public String getBuildArea() {
        return buildArea;
    }

    public void setBuildArea(String buildArea) {
        this.buildArea = buildArea;
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
    }



    public String getAuditMoney() {
        return auditMoney;
    }

    public void setAuditMoney(String auditMoney) {
        this.auditMoney = auditMoney;
    }

    public Integer getAuditRole() {
        return auditRole;
    }

    public void setAuditRole(Integer auditRole) {
        this.auditRole = auditRole;
    }

    public String getRelationalFile() {
        return relationalFile;
    }

    public void setRelationalFile(String relationalFile) {
        this.relationalFile = relationalFile;
    }

    public String getRelationalFileHash() {
        return relationalFileHash;
    }

    public void setRelationalFileHash(String relationalFileHash) {
        this.relationalFileHash = relationalFileHash;
    }

    public Integer getRelationalFileVersion() {
        return relationalFileVersion;
    }

    public void setRelationalFileVersion(Integer relationalFileVersion) {
        this.relationalFileVersion = relationalFileVersion;
    }

    public String getAuditFile() {
        return auditFile;
    }

    public void setAuditFile(String auditFile) {
        this.auditFile = auditFile;
    }

    public String getAuditFileHash() {
        return auditFileHash;
    }

    public void setAuditFileHash(String auditFileHash) {
        this.auditFileHash = auditFileHash;
    }

    public String getAuditFileVersion() {
        return auditFileVersion;
    }

    public void setAuditFileVersion(String auditFileVersion) {
        this.auditFileVersion = auditFileVersion;
    }

    public String getOriginalSendFile() {
        return originalSendFile;
    }

    public void setOriginalSendFile(String originalSendFile) {
        this.originalSendFile = originalSendFile;
    }

    public String getOriginalSendFileHash() {
        return originalSendFileHash;
    }

    public void setOriginalSendFileHash(String originalSendFileHash) {
        this.originalSendFileHash = originalSendFileHash;
    }

    public String getOriginalSendFileVersion() {
        return originalSendFileVersion;
    }

    public void setOriginalSendFileVersion(String originalSendFileVersion) {
        this.originalSendFileVersion = originalSendFileVersion;
    }

    @ApiModelProperty(value = "0:编制文件 ；1：审核文件",required = false)
    private Integer auditRole;


    @ApiModelProperty(value = "关系文件",required = false)
    private String relationalFile;

    @ApiModelProperty(value = "relationalFileHash关系文件校验 MD5 ",required = true)
    @NotBlank
    private String relationalFileHash;

    @ApiModelProperty(value = "关系文件版本",required = true)
    @NotBlank
    private Integer relationalFileVersion;

    @ApiModelProperty(value = "审核文件",required = false)
    private String auditFile;

    @ApiModelProperty(value = "审核文件 MD5 ",required = true)
    @NotBlank
    private String auditFileHash;

    @ApiModelProperty(value = "审核文件版本",required = true)
    @NotBlank
    private String auditFileVersion;

    @ApiModelProperty(value = "原始送审文件流",required = false)
    private String originalSendFile;

    @ApiModelProperty(value = "原始送审校验 MD5 ",required = true)
    @NotBlank
    private String originalSendFileHash;

    @ApiModelProperty(value = "原始送审版本",required = true)
    @NotBlank
    private String originalSendFileVersion;







    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(String docVersion) {
        this.docVersion = docVersion;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getCheckSum() {
        return checkSum;
    }

    public void setCheckSum(String checkSum) {
        this.checkSum = checkSum;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getIsUnlock() {
        return isUnlock;
    }

    public void setIsUnlock(String isUnlock) {
        this.isUnlock = isUnlock;
    }

    public String getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(String editStatus) {
        this.editStatus = editStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNextUserId() {
        return nextUserId;
    }

    public void setNextUserId(String nextUserId) {
        this.nextUserId = nextUserId;
    }

    public String getIgnoreLock() {
        return ignoreLock;
    }

    public void setIgnoreLock(String ignoreLock) {
        this.ignoreLock = ignoreLock;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

    public String getReviewComments() {
        return reviewComments;
    }

    public void setReviewComments(String reviewComments) {
        this.reviewComments = reviewComments;
    }

    public String getInformationPriceDate() {
        return informationPriceDate;
    }

    public void setInformationPriceDate(String informationPriceDate) {
        this.informationPriceDate = informationPriceDate;
    }
    public String getSendMoney() {
        return sendMoney;
    }

    public void setSendMoney(String sendMoney) {
        this.sendMoney = sendMoney;
    }

}
