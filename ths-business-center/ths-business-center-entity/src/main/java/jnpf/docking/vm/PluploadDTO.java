package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;


public class PluploadDTO {
    /**文件临时名(打文件被分解时)或原名*/
    private String name;
    /**文件描述*/
    private String fileDesc;
    /**文件外键*/
    private String fileFk;
    /**总的块数*/
    private int chunks = 1;
    /**当前块数（从0开始计数）*/
    private int chunk = 0;
    /**HttpServletRequest对象，不能直接传入进来，需要手动传入*/
    private HttpServletRequest request;
    /**文件大小，无需传入*/
    @ApiModelProperty(hidden = true)
    private long size;
    /**保存文件上传信息，不能直接传入进来，需要手动传入*/
    private MultipartFile multipartFile;
    /**扩展字段1*/
    private String uploader;
    /**扩展字段1*/
    private String extField1;
    /**扩展字段2*/
    private String extField2;
    /**扩展字段3*/
    private String extField3;
    /**扩展字段4*/
    private String extField4;
    /**扩展字段5*/
    private String extField5;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 不用传参
     */
    private String fileHash;

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getFileDesc() {
        return fileDesc;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getFileFk() {
        return fileFk;
    }

    public void setFileFk(String fileFk) {
        this.fileFk = fileFk;
    }
    public int getChunks() {
        return chunks;
    }

    public void setChunks(int chunks) {
        this.chunks = chunks;
    }

    public int getChunk() {
        return chunk;
    }

    public void setChunk(int chunk) {
        this.chunk = chunk;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public MultipartFile getMultipartFile() {
        return multipartFile;
    }

    public void setMultipartFile(MultipartFile multipartFile) {
        this.multipartFile = multipartFile;
    }

    public String getUploader() {
        return uploader;
    }

    public void setUploader(String uploader) {
        this.uploader = uploader;
    }

    public String getExtField1() {
        return extField1;
    }

    public void setExtField1(String extField1) {
        this.extField1 = extField1;
    }

    public String getExtField2() {
        return extField2;
    }

    public void setExtField2(String extField2) {
        this.extField2 = extField2;
    }

    public String getExtField3() {
        return extField3;
    }

    public void setExtField3(String extField3) {
        this.extField3 = extField3;
    }

    public String getExtField4() {
        return extField4;
    }

    public void setExtField4(String extField4) {
        this.extField4 = extField4;
    }

    public String getExtField5() {
        return extField5;
    }

    public void setExtField5(String extField5) {
        this.extField5 = extField5;
    }
}
