package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;


/**
 * @author: 沈泽宇
 * @date: 2022年06月2022/6/29日
 */
public class OperationFileLockVM {

    @ApiModelProperty(value = "具体的文件ID  必填",required = true)
    @NotBlank
    private String fileId;

    @ApiModelProperty(value = "工程ID 必填",required = true)
    @NotBlank
    private String projectId;

    @ApiModelProperty(value = "是否锁定 操作动作  必填",required = true)
    @NotNull
    private Boolean isLocked;

    @ApiModelProperty(value = "锁定/解锁 客户端ID  必填",required = true)
    @NotBlank
    private String lockedClientId;

    @ApiModelProperty(value = "客户端的文件版本 必填",required = true)
    private int docVersion;

    @ApiModelProperty(value = "客户端名称 最长50个字符")
    @Size(max = 50)
    private String lockedClientName;

    @ApiModelProperty(value = "工程状态",required = false)
    private String editStatus;

    @ApiModelProperty(value = "意见",required = false)
    private String remark;

    public String getLockedClientName() {
        return lockedClientName;
    }

    public void setLockedClientName(String lockedClientName) {
        this.lockedClientName = lockedClientName;
    }

    public String getLockedClientId() {
        return lockedClientId;
    }

    public void setLockedClientId(String lockedClientId) {
        this.lockedClientId = lockedClientId;
    }

    public int getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(int docVersion) {
        this.docVersion = docVersion;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Boolean islocked) {
        this.isLocked = islocked;
    }

    public String getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(String editStatus) {
        this.editStatus = editStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
