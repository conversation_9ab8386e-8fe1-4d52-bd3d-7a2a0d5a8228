package jnpf.docking.vm;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;


public class ThsAiVM {

    @ApiModelProperty(value = "命令",required = true)
    @NotBlank
    private String instructions;

    @ApiModelProperty(value = "工程id",required = true)
    @JsonProperty(value="tree_id")
    @NotBlank
    private String tree_id;

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public String getTree_id() {
        return tree_id;
    }

    public void setTree_id(String tree_id) {
        this.tree_id = tree_id;
    }
}
