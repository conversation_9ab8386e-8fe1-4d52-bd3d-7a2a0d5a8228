package jnpf.docking.vm;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;

public class BASE64DecodedMultipartFile implements MultipartFile {

    private final byte[] imgContent;
    private final String header;
    private final String fileType; // 新增字段，用于指定文件后缀类型


    public BASE64DecodedMultipartFile(byte[] imgContent, String header) {
        this.imgContent = imgContent;
        this.header = header.split(";")[0];
        this.fileType = null;
    }

    /**
     * 新增构造函数，允许指定文件类型
     */
    public BASE64DecodedMultipartFile(byte[] imgContent, String header, String fileType) {
        this.imgContent = imgContent;
        this.header = header.split(";")[0];
        this.fileType = fileType != null ? fileType.toLowerCase() : parseFileType(header); // 优先使用指定的文件类型
    }


    /**
     * 解析文件类型
     *
     * @param header Base64 的头部信息
     * @return 文件类型，例如 "png"
     */
    private String parseFileType(String header) {
        if (header == null || !header.contains("/")) {
            throw new IllegalArgumentException("Invalid header format");
        }
        return header.split("/")[1].split(";")[0];
    }

    @Override
    public String getName() {
        // TODO - implementation depends on your requirements
        return System.currentTimeMillis() + Math.random() + "." + header.split("/")[1];
    }

    @Override
    public String getOriginalFilename() {
        // TODO - implementation depends on your requirements
        return System.currentTimeMillis() + (int) Math.random() * 10000 + "." + header.split("/")[1];
    }

    @Override
    public String getContentType() {
        // TODO - implementation depends on your requirements
        return header.split(":")[1];
    }

    @Override
    public boolean isEmpty() {
        return imgContent == null || imgContent.length == 0;
    }

    @Override
    public long getSize() {
        return imgContent.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return imgContent;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(imgContent);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        new FileOutputStream(dest).write(imgContent);
    }
}
