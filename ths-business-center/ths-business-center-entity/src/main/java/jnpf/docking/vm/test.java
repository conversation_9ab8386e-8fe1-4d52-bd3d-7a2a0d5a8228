package jnpf.docking.vm;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * @auther wxy
 * @date 2024-08-30 09:48
 */
public class test {

    public static String doubleMd5Encrypt(String input) {
        // First MD5 encryption
        String firstMd5 = DigestUtils.md5Hex(input);

        // Second MD5 encryption using the result of the first encryption
        return DigestUtils.md5Hex(firstMd5);
    }

    public static void main(String[] args) {
        String plainText = "SZCCBpassword666*";
        String encryptedText = doubleMd5Encrypt(plainText);
        System.out.println("Original Text: " + plainText);
        System.out.println("Double MD5 Encrypted Text: " + encryptedText);
    }
}
