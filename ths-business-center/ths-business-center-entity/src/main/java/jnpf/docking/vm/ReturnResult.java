package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

public class ReturnResult<T> {

    @ApiModelProperty(value = "状态说明/异常说明")
    private String message;

    @ApiModelProperty(value = "状态码，非业务相关，预留")
    private String code;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "返回的结果正文")
    private T data;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
