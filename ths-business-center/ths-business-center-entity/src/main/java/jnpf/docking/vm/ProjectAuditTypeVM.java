package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ProjectAuditTypeVM {

    @ApiModelProperty(value = "状态名称")
    private String name;

    @ApiModelProperty(value = "状态值")
    private String value;

    @ApiModelProperty(value = "所属用户集合")
    private List<PersonVM> userList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<PersonVM> getUserList() {
        return userList;
    }

    public void setUserList(List<PersonVM> userList) {
        this.userList = userList;
    }

    public ProjectAuditTypeVM(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public ProjectAuditTypeVM() {
    }
}
