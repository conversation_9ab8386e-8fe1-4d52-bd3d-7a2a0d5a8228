package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

/**编审模式计价交互 原始送审文件信息
 * @auther wxy
 * @date 2024-08-28 17:15
 */
public class CloudOriginalSendVM {
    private String id;
    @ApiModelProperty("第三份文件名称")
    private String Name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getPhaseName() {
        return phaseName;
    }

    public void setPhaseName(String phaseName) {
        this.phaseName = phaseName;
    }

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public Integer getFileVersion() {
        return fileVersion;
    }

    public void setFileVersion(Integer fileVersion) {
        this.fileVersion = fileVersion;
    }

    @ApiModelProperty("阶段名称")
    private String phaseName;

    @ApiModelProperty("md5  客户端使用，云端仅保存")
    private String fileHash;

    @ApiModelProperty(value = "文件版本")
    private Integer fileVersion;
}
