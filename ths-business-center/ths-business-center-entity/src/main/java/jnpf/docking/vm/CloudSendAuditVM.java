package jnpf.docking.vm;

import io.swagger.annotations.ApiModelProperty;

/** 编审模式计价交互 送审审核文件信息
 * @auther wxy
 * @date 2024-08-28 17:14
 */

public class CloudSendAuditVM {
    private String id;

    @ApiModelProperty("阶段名称")
    private String phaseName;

    @ApiModelProperty("md5  客户端使用，云端仅保存")
    private String fileHash;

    @ApiModelProperty(value = "文件版本")
    private Integer fileVersion;

    @ApiModelProperty(value = "关系文件ID")
    private String relationalFileId;

    @ApiModelProperty(" 关系文件md5  客户端使用，云端仅保存")
    private String relationalFileIdHash;

    @ApiModelProperty(value = "关系文件文件版本")
    private Integer relationalFileVersion;

    @ApiModelProperty(value = "是否交换")
    private boolean swap;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPhaseName() {
        return phaseName;
    }

    public void setPhaseName(String phaseName) {
        this.phaseName = phaseName;
    }

    public String getFileHash() {
        return fileHash;
    }

    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }

    public Integer getFileVersion() {
        return fileVersion;
    }

    public void setFileVersion(Integer fileVersion) {
        this.fileVersion = fileVersion;
    }

    public String getRelationalFileId() {
        return relationalFileId;
    }

    public void setRelationalFileId(String relationalFileId) {
        this.relationalFileId = relationalFileId;
    }

    public String getRelationalFileIdHash() {
        return relationalFileIdHash;
    }

    public void setRelationalFileIdHash(String relationalFileIdHash) {
        this.relationalFileIdHash = relationalFileIdHash;
    }

    public Integer getRelationalFileVersion() {
        return relationalFileVersion;
    }

    public void setRelationalFileVersion(Integer relationalFileVersion) {
        this.relationalFileVersion = relationalFileVersion;
    }

    public boolean isSwap() {
        return swap;
    }

    public void setSwap(boolean swap) {
        this.swap = swap;
    }
}
