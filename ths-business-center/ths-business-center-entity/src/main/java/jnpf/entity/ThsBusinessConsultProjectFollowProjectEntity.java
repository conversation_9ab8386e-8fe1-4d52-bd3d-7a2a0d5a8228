package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_consult_project_follow_project")
public class ThsBusinessConsultProjectFollowProjectEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "CONSULT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultId;
    @TableField(value = "BUSINESS_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String businessType;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "MODIFIER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String modifierId;
    @TableField(value = "MODIFY_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date modifyTime;
}  
