package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: xl
 * @description: 合同审批历史信息表
 * @date: 2024/4/10 14:04
 */
@Data
@TableName("ths_manage_contract_status_history")
public class ThsManageContractStatusHistoryEntity {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value ="CONTRACT_ID"  )
    private String contractId;
    @TableField(value ="STATUS"  )
    private String status;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("REMARK")
    private String remark;
}
