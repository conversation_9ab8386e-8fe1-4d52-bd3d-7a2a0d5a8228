package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 一表通-系统模板-数据源字段名表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-08-08
 */
@Data
@TableName("ths_template_report_category_file_source_field")
public class ThsTemplateReportCategoryFileSourceFieldEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "CATEGORY_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String categoryId;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "SOURCE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String sourceId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("F_FLOWID")
    private String flowid;
}
