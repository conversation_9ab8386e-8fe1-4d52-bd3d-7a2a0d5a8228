package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 询价材料品牌表
 *
 * @版本： V5.0.0
 * @版权： Copyright @ 2024  引迈信息技术有限公司版权所有
 * @作者： 引迈信息技术有限公司
 * @日期： 2024-10-30
 */
@Data
@TableName("ths_archive_inquiry_material_quotation")
public class ThsArchiveInquiryMaterialQuotationEntity {

    @TableId(value = "id")
    private String id;
    @TableField(value = "project_id", updateStrategy = FieldStrategy.IGNORED)
    private String projectId;
    @TableField("material_id")
    private String materialId;
    @TableField("brand")
    private String brand;
    @TableField("quotation")
    private BigDecimal quotation;
    @TableField("quotation_date")
    private Date quotationDate;
    @TableField("quotation_unit_name")
    private String quotationUnitName;
    @TableField("quotation_person")
    private String quotationPerson;
    @TableField("contact_information")
    private String contactInformation;
    @TableField("remark")
    private String remark;
    @TableField("creator_id")
    private String creatorId;
    @TableField("create_time")
    private Date createTime;
    @TableField("sequence")
    private Integer sequence;

    @TableField("item_source")
    private Integer itemSource;
}
