package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_budget_project_approval_opinions")
public class ThsBusinessBudgetProjectApprovalOpinionsEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID", updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID", updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "IID", updateStrategy = FieldStrategy.IGNORED)
    private Integer iid;
    @TableField(value = "CODE", updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME", updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "OPINION_TYPE", updateStrategy = FieldStrategy.IGNORED)
    private String opinionType;
    @TableField(value = "OPINION_REMARK", updateStrategy = FieldStrategy.IGNORED)
    private String opinionRemark;
    @TableField(value = "OPINION_ID", updateStrategy = FieldStrategy.IGNORED)
    private String opinionId;
    @TableField(value = "OPINION_TIME", updateStrategy = FieldStrategy.IGNORED)
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opinionTime;
    @TableField(value = "CUR_FLOW_DETAIL_ID", updateStrategy = FieldStrategy.IGNORED)
    private String curFlowDetailId;
    @TableField(value = "SEQUENCE", updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;

    /**
     * 回复意见
     */
    @TableField(value = "REPLY_REMARK", updateStrategy = FieldStrategy.IGNORED)
    private String replyRemark;

    /**
     * 回复人ID
     */
    @TableField(value = "REPLY_ID", updateStrategy = FieldStrategy.IGNORED)
    private String replyId;

    /**
     * 回复时间
     */
    @TableField(value = "REPLY_TIME", updateStrategy = FieldStrategy.IGNORED)
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyTime;
}  
