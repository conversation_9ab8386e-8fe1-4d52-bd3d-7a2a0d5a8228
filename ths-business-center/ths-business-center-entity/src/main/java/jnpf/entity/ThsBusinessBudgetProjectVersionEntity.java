package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;
/**
 * 协同计价版本管理/计价模板管理
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-05
 */
@Data
@TableName("ths_business_budget_project_version")
public class ThsBusinessBudgetProjectVersionEntity  {
    public static final int TEMPLATE_CASE_PROJECT = 0;
    public static final int TEMPLATE_CASE_TEMPLATE = 1;

    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "PID" , updateStrategy = FieldStrategy.IGNORED)
    private String pid;
    @TableField(value = "DEPT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String deptId;
    @TableField(value = "CONSULT_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultProjectId;
    @TableField(value = "SYS_FLOW_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String sysFlowId;
    @TableField(value = "STANDARD_LIST_VER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String standardListVerId;
    @TableField(value = "STANDARD_MATERIA_VER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String standardMateriaVerId;
    @TableField(value = "USE_TEMPLATE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String useTemplateId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "OBLIG_USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String obligUserId;
    @TableField(value = "VER" , updateStrategy = FieldStrategy.IGNORED)
    private Integer ver;
    @TableField(value = "TEMPLATE_CASE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer templateCase;

    @Schema(description = "状态  0：编制中 1：编制完成 2：审批中 3：审批完成")
    @TableField(value = "STATUS" , updateStrategy = FieldStrategy.IGNORED)
    private Integer status;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "LAST_MODIFY_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date lastModifyTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "IS_FINALIZE" , updateStrategy = FieldStrategy.IGNORED)
    private Boolean isFinalize;
    @TableField(value = "ISSUER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String issuerId;
    @TableField(value = "ISSUE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date issueTime;
    @TableField(value = "S_VER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String sVerId;
    @TableField(value = "AREA_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String areaId;
    @TableField(value = "TEMPLATE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String templateId;
    @TableField(value = "CATEGORY_DICT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String categoryDictId;
    @TableField(value = "TENANT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String tenantId;
    @TableField("MODIFIER")
    private String modifier;

    @TableField(value = "INDICATORS_ARCHIVE_STATUS" , updateStrategy = FieldStrategy.IGNORED)
    private Integer indicatorsArchiveStatus;

    @TableField(value = "CASE_ARCHIVE_STATUS" , updateStrategy = FieldStrategy.IGNORED)
    private Integer CaseArchiveStatus;

    @TableField(value = "EDITORIAL_TYPE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String editorialTypeId;
    @TableField(value = "THSFILE_FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String thsfileFileId;

//    @TableField("F_FLOWID")
//    private String flowid;
}
