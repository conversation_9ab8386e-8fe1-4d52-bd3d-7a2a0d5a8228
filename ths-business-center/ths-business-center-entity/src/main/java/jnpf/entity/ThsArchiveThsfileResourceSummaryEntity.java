package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * (工程价)工料机汇总
 *
 * @版本： V5.0.0
 * @版权： ©2011- 2023 深圳市斯维尔科技股份有限公司 版权所有
 * @作者： 深圳市斯维尔科技股份有限公司
 * @日期： 2024-11-19
 */
@Data
@TableName("ths_archive_thsfile_resource_summary")
public class ThsArchiveThsfileResourceSummaryEntity  {
    @TableId(value ="id"  )
    @JSONField(name = "id")
    private String id;
    @TableField("code")
    @JSONField(name = "code")
    private String code;
    @TableField("name")
    @JSONField(name = "name")
    private String name;
    @TableField(value = "SPEC" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "spec")
    private String spec;
    @TableField(value = "item_unit" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "item_unit")
    private String itemUnit;
    @TableField(value = "quantity" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "quantity")
    private BigDecimal quantity;
    @TableField(value = "tax_unit_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "tax_unit_price")
    private BigDecimal taxUnitPrice;
    @TableField(value = "tax_rate" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "tax_rate")
    private BigDecimal taxRate;
    @TableField(value = "unit_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "unit_price")
    private BigDecimal unitPrice;
    @TableField(value = "de_unit_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "de_unit_price")
    private BigDecimal deUnitPrice;
    @TableField(value = "total_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "total_price")
    private BigDecimal totalPrice;
    @TableField(value = "cost_kind" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "cost_kind")
    private Integer costKind;
    @TableField("producing_area")
    @JSONField(name = "producing_area")
    private String producingArea;
    @TableField("supplier")
    @JSONField(name = "supplier")
    private String supplier;
    @TableField(value = "provider" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "provider")
    private Integer provider;
    @TableField(value = "auto_adjust_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "auto_adjust_price")
    private Integer autoAdjustPrice;
    @TableField(value = "remark" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "remark")
    private String remark;
    @TableField("sequence")
    @JSONField(name = "sequence")
    private Integer sequence;
    @TableField(value = "item_source" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "item_source")
    private Integer itemSource;
    @TableField(value = "project_id" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "project_id")
    private String projectId;

    @TableField("information_price_file_name")
    @JSONField(name = "information_price_file_name")
    private String informationPriceFileName;
}
