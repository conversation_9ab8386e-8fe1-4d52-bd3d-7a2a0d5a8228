package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 材料分类-属性表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-08-05
 */
@Data
@TableName("ths_template_report_category_file_source")
public class ThsTemplateReportCategoryFileSourceEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "CATEGORY_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String categoryId;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "SQL_TEXT" , updateStrategy = FieldStrategy.IGNORED)
    private String sqlText;
    @TableField(value = "API_FUNC_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String apiFuncName;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField(value = "DATA_SOURCE_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer dataSourceType;
    @TableField(value = "QUERY_METHOD" , updateStrategy = FieldStrategy.IGNORED)
    private Integer queryMethod;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("F_FLOWID")
    private String flowid;
}
