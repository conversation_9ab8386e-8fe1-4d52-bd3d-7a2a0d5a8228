package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
/**
 * 成果文件管理
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-19
 */
@Data
@TableName("ths_business_consult_achievement_file")
public class ThsBusinessConsultAchievementFileEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("ACHIEVEMENT_ID")
    private String achievementId;
    @TableField("CONSULT_ID")
    private String consultId;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("LAST_MODIFY_TIME")
    private Date lastModifyTime;
    @TableField("TENANT_ID")
    private String tenantId;
    @TableField("UPLOADER")
    private String uploader;
    @TableField(value = "DATA_TYPE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String dataTypeId;
    @TableField(value = "FILE_NUMBER" , updateStrategy = FieldStrategy.IGNORED)
    private Integer fileNumber;
    @TableField(value = "AUDIT_MONEY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal auditMoney;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
}
