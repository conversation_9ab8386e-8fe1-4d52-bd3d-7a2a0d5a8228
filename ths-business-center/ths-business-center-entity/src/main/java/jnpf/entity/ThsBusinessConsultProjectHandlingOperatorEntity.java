package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("ths_business_consult_project_handling_operator")
public class ThsBusinessConsultProjectHandlingOperatorEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String projectId;
    @TableField(value = "ORGANIZE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String organizeId;
    @TableField(value = "OPERATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String operatorId;
    @TableField(value = "OPERATOR_CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String operatorCode;
    @TableField(value = "OPERATOR_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String operatorName;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
}  
