package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 单价分析
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-01-12
 */
@Data
@TableName("thsfile_qdy_measure_price_analysis")
public class ThsfileQdyMeasurePriceAnalysisEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "PID" , updateStrategy = FieldStrategy.IGNORED)
    private String pid;
    @TableField(value = "IID" , updateStrategy = FieldStrategy.IGNORED)
    private Integer iid;
    @TableField(value = "IPID" , updateStrategy = FieldStrategy.IGNORED)
    private Integer ipid;
    @TableField(value = "XMJJ_ID" , updateStrategy = FieldStrategy.IGNORED)
    private Integer xmjjId;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "QDG_STRUCTURE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String qdgStructureId;
    @TableField(value = "QDY_MEASURE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String qdyMeasureId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "COST_BASE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal costBase;
    @TableField(value = "COST_RATE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal costRate;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "FEEEXPR" , updateStrategy = FieldStrategy.IGNORED)
    private String feeexpr;
    @TableField("F_FLOWID")
    private String flowid;
}
