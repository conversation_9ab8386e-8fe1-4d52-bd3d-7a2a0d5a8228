package jnpf.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 询价材料附件关系表
 *
 * @版本： V5.0.0
 * @版权： Copyright @ 2024  引迈信息技术有限公司版权所有
 * @作者： 引迈信息技术有限公司
 * @日期： 2024-10-30
 */
@Data
@TableName("ths_archive_material_annex_relation")
public class ThsArchiveMaterialAnnexRelationEntity {

    @TableId(value = "id")
    @JSONField(name = "id")
    private String id;
    @TableField(value = "project_id", updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "project_id")
    private String projectId;
    @TableField("material_id")
    @JSONField(name = "material_id")
    private String materialId;
    @TableField("annex_id")
    @JSONField(name = "annex_id")
    private String annexId;
    @TableField("relation_type")
    @JSONField(name = "relation_type")
    private Integer relationType;
}
