package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 工程造价编制 - 客户端机器表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-06
 */
@Data
@TableName("ths_business_budget_project_machine")
public class ThsBusinessBudgetProjectMachineEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "MACHINE_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String machineName;
    @TableField(value = "MACHINE_CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String machineCode;
    @TableField(value = "MACHINE_USER" , updateStrategy = FieldStrategy.IGNORED)
    private String machineUser;
    @TableField(value = "CREATOR" , updateStrategy = FieldStrategy.IGNORED)
    private String creator;
    @TableField(value = "CREATION_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date creationTime;
    @TableField(value = "MODIFIER" , updateStrategy = FieldStrategy.IGNORED)
    private String modifier;
    @TableField(value = "MODIFIED_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date modifiedTime;
    @TableField(value = "IS_DELETE" , updateStrategy = FieldStrategy.IGNORED)
    private Boolean isDelete;
//    @TableField("F_FLOWID")
//    private String flowid;
}
