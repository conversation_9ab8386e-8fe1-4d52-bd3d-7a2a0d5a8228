package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_consult_project_log_management")
public class ThsBusinessConsultProjectLogManagementEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "USER_NAME",updateStrategy = FieldStrategy.IGNORED)
    private String userName;
    @TableField(value = "MODEL_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer modelType;
    @TableField(value = "MODEL_RECORD_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String modelRecordId;
    @TableField(value = "OPERATIONAL_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String operationalType;
    @TableField(value = "OPERATIONAL_INFORMATION", updateStrategy = FieldStrategy.IGNORED)
    private String operationalInformation;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
}  
