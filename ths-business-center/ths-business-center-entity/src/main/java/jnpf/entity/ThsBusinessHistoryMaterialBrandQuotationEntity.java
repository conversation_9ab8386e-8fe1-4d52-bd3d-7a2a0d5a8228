package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
/**
 * 材料品牌管理表
 *
 * @版本： V5.0.0
 * @版权： ©2011- 2023 深圳市斯维尔科技股份有限公司 版权所有
 * @作者： 深圳市斯维尔科技股份有限公司
 * @日期： 2024-11-19
 */
@Data
@TableName("ths_business_history_material_brand_quotation")
public class ThsBusinessHistoryMaterialBrandQuotationEntity  {
    @TableId(value ="id"  )
    @JSONField(name = "id")
    private String id;
    @TableField(value = "material_id" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "material_id")
    private String materialId;
    @TableField(value = "brand" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "brand")
    private String brand;
    @TableField(value = "company" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "company")
    private String company;
    @TableField(value = "contact_person" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "contact_person")
    private String contactPerson;
    @TableField(value = "contact_phone" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "contact_phone")
    private String contactPhone;
    @TableField(value = "unit_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "unit_price")
    private BigDecimal unitPrice;
    @TableField(value = "creator_id" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "creator_id")
    private String creatorId;
    @TableField(value = "quoted_time" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "quoted_time")
    private Date quotedtime;
    @TableField(value = "create_time" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "create_time")
    private Date createTime;
    @TableField(value = "remark" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "remark")
    private String remark;
}
