package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_budget_project_detail_report_serial_number")
public class ThsBusinessBudgetProjectDetailReportSerialNumberEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "REPORT_ID", updateStrategy = FieldStrategy.IGNORED)
    private String reportId;
    @TableField(value = "SERIAL_NUMBER", updateStrategy = FieldStrategy.IGNORED)
    private Integer serialNumber;
    @TableField(value = "TYPE", updateStrategy = FieldStrategy.IGNORED)
    private Integer type;
    @TableField(value = "USER_ID", updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "USER_NAME", updateStrategy = FieldStrategy.IGNORED)
    private String userName;
    @TableField(value = "PRINT_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date printTime;
    @TableField(value = "ADD_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date addTime;
    @TableField(value = "LAST_EDIT_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date lastEditTime;
}  
