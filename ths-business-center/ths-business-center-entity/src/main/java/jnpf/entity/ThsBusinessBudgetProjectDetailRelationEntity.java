package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_budget_project_detail_relation")
public class ThsBusinessBudgetProjectDetailRelationEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "RELATION_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String relationId;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "CREATOR" , updateStrategy = FieldStrategy.IGNORED)
    private String creator;
    @TableField(value = "CREATION_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date creationTime;
}  
