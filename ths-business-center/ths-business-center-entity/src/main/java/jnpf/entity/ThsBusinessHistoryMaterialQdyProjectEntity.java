package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
/**
 * 材料qdy项目管理
 *
 * @版本： V5.0.0
 * @版权： ©2011- 2023 深圳市斯维尔科技股份有限公司 版权所有
 * @作者： 深圳市斯维尔科技股份有限公司
 * @日期： 2024-11-13
 */
@Data
@TableName("ths_business_history_material_qdy_project")
public class ThsBusinessHistoryMaterialQdyProjectEntity  {
    @TableId(value ="id"  )
    @JSONField(name = "id")
    private String id;
    @TableField(value = "contract_code" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "contract_code")
    private String contractCode;
    @TableField(value = "project_name" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "project_name")
    private String projectName;
    @TableField("file_name")
    @JSONField(name = "file_name")
    private String fileName;
    @TableField("remark")
    @JSONField(name = "remark")
    private String remark;
    @TableField("sequence")
    @JSONField(name = "sequence")
    private Integer sequence;
    @TableField("creator_id")
    @JSONField(name = "creator_id")
    private String creatorId;
    @TableField(value = "create_time" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "create_time")
    private Date createTime;
    @TableField("thsfile_id")
    @JSONField(name = "thsfile_id")
    private String thsfileId;

    @TableField("file_id")
    @JSONField(name = "file_id")
    private String fileId;

    @TableField("delete_flag")
    @JSONField(name = "delete_flag")
    private Integer deleteFlag;
}
