package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 工料机组成
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-11-16
 */
@Data
@TableName("thsfile_qdy_measure_resource_component")
public class ThsfileQdyMeasureResourceComponentEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "IID" , updateStrategy = FieldStrategy.IGNORED)
    private Integer iid;
    @TableField(value = "XMJJ_ID" , updateStrategy = FieldStrategy.IGNORED)
    private Integer xmjjId;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "QDG_STRUCTURE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String qdgStructureId;
    @TableField(value = "QDY_MEASURE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String qdyMeasureId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "SPEC" , updateStrategy = FieldStrategy.IGNORED)
    private String spec;
    @TableField(value = "ITEM_UNIT" , updateStrategy = FieldStrategy.IGNORED)
    private String itemUnit;
    @TableField(value = "QUANTITY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quantity;
    @TableField(value = "TAX_UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxUnitPrice;
    @TableField(value = "TAX_RATE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxRate;
    @TableField(value = "UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal unitPrice;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;
    @TableField(value = "COST_KIND" , updateStrategy = FieldStrategy.IGNORED)
    private Integer costKind;
    @TableField(value = "PRODUCTION_AREA" , updateStrategy = FieldStrategy.IGNORED)
    private String productionArea;
    @TableField(value = "SUPPLIER" , updateStrategy = FieldStrategy.IGNORED)
    private String supplier;
    @TableField(value = "PROVIDER" , updateStrategy = FieldStrategy.IGNORED)
    private Integer provider;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField(value = "DE_UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal deUnitPrice;
    @TableField(value = "QUANTITY_BASE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quantityBase;
    @TableField(value = "QUANTITY_REAL" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quantityReal;
}
