package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_budget_project_audit_remark")
public class ThsBusinessBudgetProjectAuditRemarkEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "TABLE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String tableId;
    @TableField(value = "KEY_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String keyId;
    @TableField(value = "USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "MESSAGE" , updateStrategy = FieldStrategy.IGNORED)
    private String message;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
}  
