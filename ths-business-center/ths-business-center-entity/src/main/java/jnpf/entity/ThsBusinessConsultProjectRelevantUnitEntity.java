package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 项目参与方
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-08-16
 */
@Data
@TableName("ths_business_consult_project_relevant_unit")
public class ThsBusinessConsultProjectRelevantUnitEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("CONSULT_ID")
    private String consultId;
    @TableField(value = "UNIT_TYPE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String unitTypeId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "CONTACT_USER" , updateStrategy = FieldStrategy.IGNORED)
    private String contactUser;
    @TableField(value = "CONTACT_PHONE" , updateStrategy = FieldStrategy.IGNORED)
    private String contactPhone;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("LAST_MODIFY_TIME")
    private Date lastModifyTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("TENANT_ID")
    private String tenantId;
    @TableField("REMARK")
    private String remark;
}
