package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同支付条款表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-03
 */
@Data
@TableName("ths_manage_contract_payment_clause")
public class ThsManageContractPaymentClauseEntity {
    @TableId(value = "ID")
    private String id;
    @TableField("CONTRACT_ID")
    private String contractId;
    @TableField(value = "PERIOD_NUM", updateStrategy = FieldStrategy.IGNORED)
    private Integer periodNum;
    @TableField(value = "AMOUNT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal amount;
    @TableField(value = "PAYMENT_DATE", updateStrategy = FieldStrategy.IGNORED)
    private Date paymentDate;
    @TableField(value = "REMARK", updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField(value = "PAYMENT_TERMS", updateStrategy = FieldStrategy.IGNORED)
    private String paymentTerms;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("LAST_MODIFY_TIME")
    private Date lastModifyTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("TENANT_ID")
    private String tenantId;
}
