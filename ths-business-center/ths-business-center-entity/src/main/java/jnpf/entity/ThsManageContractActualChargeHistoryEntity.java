package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 当前累计应收费明细
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-04-26
 */
@Data
@TableName("ths_manage_contract_actual_charge_history")
public class ThsManageContractActualChargeHistoryEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "CONTRACT_ID", updateStrategy = FieldStrategy.IGNORED)
    private String contractId;
    @TableField(value = "ACTUAL_CHARGE", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal actualCharge;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
}
