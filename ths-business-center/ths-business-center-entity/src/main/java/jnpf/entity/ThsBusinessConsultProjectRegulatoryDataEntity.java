package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("ths_business_consult_project_regulatory_data")
public class ThsBusinessConsultProjectRegulatoryDataEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "CONSULT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultId;
    @TableField(value = "CONSTRUCTION_PROJECT" , updateStrategy = FieldStrategy.IGNORED)
    private Integer constructionProject;
    @TableField(value = "ENGINEERING_COST_EXCEED" , updateStrategy = FieldStrategy.IGNORED)
    private Integer engineeringCostExceed;
    @TableField(value = "START_DATE" , updateStrategy = FieldStrategy.IGNORED)
    private Date startDate;
    @TableField(value = "COMPLETION_DATE" , updateStrategy = FieldStrategy.IGNORED)
    private Date completionDate;
    @TableField(value = "CONSULTATION_CONTRACT_AMOUNT" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal consultationContractAmount;
    @TableField(value = "CONSULTATION_CONTRACT_METHOD" , updateStrategy = FieldStrategy.IGNORED)
    private String consultationContractMethod;
    @TableField(value = "CONTRACT_SIGNING_DATE" , updateStrategy = FieldStrategy.IGNORED)
    private Date contractSigningDate;
    @TableField(value = "CONSULTING_CONTRACT_SERVICE_DATE" , updateStrategy = FieldStrategy.IGNORED)
    private String consultingContractServiceDate;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "MODIFIER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String modifierId;
    @TableField(value = "MODIFY_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date modifyTime;
    @TableField(value = "UNIFIED_ENGINEERING_CODING" , updateStrategy = FieldStrategy.IGNORED)
    private String unifiedEngineeringCoding;
    @TableField(value = "CONSTRUCTION_PERMIT_PROJECT_NUMBER" , updateStrategy = FieldStrategy.IGNORED)
    private String constructionPermitProjectNumber;
    @TableField(value = "CONSTRUCTION_CONTRACT_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String constructionContractType;

    @TableField(value = "STRUCTURE_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String structureType;
    @TableField(value = "PROJECT_CONTRACTING_METHOD" , updateStrategy = FieldStrategy.IGNORED)
    private String projectContractingMethod;
    @TableField(value = "EXECUTE_QUOTA" , updateStrategy = FieldStrategy.IGNORED)
    private String executeQuota;

    @TableField(value = "RATE_STANDARD" , updateStrategy = FieldStrategy.IGNORED)
    private String rateStandard;
    @TableField(value = "INFORMATION_PRICE_PERIODS" , updateStrategy = FieldStrategy.IGNORED)
    private String informationPricePeriods;
    @TableField(value = "PRICING_MODE" , updateStrategy = FieldStrategy.IGNORED)
    private String pricingMode;

    @TableField(value = "DECORATION_STANDARDS" , updateStrategy = FieldStrategy.IGNORED)
    private String decorationStandards;
    @TableField(value = "PROJECT_SCALE" , updateStrategy = FieldStrategy.IGNORED)
    private String projectScale;
}  
