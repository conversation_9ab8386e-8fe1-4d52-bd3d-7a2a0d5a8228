package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-04-23
 */
@Data
@TableName("ths_business_budget_project_detail_download")
public class ThsBusinessConsultProjectDetailDownloadEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value ="PID"  )
    private String pid;
    @TableField(value = "VNO" , updateStrategy = FieldStrategy.IGNORED)
    private String vno;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "BUILD_AREA" )
    private BigDecimal buildArea;
    @TableField(value = "TOTAL_FLOOR" , updateStrategy = FieldStrategy.IGNORED)
    private Integer totalFloor;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;
    @TableField(value = "AVG_TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal avgTotalPrice;
    @TableField(value = "BZR" , updateStrategy = FieldStrategy.IGNORED)
    private String bzr;
    @TableField(value = "JHR" , updateStrategy = FieldStrategy.IGNORED)
    private String jhr;
    @TableField(value = "HZR" , updateStrategy = FieldStrategy.IGNORED)
    private String hzr;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
}
