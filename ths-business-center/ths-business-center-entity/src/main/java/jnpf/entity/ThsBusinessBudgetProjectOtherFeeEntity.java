package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.base.entity.SuperBaseEntity;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 项目的建设工程其他费
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-18
 */
@Data
@TableName("ths_business_budget_project_other_fee")
public class ThsBusinessBudgetProjectOtherFeeEntity extends SuperBaseEntity.SuperTBaseEntity {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "ORD_CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String ordCode;
    @TableField(value = "PID" , updateStrategy = FieldStrategy.IGNORED)
    private String pid;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "CONTENT" , updateStrategy = FieldStrategy.IGNORED)
    private String content;
    @TableField(value = "ITEM_UNIT" , updateStrategy = FieldStrategy.IGNORED)
    private String itemUnit;
    @TableField(value = "QUANTITY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quantity;
    @TableField(value = "UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal unitPrice;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;

    @TableField(value = "SEND_QUANTITY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal sendQuantity;
    @TableField(value = "SEND_UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal sendUnitPrice;
    @TableField(value = "SEND_TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal sendTotalPrice;

    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "LAST_MODIFY_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date lastModifyTime;
    @TableField(value = "TENANT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String tenantId;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "MODIFIER" , updateStrategy = FieldStrategy.IGNORED)
    private String modifier;

//    @Schema(description = "不可竞争费")
//    @TableField(value = "NON_COMPETITIVE" , updateStrategy = FieldStrategy.IGNORED)
//    private Boolean NonCompetitive;
//    @TableField("F_FLOWID")
//    private String flowid;
}
