package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
/**
 * 流程传递记录表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-11
 */
@Data
@TableName("ths_business_budget_project_flow_history")
public class ThsBusinessBudgetProjectFlowHistoryEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "FLOW_DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String flowDetailId;
    @TableField(value = "NEXT_FLOW_DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String nextFlowDetailId;
    @TableField(value = "USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "MESSAGE" , updateStrategy = FieldStrategy.IGNORED)
    private String message;
    @TableField(value = "TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer type;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;

//    @TableField(value = "SEND_TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
//    private BigDecimal sendTotalPrice;

    @TableField(value = "ROLE_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer roleType;
    @TableField(value = "DIFFERENCE_SEND_MONEY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal differenceSendMoney;

    @TableField(value = "AUDIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal auditPrice;
}