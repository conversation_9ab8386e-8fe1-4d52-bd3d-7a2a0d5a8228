package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-11-08
 */
@Data
@TableName("ths_business_budget_project_detail_resource_summary")
public class ThsBusinessBudgetProjectDetailResourceSummaryEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "SPEC" , updateStrategy = FieldStrategy.IGNORED)
    private String spec;
    @TableField(value = "ITEM_UNIT" , updateStrategy = FieldStrategy.IGNORED)
    private String itemUnit;
    @TableField(value = "QUANTITY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quantity;
    @TableField(value = "TAX_UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxUnitPrice;
    @TableField(value = "TAX_RATE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxRate;
    @TableField("UNIT_PRICE")
    private BigDecimal unitPrice;
    @TableField(value = "DE_UNIT_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal deUnitPrice;
    @TableField(value = "TOTAL_PRICE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalPrice;
    @TableField(value = "COST_KIND" , updateStrategy = FieldStrategy.IGNORED)
    private Integer costKind;
    @TableField(value = "PRODUCING_AREA" , updateStrategy = FieldStrategy.IGNORED)
    private String producingArea;
    @TableField(value = "SUPPLIER" , updateStrategy = FieldStrategy.IGNORED)
    private String supplier;
    @TableField(value = "PROVIDER" , updateStrategy = FieldStrategy.IGNORED)
    private Integer provider;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("F_FLOWID")
    private String flowid;
}
