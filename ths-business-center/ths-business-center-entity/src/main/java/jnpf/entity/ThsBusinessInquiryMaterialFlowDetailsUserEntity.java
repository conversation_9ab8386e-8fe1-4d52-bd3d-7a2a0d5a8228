package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 建行-流程节点对应的人员表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-18
 */
@Data
@TableName("ths_business_inquiry_material_flow_details_user")
public class ThsBusinessInquiryMaterialFlowDetailsUserEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("CONSULT_ID")
    private String consultId;
//    @TableField("MATERIAL_ID")
//    private String materialId;
    @TableField("FLOW_DETAIL_ID")
    private String flowDetailId;
    @TableField("USER_ID")
    private String userId;
    @TableField("SEQUENCE")
    private Integer sequence;
}
