package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 咨询项目附加信息
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-08-21
 */
@Data
@TableName("ths_business_consult_project_additional")
public class ThsBusinessConsultProjectAdditionalEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "CONSULT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "DATA_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String dataType;
    @TableField(value = "UNIT" , updateStrategy = FieldStrategy.IGNORED)
    private String unit;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField("TENANT_ID")
    private String tenantId;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("MODIFIER")
    private String modifier;
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    @TableField(value = "VALUE" , updateStrategy = FieldStrategy.IGNORED)
    private String value;
//    @TableField("F_FLOWID")
//    private String flowid;
    @TableField("DICT_ID")
    private String dictId;
}
