package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
/**
 * 文件历史版本表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-10
 */
@Data
@TableName("ths_business_budget_project_detail_file_history")
public class ThsBusinessBudgetProjectDetailFileHistoryEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "SOURCE_FID" , updateStrategy = FieldStrategy.IGNORED)
    private String sourceFid;
    @TableField(value = "ITEM_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String itemId;
    @TableField(value = "FILE_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String fileType;
    @TableField(value = "FILE_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String fileName;
    @TableField(value = "FILE_SAVE_PATH" , updateStrategy = FieldStrategy.IGNORED)
    private String fileSavePath;
    @TableField(value = "FILE_SIZE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal fileSize;
    @TableField(value = "FILE_VERSION" , updateStrategy = FieldStrategy.IGNORED)
    private Integer fileVersion;
    @TableField(value = "IS_TAG" , updateStrategy = FieldStrategy.IGNORED)
    private Boolean isTag;
    @TableField(value = "TAG_REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String tagRemark;
    @TableField(value = "TAG_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String tagName;
    @TableField(value = "SUBMIT_BY" , updateStrategy = FieldStrategy.IGNORED)
    private String submitBy;
    @TableField(value = "CREATOR" , updateStrategy = FieldStrategy.IGNORED)
    private String creator;
    @TableField(value = "CREATION_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date creationTime;
    @TableField(value = "MODIFIER" , updateStrategy = FieldStrategy.IGNORED)
    private String modifier;
    @TableField(value = "MODIFIED_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date modifiedTime;
    @TableField(value = "IS_DELETE" , updateStrategy = FieldStrategy.IGNORED)
    private Boolean isDelete;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "TENANT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String tenantId;

    @TableField(value = "AUDIT_ROLE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer auditRole;
//    @TableField("F_FLOWID")
//    private String flowid;
}
