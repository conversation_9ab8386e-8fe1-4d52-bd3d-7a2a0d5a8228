package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-07-01
 */
@Data
@TableName("ths_business_budget_project_detail_contract_file")
public class ThsBusinessBudgetProjectDetailContractFileEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "budget_project_id" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "FILE_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String fileName;
    @TableField(value = "FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String fileId;
    @TableField(value = "UPLOADER" , updateStrategy = FieldStrategy.IGNORED)
    private String uploader;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
}
