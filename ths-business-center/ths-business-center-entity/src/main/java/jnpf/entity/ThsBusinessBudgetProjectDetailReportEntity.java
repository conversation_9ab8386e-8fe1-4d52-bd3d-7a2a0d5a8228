package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 一表通-excel报表模板
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-08-05
 */
@Data
@TableName("ths_business_budget_project_detail_report")
public class ThsBusinessBudgetProjectDetailReportEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "TEMPLATE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String templateId;
    @TableField(value = "SYS_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sysType;
    @TableField(value = "DETAIL_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer detailType;
    @TableField(value = "IS_ARCHIVED" , updateStrategy = FieldStrategy.IGNORED)
    private Integer isArchived;
    @TableField(value = "ARCHIVED_FILE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String archivedFileId;
    @TableField(value = "IS_TRANSFER" , updateStrategy = FieldStrategy.IGNORED)
    private Integer isTransfer;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("F_FLOWID")
    private String flowid;
}
