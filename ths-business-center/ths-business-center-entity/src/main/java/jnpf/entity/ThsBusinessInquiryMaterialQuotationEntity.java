package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
/**
 * 询价记录材料报价信息表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-06
 */
@Data
@TableName("ths_business_inquiry_material_quotation")
public class ThsBusinessInquiryMaterialQuotationEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("MATERIAL_ID")
    private String materialId;
    @TableField(value = "BRAND" , updateStrategy = FieldStrategy.IGNORED)
    private String brand;
    @TableField(value = "QUOTATION" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal quotation;
    @TableField(value = "QUOTATION_DATE" , updateStrategy = FieldStrategy.IGNORED)
    private Date quotationDate;
    @TableField(value = "QUOTATION_UNIT_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String quotationUnitName;
    @TableField(value = "QUOTATION_PERSON" , updateStrategy = FieldStrategy.IGNORED)
    private String quotationPerson;
    @TableField(value = "CONTACT_INFORMATION" , updateStrategy = FieldStrategy.IGNORED)
    private String contactInformation;
    @TableField(value = "REMARK" , updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("MODIFIER_ID")
    private String modifierId;
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    @TableField("SEQUENCE")
    private Integer sequence;
}
