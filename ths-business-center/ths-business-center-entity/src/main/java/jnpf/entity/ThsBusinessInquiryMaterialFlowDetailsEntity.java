package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 建行-材料流程详情表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-18
 */
@Data
@TableName("ths_business_inquiry_material_flow_details")
public class ThsBusinessInquiryMaterialFlowDetailsEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "CONSULT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultId;
    @TableField("PREV_ID")
    private String prevId;
    @TableField("NEXT_ID")
    private String nextId;
    @TableField("CODE")
    private String code;
    @TableField("NAME")
    private String name;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("F_FLOWID")
    private String flowid;
}
