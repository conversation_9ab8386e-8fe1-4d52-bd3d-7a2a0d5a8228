package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 项目流程详情表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-06
 */
@Data
@TableName("ths_business_budget_project_flow_details")
public class ThsBusinessBudgetProjectFlowDetailsEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "PREV_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String prevId;
    @TableField(value = "NEXT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String nextId;
    @TableField(value = "CODE" , updateStrategy = FieldStrategy.IGNORED)
    private String code;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField("ALLOW_EDITORIAL")
    private Integer allowEditorial;
    @TableField(value = "role_type" , updateStrategy = FieldStrategy.IGNORED)
    private Integer roleType;
}
