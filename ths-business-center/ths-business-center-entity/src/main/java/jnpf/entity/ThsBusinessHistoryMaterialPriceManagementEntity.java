package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 历史材料价格管理
 *
 * @版本： V5.0.0
 * @版权： ©2011- 2023 深圳市斯维尔科技股份有限公司 版权所有
 * @作者： 深圳市斯维尔科技股份有限公司
 * @日期： 2024-11-14
 */
@Data
@TableName("ths_business_history_material_price_management")
public class ThsBusinessHistoryMaterialPriceManagementEntity  {
    @TableId(value ="id"  )
    @JSONField(name = "id")
    private String id;
    @TableField(value = "contract_code" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "contract_code")
    private String contractCode;
    @TableField("information_price_file_name")
    @JSONField(name = "information_price_file_name")
    private String informationPriceFileName;
    @TableField("code")
    @JSONField(name = "code")
    private String code;
    @TableField("name")
    @JSONField(name = "name")
    private String name;
    @TableField(value = "spec" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "spec")
    private String spec;
    @TableField(value = "item_unit" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "item_unit")
    private String itemUnit;
    @TableField("quantity")
    @JSONField(name = "quantity")
    private BigDecimal quantity;
    @TableField(value = "unit_price" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "unit_price")
    private BigDecimal unitPrice;
    @TableField("total_price")
    @JSONField(name = "total_price")
    private BigDecimal totalPrice;
    @TableField("cost_kind")
    @JSONField(name = "cost_kind")
    private Integer costKind;
    @TableField("remark")
    @JSONField(name = "remark")
    private String remark;
    @TableField("sequence")
    @JSONField(name = "sequence")
    private Integer sequence;
    @TableField(value = "item_type" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "item_type")
    private Integer itemType;
    @TableField("price_selection_principle")
    @JSONField(name = "price_selection_principle")
    private String priceSelectionPrinciple;
    @TableField(value = "project_name" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "project_name")
    private String projectName;
    @TableField("creator_id")
    @JSONField(name = "creator_id")
    private String creatorId;
    @TableField(value = "import_time" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "import_time")
    private Date importTime;
    @TableField("create_time")
    @JSONField(name = "create_time")
    private Date createTime;
    @TableField(value = "information_price_date" , updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "information_price_date")
    private Date informationPriceDate;
    @TableField("qdy_resource_summary_id")
    @JSONField(name = "qdy_resource_summary_id")
    private String qdyResourceSummaryId;
    @TableField("qdy_project_id")
    @JSONField(name = "qdy_project_id")
    private String qdyProjectId;
}
