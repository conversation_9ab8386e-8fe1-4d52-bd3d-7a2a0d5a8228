package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


import java.util.Date;

/**
 * 一表通-excel报表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-26
 */
@Data
@TableName("ths_business_budget_project_detail_excel_report")
public class ThsBusinessBudgetProjectDetailExcelReportEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "DETAIL_ID", updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "NAME", updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField(value = "DATA_REPORT_ID", updateStrategy = FieldStrategy.IGNORED)
    private String dataReportId;
    @TableField(value = "SYS_TYPE", updateStrategy = FieldStrategy.IGNORED)
    private Integer sysType;
    @TableField(value = "DETAIL_TYPE", updateStrategy = FieldStrategy.IGNORED)
    private Integer detailType;
    @TableField("IS_FILE")
    private Integer isFile;
    @TableField("IS_ARCHIVED")
    private Integer isArchived;
    @TableField("EXCEL_FILE_ID")
    private String excelFileId;
    @TableField("ARCHIVED_FILE_ID")
    private String archivedFileId;
    @TableField("IS_TRANSFER")
    private Integer isTransfer;
    @TableField("EDIT_FILE")
    private byte[] editFile;
}
