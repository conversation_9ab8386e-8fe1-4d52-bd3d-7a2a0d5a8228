package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 编制说明
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-19
 */
@Data
@TableName("ths_business_budget_project_instructions")
public class ThsBusinessBudgetProjectInstructionsEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "LAST_MODIFY_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date lastModifyTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "TENANT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String tenantId;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CONTENT" , updateStrategy = FieldStrategy.IGNORED)
    private String content;
}
