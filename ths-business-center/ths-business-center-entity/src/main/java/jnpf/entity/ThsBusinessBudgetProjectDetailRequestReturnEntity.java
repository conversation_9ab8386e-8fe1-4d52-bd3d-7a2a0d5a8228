package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 工程审批流程申请撤回表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-21
 */
@Data
@TableName("ths_business_budget_project_detail_request_return")
public class ThsBusinessBudgetProjectDetailRequestReturnEntity {
    @TableId(value = "ID")
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID", updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID", updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "PROJECT_NAME", updateStrategy = FieldStrategy.IGNORED)
    private String projectName;
    @TableField(value = "SEQUENCE", updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "FINISH_STATE", updateStrategy = FieldStrategy.IGNORED)
    private Boolean finishState;
    @TableField(value = "CREATOR_ID", updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "IDEA_DESCRIPTION", updateStrategy = FieldStrategy.IGNORED)
    private String ideaDescription;
    @TableField(value = "ACCEPT_UID", updateStrategy = FieldStrategy.IGNORED)
    private String acceptUid;
    @TableField(value = "ACCEPT_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date acceptTime;
    @TableField(value = "ACCEPTANCE", updateStrategy = FieldStrategy.IGNORED)
    private Boolean acceptance;
    @TableField("F_FLOWID")
    private String flowid;

    /**
     * 撤回原因
     */
    @TableField("withdraw_reason")
    private String withdrawReason;
}
