package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
/**
 * 咨询成果登记详细信息
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-19
 */
@Data
@TableName("ths_business_consult_achievement_details")
public class ThsBusinessConsultAchievementDetailsEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("PID")
    private String pid;
    @TableField("ACHIEVEMENT_ID")
    private String achievementId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "PRODUCTION_COMPLETE_RATE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal productionCompleteRate;
    @TableField(value = "BUILD_AREA" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal buildArea;
    @TableField(value = "ITEM_UNIT" , updateStrategy = FieldStrategy.IGNORED)
    private String itemUnit;
    @TableField(value = "AUDIT_MONEY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal auditMoney;
    @TableField(value = "SEND_MONEY" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal sendMoney;

    @TableField("CREATOR_ID")
    private String creatorId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("LAST_MODIFY_TIME")
    private Date lastModifyTime;
    @TableField("SEQUENCE")
    private Integer sequence;
    @TableField("TENANT_ID")
    private String tenantId;
    @TableField("REMARK")
    private String remark;
    @TableField("BUDGET_DETAIL_ID")
    private String budgetDetailId;
    @TableField("MODIFIER")
    private String modifier;
    @TableField(value = "ITEM_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer itemType;
}
