package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 流程节点对应的人员表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-06
 */
@Data
@TableName("ths_business_budget_project_flow_details_user")
public class ThsBusinessBudgetProjectFlowDetailsUserEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField(value = "BUDGET_PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String budgetProjectId;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "FLOW_DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String flowDetailId;
    @TableField(value = "USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
    @TableField(value = "FINISH_STATE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer finishState;
}
