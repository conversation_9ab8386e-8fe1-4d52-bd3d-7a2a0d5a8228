package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("ths_business_consult_project_business_progress")
public class ThsBusinessConsultProjectBusinessProgressEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "CONSULT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String consultId;
    @TableField(value = "JOB_CATEGORY" , updateStrategy = FieldStrategy.IGNORED)
    private String jobCategory;
    @TableField(value = "JOB_CONTENT" , updateStrategy = FieldStrategy.IGNORED)
    private String jobContent;
    @TableField(value = "SCHEDULE" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal schedule;
    @TableField(value = "SCHEDULE_PROPORTION" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal scheduleProportion;
    @TableField(value = "REGISTRATION_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date registrationTime;
    @TableField(value = "REGISTRATION_PERSON" , updateStrategy = FieldStrategy.IGNORED)
    private String registrationPerson;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
}  
