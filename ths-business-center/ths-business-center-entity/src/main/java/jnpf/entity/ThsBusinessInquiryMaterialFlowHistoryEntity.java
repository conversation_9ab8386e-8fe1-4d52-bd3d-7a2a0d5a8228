package jnpf.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;
/**
 * 建行-流程传递记录表
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-03-18
 */
@Data
@TableName("ths_business_inquiry_material_flow_history")
public class ThsBusinessInquiryMaterialFlowHistoryEntity  {
    @TableId(value ="ID"  )
    private String id;
    @TableField("CONSULT_ID")
    private String consultId;
    @TableField("MATERIAL_ID")
    private String materialId;
    @TableField("FLOW_DETAIL_ID")
    private String flowDetailId;
    @TableField("NEXT_FLOW_DETAIL_ID")
    private String nextFlowDetailId;
    @TableField("USER_ID")
    private String userId;
    @TableField("MESSAGE")
    private String message;
    @TableField("TYPE")
    private Integer type;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("SEQUENCE")
    private Integer sequence;
}
