package jnpf.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("ths_business_budget_project_detail_excel_report_details")
public class ThsBusinessBudgetProjectDetailExcelReportDetailsEntity {  
    @TableId(value = "ID")
    private String id;
    @TableField(value = "DETAIL_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String detailId;
    @TableField(value = "EXCEL_REPORT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String excelReportId;
    @TableField(value = "REPORT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String reportId;
    @TableField(value = "NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String name;
    @TableField(value = "VALUE_DATA" , updateStrategy = FieldStrategy.IGNORED)
    private String valueData;
    @TableField(value = "VALUE_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String valueType;
    @TableField(value = "CREATOR_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String creatorId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "SEQUENCE" , updateStrategy = FieldStrategy.IGNORED)
    private Integer sequence;
}  
