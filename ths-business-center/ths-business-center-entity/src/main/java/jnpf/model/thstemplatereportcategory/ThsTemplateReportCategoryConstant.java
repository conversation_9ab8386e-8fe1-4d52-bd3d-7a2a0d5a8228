package jnpf.model.thstemplatereportcategory;

import jnpf.util.JsonUtil;
import java.util.Map;

/**
 * ThsTemplateReportCategory配置json
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-08-05
 */
public class ThsTemplateReportCategoryConstant{
    /** 数据库链接 */
    public static final String  DBLINKID = "0";
    /** 表别名 map */
    public static final Map<String,String>  TABLERENAMES = JsonUtil.getJsonToBean("{\"ths_template_report_category\":\"ThsTemplateReportCategory\"}",Map.class);
    /** 子表model map */
    public static final Map<String,String>  TABLEFIELDKEY = JsonUtil.getJsonToBean("{}",Map.class);
    /** 整个表单配置json */
    public static final String  getFormData(){
        StringBuilder sb = new StringBuilder();
sb.append("{\"formRef\":\"formRef\",\"formModel\":\"dataForm\",\"size\":\"default\",\"labelPosition\":\"right\",\"labelWidth\":100,\"formRules\":\"rules\",\"popupType\":\"general\",\"generalWidth\":\"600px\",\"fullScreenWidth\":\"100%\",\"drawerWidth\":\"600px\",\"gutter\":15,\"disabled\":false,\"span\":24,\"colon\":false,\"hasCancelBtn\":true,\"cancelButtonText\":\"取消\",\"hasConfirmBtn\":true,\"confirmButtonText\":\"确定\",\"hasPrintBtn\":false,\"printButtonText\":\"打印\",\"primaryKeyPolicy\":1,\"concurrencyLock\":false,\"logicalDelete\":false,\"printId\":\"\",\"formStyle\":\"\",\"classNames\":[],\"className\":[],\"classJson\":\"\",\"funcs\":{\"onLoad\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"beforeSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    return new Promise((resolve, reject) => {\\n        // 在此编写代码\\n        \\n        // 继续执行\\n        resolve()\\n    })\\n}\",\"afterSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"idGlobal\":111,\"fields\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\"},{\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"}]}");        return sb.toString();
    }
    /** 列表字段配置json */
    public static final String  getColumnData(){
        StringBuilder sb = new StringBuilder();
sb.append("{\"ruleList\":[],\"searchList\":[],\"hasSuperQuery\":true,\"childTableStyle\":1,\"showSummary\":false,\"summaryField\":[],\"columnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"父ID\",\"prop\":\"pid\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"pid\",\"fullName\":\"父ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"pid\",\"fullName\":\"父ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"}],\"defaultColumnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":true},{\"label\":\"父ID\",\"prop\":\"pid\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"pid\",\"fullName\":\"父ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\",\"checked\":true}],\"type\":1,\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"hasTreeQuery\":false,\"treeTitle\":\"左侧标题\",\"treeDataSource\":\"organize\",\"treeDictionary\":\"\",\"treeRelation\":\"\",\"treeSynType\":0,\"treeInterfaceId\":\"\",\"treeInterfaceName\":\"\",\"treeTemplateJson\":[],\"treePropsUrl\":\"\",\"treePropsName\":\"\",\"treePropsValue\":\"id\",\"treePropsChildren\":\"children\",\"treePropsLabel\":\"fullName\",\"groupField\":\"\",\"parentField\":\"\",\"printIds\":[],\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\",\"rowStyle\":\"(record, index) => {\\r\\n   \\r\\n}\",\"cellStyle\":\"(record, rowIndex, column) => {\\r\\n   \\r\\n}\"},\"uploaderTemplateJson\":{}}");        return sb.toString();
    }
    /** app列表字段配置json */
    public static final String  getAppColumnData(){
        StringBuilder sb = new StringBuilder();
sb.append("{\"ruleListApp\":[],\"searchList\":[],\"hasSuperQuery\":false,\"columnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"设计子表-名称\",\"prop\":\"tableField103-name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField103-name\",\"fullName\":\"设计子表-名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1722822228746,\"isSubTable\":true,\"parentVModel\":\"tableField103\",\"relationTable\":\"ths_template_report_category_file\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"设计子表-代号\",\"prop\":\"tableField103-code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField103-code\",\"fullName\":\"设计子表-代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1722822247850,\"isSubTable\":true,\"parentVModel\":\"tableField103\",\"relationTable\":\"ths_template_report_category_file\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"设计子表-ths_template_report_category 表 id\",\"prop\":\"tableField103-categoryId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField103-categoryId\",\"fullName\":\"设计子表-ths_template_report_category 表 id\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"ths_template_report_category 表 id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1722822251154,\"isSubTable\":true,\"parentVModel\":\"tableField103\",\"relationTable\":\"ths_template_report_category_file\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"categoryId\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"pid\",\"fullName\":\"父ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"}],\"defaultColumnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1722822190128},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1722822195306},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":true},{\"label\":\"父ID\",\"prop\":\"pid\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"pid\",\"fullName\":\"父ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"父ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":108,\"renderKey\":1722825992771},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"pid\",\"checked\":false},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":110,\"renderKey\":1722826009129},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":false},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_template_report_category\",\"noShow\":false,\"formId\":111,\"renderKey\":1722826011042},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\",\"checked\":false}],\"sortList\":[],\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\"}}");        return sb.toString();
    }
    /** 表列表 */
    public static final String  getTableList(){
        StringBuilder sb = new StringBuilder();
sb.append("[{\"relationField\":\"\",\"relationTable\":\"\",\"table\":\"ths_template_report_category\",\"tableName\":\"材料分类表\",\"tableField\":\"\",\"typeId\":\"1\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"pid\",\"field\":\"pid\",\"fieldName\":\"父ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"unit\",\"field\":\"unit\",\"fieldName\":\"单位\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"code\",\"field\":\"code\",\"fieldName\":\"代号\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"name\",\"field\":\"name\",\"fieldName\":\"名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"creator_id\",\"field\":\"creatorId\",\"fieldName\":\"创建人\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"create_time\",\"field\":\"createTime\",\"fieldName\":\"创建时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowid\",\"field\":\"flowid\",\"fieldName\":\"流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]}]");        return sb.toString();
    }
}
