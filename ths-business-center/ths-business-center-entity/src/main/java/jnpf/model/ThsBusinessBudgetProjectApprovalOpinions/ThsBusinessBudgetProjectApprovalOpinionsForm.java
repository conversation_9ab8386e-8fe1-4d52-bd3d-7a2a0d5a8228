package jnpf.model.ThsBusinessBudgetProjectApprovalOpinions;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ThsBusinessBudgetProjectApprovalOpinionsForm {  
    @Schema(description = "id")
    @JsonProperty("id")
    private String id;

    @Schema(description = "项目id")
    @JsonProperty("budgetProjectId")
    private String budgetProjectId;

    @Schema(description = "detail表Id")
    @JsonProperty("detailId")
    private String detailId;

    @Schema(description = "解析清单id")
    @JsonProperty("iid")
    private Integer iid;

    @Schema(description = "清单编码")
    @JsonProperty("code")
    private String code;

    @Schema(description = "清单名称")
    @JsonProperty("name")
    private String name;

    @Schema(description = "意见类型")
    @JsonProperty("opinionType")
    private String opinionType;

    @Schema(description = "审批意见")
    @JsonProperty("opinionRemark")
    private String opinionRemark;

    @Schema(description = "审批人id")
    @JsonProperty("opinionId")
    private String opinionId;

    @Schema(description = "审批时间")
    @JsonProperty("opinionTime")
    private Date opinionTime;

    @Schema(description = "当前审批id")
    @JsonProperty("curFlowDetailId")
    private String curFlowDetailId;

    @Schema(description = "顺序码")
    @JsonProperty("sequence")
    private Integer sequence;

}  
