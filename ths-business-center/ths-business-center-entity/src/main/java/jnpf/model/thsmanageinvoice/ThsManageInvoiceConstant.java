package jnpf.model.thsmanageinvoice;

import jnpf.util.JsonUtil;

import java.util.Map;

/**
 * ThsManageInvoice配置json
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2024-02-29
 */
public class ThsManageInvoiceConstant {
    /**
     * 数据库链接
     */
    public static final String DBLINKID = "0";
    /**
     * 表别名 map
     */
    public static final Map<String, String> TABLERENAMES = JsonUtil.getJsonToBean("{\"ths_manage_invoice\":\"ths_manage_invoice\",\"ths_manage_invoice_details\":\"ths_manage_invoice_details\"}", Map.class);
    /**
     * 子表model map
     */
    public static final Map<String, String> TABLEFIELDKEY = JsonUtil.getJsonToBean("{\"tableField108\":\"ths_manage_invoice_details\"}", Map.class);

    /**
     * 整个表单配置json
     */
    public static final String getFormData() {
        return "{\"formRef\":\"formRef\",\"formModel\":\"dataForm\",\"size\":\"default\",\"labelPosition\":\"right\",\"labelWidth\":100,\"formRules\":\"rules\",\"popupType\":\"general\",\"generalWidth\":\"600px\",\"fullScreenWidth\":\"100%\",\"drawerWidth\":\"600px\",\"gutter\":15,\"disabled\":false,\"span\":24,\"colon\":false,\"hasCancelBtn\":true,\"cancelButtonText\":\"取消\",\"hasConfirmBtn\":true,\"confirmButtonText\":\"确定\",\"hasPrintBtn\":false,\"printButtonText\":\"打印\",\"primaryKeyPolicy\":1,\"concurrencyLock\":false,\"logicalDelete\":false,\"printId\":\"\",\"formStyle\":\"\",\"classNames\":[],\"className\":[],\"classJson\":\"\",\"funcs\":{\"onLoad\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"beforeSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    return new Promise((resolve, reject) => {\\n        // 在此编写代码\\n        \\n        // 继续执行\\n        resolve()\\n    })\\n}\",\"afterSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"idGlobal\":123,\"fields\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\"},{\"__config__\":{\"jnpfKey\":\"uploadFile\",\"label\":\"up\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfUploadFile\",\"tagIcon\":\"icon-ym icon-ym-generator-upload\",\"className\":[],\"defaultValue\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"formId\":122,\"renderKey\":*************},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"disabled\":false,\"accept\":\"\",\"fileSize\":10,\"sizeUnit\":\"MB\",\"buttonText\":\"点击上传\",\"limit\":9,\"pathType\":\"selfPath\",\"isAccount\":0,\"folder\":\"/thsManage/Invoice\",\"tipText\":\"\",\"__vModel__\":\"tenantId\"},{\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"},{\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\"},{\"__config__\":{\"jnpfKey\":\"table\",\"label\":\"发票详情\",\"tipLabel\":\"\",\"showLabel\":false,\"tagIcon\":\"icon-ym icon-ym-generator-table\",\"className\":[],\"tag\":\"JnpfInputTable\",\"defaultValue\":[],\"layout\":\"rowFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"noShow\":false,\"showTitle\":true,\"type\":\"table\",\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\"},{\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\"},{\"__config__\":{\"jnpfKey\":\"uploadFile\",\"label\":\"发票附件路径\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfUploadFile\",\"tagIcon\":\"icon-ym icon-ym-generator-upload\",\"className\":[],\"defaultValue\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"formId\":119,\"renderKey\":*************,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"disabled\":false,\"accept\":\"\",\"fileSize\":100,\"sizeUnit\":\"MB\",\"buttonText\":\"点击上传\",\"limit\":9,\"pathType\":\"selfPath\",\"isAccount\":0,\"folder\":\"/thsManage/Invoice\",\"tipText\":\"\",\"__vModel__\":\"filePath\"}],\"tableName\":\"ths_manage_invoice_details\",\"formId\":108,\"renderKey\":*************,\"componentName\":\"table108\"},\"disabled\":false,\"actionText\":\"添加\",\"showSummary\":0,\"addType\":0,\"addTableConf\":{\"popupTitle\":\"选择数据\",\"popupType\":\"dialog\",\"popupWidth\":\"800px\",\"interfaceId\":\"\",\"interfaceName\":\"\",\"templateJson\":[],\"hasPage\":true,\"pageSize\":20,\"columnOptions\":[],\"relationOptions\":[]},\"summaryField\":[],\"tableConf\":{},\"defaultValue\":[],\"__vModel__\":\"tableField108\"}]}";
    }

    /**
     * 列表字段配置json
     */
    public static final String getColumnData() {
        return "{\"ruleList\":[],\"searchList\":[{\"label\":\"催款登记外键ID\",\"prop\":\"urgingPaymentId\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"label\":\"发票号,多个发票号可以用，串起来\",\"prop\":\"invoiceCode\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"label\":\"状态 \",\"prop\":\"status\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":1,\"searchMultiple\":false,\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"}],\"hasSuperQuery\":true,\"childTableStyle\":1,\"showSummary\":false,\"summaryField\":[],\"columnList\":[{\"label\":\"催款登记外键ID\",\"prop\":\"urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"label\":\"发票号,多个发票号可以用，串起来\",\"prop\":\"invoiceCode\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"label\":\"开票日期\",\"prop\":\"invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"label\":\"状态 \",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"label\":\"开票金额\",\"prop\":\"totalAmount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\"},{\"label\":\"领用人\",\"prop\":\"recipientUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\"},{\"label\":\"备注\",\"prop\":\"remark\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"modifyTime\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\"},{\"label\":\"发票详情-催款登记外键ID\",\"prop\":\"tableField108-urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"label\":\"发票详情-发票号\",\"prop\":\"tableField108-invoiceNo\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\"},{\"label\":\"发票详情-开票日期\",\"prop\":\"tableField108-invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"label\":\"发票详情-开票金额\",\"prop\":\"tableField108-amount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\"},{\"label\":\"开票号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"开票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"}],\"columnOptions\":[{\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"id\":\"code\",\"fullName\":\"开票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"contractId\",\"fullName\":\"合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\"},{\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\"},{\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\"},{\"id\":\"tableField108-contractId\",\"fullName\":\"发票详情-合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\"},{\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\"}],\"defaultColumnList\":[{\"label\":\"催款登记外键ID\",\"prop\":\"urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\",\"checked\":true},{\"label\":\"开票号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"开票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":false},{\"label\":\"发票号,多个发票号可以用，串起来\",\"prop\":\"invoiceCode\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\",\"checked\":true},{\"label\":\"开票日期\",\"prop\":\"invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\",\"checked\":true},{\"label\":\"状态 \",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\",\"checked\":true},{\"label\":\"开票金额\",\"prop\":\"totalAmount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\",\"checked\":true},{\"label\":\"领用人\",\"prop\":\"recipientUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\",\"checked\":true},{\"label\":\"备注\",\"prop\":\"remark\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"modifyTime\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\",\"checked\":true},{\"label\":\"发票详情-合同ID\",\"prop\":\"tableField108-contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-contractId\",\"fullName\":\"发票详情-合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":false},{\"label\":\"发票详情-催款登记外键ID\",\"prop\":\"tableField108-urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\",\"checked\":true},{\"label\":\"发票详情-发票号\",\"prop\":\"tableField108-invoiceNo\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\",\"checked\":true},{\"label\":\"发票详情-开票日期\",\"prop\":\"tableField108-invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\",\"checked\":true},{\"label\":\"发票详情-开票金额\",\"prop\":\"tableField108-amount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\",\"checked\":true}],\"type\":1,\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"hasTreeQuery\":false,\"treeTitle\":\"左侧标题\",\"treeDataSource\":\"dictionary\",\"treeDictionary\":\"\",\"treeRelation\":\"\",\"treeSynType\":0,\"treeInterfaceId\":\"\",\"treeInterfaceName\":\"\",\"treeTemplateJson\":[],\"treePropsUrl\":\"\",\"treePropsName\":\"\",\"treePropsValue\":\"id\",\"treePropsChildren\":\"children\",\"treePropsLabel\":\"fullName\",\"groupField\":\"\",\"parentField\":\"\",\"printIds\":[],\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\",\"rowStyle\":\"(record, index) => {\\r\\n   \\r\\n}\",\"cellStyle\":\"(record, rowIndex, column) => {\\r\\n   \\r\\n}\"},\"uploaderTemplateJson\":{}}";
    }

    /**
     * app列表字段配置json
     */
    public static final String getAppColumnData() {
        return "{\"ruleListApp\":[],\"searchList\":[],\"hasSuperQuery\":false,\"columnList\":[{\"label\":\"催款登记外键ID\",\"prop\":\"urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"label\":\"咨询项目ID\",\"prop\":\"consultProjectId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"consultProjectId\",\"fullName\":\"咨询项目ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"咨询项目ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"consultProjectId\"},{\"label\":\"发票号,多个发票号可以用，串起来\",\"prop\":\"invoiceCode\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"label\":\"开票日期\",\"prop\":\"invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"label\":\"状态 \",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"label\":\"开票金额\",\"prop\":\"totalAmount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\"},{\"label\":\"领用人\",\"prop\":\"recipientUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\"},{\"label\":\"备注\",\"prop\":\"remark\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"modifyTime\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\"},{\"label\":\"发票详情-咨询项目ID\",\"prop\":\"tableField108-consultProjectId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-consultProjectId\",\"fullName\":\"发票详情-咨询项目ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"咨询项目ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"consultProjectId\"},{\"label\":\"发票详情-催款登记外键ID\",\"prop\":\"tableField108-urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"label\":\"发票详情-发票号\",\"prop\":\"tableField108-invoiceNo\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\"},{\"label\":\"发票详情-开票日期\",\"prop\":\"tableField108-invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"label\":\"发票详情-开票金额\",\"prop\":\"tableField108-amount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\"}],\"columnOptions\":[{\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"id\":\"code\",\"fullName\":\"开票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"contractId\",\"fullName\":\"合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\"},{\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\"},{\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\"},{\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\"},{\"id\":\"tableField108-contractId\",\"fullName\":\"发票详情-合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\"},{\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\"},{\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\"},{\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\"}],\"defaultColumnList\":[{\"label\":\"催款登记外键ID\",\"prop\":\"urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"urgingPaymentId\",\"fullName\":\"催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1694682196588},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\",\"checked\":true},{\"label\":\"开票号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"开票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":123,\"renderKey\":1695089756703},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":false},{\"label\":\"合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1694688180987},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":false},{\"label\":\"发票号,多个发票号可以用，串起来\",\"prop\":\"invoiceCode\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"invoiceCode\",\"fullName\":\"发票号,多个发票号可以用，串起来\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号,多个发票号可以用，串起来\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":12,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1694688203520},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceCode\",\"checked\":true},{\"label\":\"开票日期\",\"prop\":\"invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"invoicingDate\",\"fullName\":\"开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":10,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":104,\"renderKey\":1694688223826},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\",\"checked\":true},{\"label\":\"状态 \",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 \",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 \",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":120,\"renderKey\":1694688576642},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\",\"checked\":true},{\"label\":\"开票金额\",\"prop\":\"totalAmount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"totalAmount\",\"fullName\":\"开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1694688229956},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"totalAmount\",\"checked\":true},{\"label\":\"领用人\",\"prop\":\"recipientUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"recipientUser\",\"fullName\":\"领用人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"领用人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1694688261920},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"recipientUser\",\"checked\":true},{\"label\":\"备注\",\"prop\":\"remark\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"remark\",\"fullName\":\"备注\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"备注\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1694688233156},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"remark\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createTime\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"createTime\",\"label\":\"创建时间\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-createtime\",\"className\":[],\"defaultValue\":\"\",\"layout\":\"colFormItem\",\"required\":false,\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":112,\"renderKey\":*************},\"style\":{\"width\":\"100%\"},\"type\":\"currTime\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"createTime\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":110,\"renderKey\":1694688342892},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"modifyTime\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"modifyTime\",\"label\":\"最后一次修改时间\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-modifytime\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":8,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":true,\"formId\":113,\"renderKey\":1694688403861},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"lastModifyTime\",\"checked\":true},{\"label\":\"发票详情-合同ID\",\"prop\":\"tableField108-contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-contractId\",\"fullName\":\"发票详情-合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1694688323944,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":false},{\"label\":\"发票详情-催款登记外键ID\",\"prop\":\"tableField108-urgingPaymentId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-urgingPaymentId\",\"fullName\":\"发票详情-催款登记外键ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"催款登记外键ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":114,\"renderKey\":1694688427004,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"urgingPaymentId\",\"checked\":true},{\"label\":\"发票详情-发票号\",\"prop\":\"tableField108-invoiceNo\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoiceNo\",\"fullName\":\"发票详情-发票号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"发票号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":115,\"renderKey\":1694688430656,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"invoiceNo\",\"checked\":true},{\"label\":\"发票详情-开票日期\",\"prop\":\"tableField108-invoicingDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-invoicingDate\",\"fullName\":\"发票详情-开票日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"开票日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":117,\"renderKey\":1694688457196,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"invoicingDate\",\"checked\":true},{\"label\":\"发票详情-开票金额\",\"prop\":\"tableField108-amount\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-amount\",\"fullName\":\"发票详情-开票金额\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"开票金额\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_manage_invoice\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":118,\"renderKey\":1694688464378,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_manage_invoice_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"amount\",\"checked\":true}],\"sortList\":[],\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\"}}";
    }

    /**
     * 表列表
     */
    public static final String getTableList() {
        return "[{\"relationField\":\"\",\"relationTable\":\"\",\"table\":\"ths_manage_invoice\",\"tableName\":\"开票信息管理\",\"tableField\":\"\",\"typeId\":\"1\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"ID 主键\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"urging_payment_id\",\"field\":\"urgingPaymentId\",\"fieldName\":\"催款登记外键ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"contract_id\",\"field\":\"contractId\",\"fieldName\":\"合同ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"invoice_code\",\"field\":\"invoiceCode\",\"fieldName\":\"发票号,多个发票号可以用，串起来\",\"dataType\":\"varchar\",\"dataLength\":\"500\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"invoicing_date\",\"field\":\"invoicingDate\",\"fieldName\":\"开票日期\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"total_amount\",\"field\":\"totalAmount\",\"fieldName\":\"开票金额\",\"dataType\":\"decimal\",\"dataLength\":\"20,4\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"remark\",\"field\":\"remark\",\"fieldName\":\"备注\",\"dataType\":\"varchar\",\"dataLength\":\"6000\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"recipient_user\",\"field\":\"recipientUser\",\"fieldName\":\"领用人\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"creator_id\",\"field\":\"creatorId\",\"fieldName\":\"创建人\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"create_time\",\"field\":\"createTime\",\"fieldName\":\"创建时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"last_modify_time\",\"field\":\"lastModifyTime\",\"fieldName\":\"最后一次修改时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"status\",\"field\":\"status\",\"fieldName\":\"状态 ，通过的审批的发票才可以收款登记\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"tenant_id\",\"field\":\"tenantId\",\"fieldName\":\"租户ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowtaskid\",\"field\":\"flowtaskid\",\"fieldName\":\"流程任务主键\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowid\",\"field\":\"flowid\",\"fieldName\":\"流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"code\",\"field\":\"code\",\"fieldName\":\"开票号, 绑定发票附件\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]},{\"relationField\":\"id\",\"relationTable\":\"ths_manage_invoice\",\"table\":\"ths_manage_invoice_details\",\"tableName\":\"发票详情管理\",\"tableField\":\"invoiceId\",\"typeId\":\"0\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"ID 主键\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"invoice_id\",\"field\":\"invoiceId\",\"fieldName\":\"\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"contract_id\",\"field\":\"contractId\",\"fieldName\":\"合同ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"urging_payment_id\",\"field\":\"urgingPaymentId\",\"fieldName\":\"催款登记外键ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"invoice_no\",\"field\":\"invoiceNo\",\"fieldName\":\"发票号\",\"dataType\":\"varchar\",\"dataLength\":\"500\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"invoicing_date\",\"field\":\"invoicingDate\",\"fieldName\":\"开票日期\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"amount\",\"field\":\"amount\",\"fieldName\":\"开票金额\",\"dataType\":\"decimal\",\"dataLength\":\"20,4\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"file_path\",\"field\":\"filePath\",\"fieldName\":\"发票附件路径\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"tenant_id\",\"field\":\"tenantId\",\"fieldName\":\"租户ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]}]";
    }
}
