package jnpf.model.thsbusinessconsultproject;

import jnpf.util.JsonUtil;
import java.util.Map;

/**
 * ths_business_consult_project配置json
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-09-26
 */
public class ThsBusinessConsultProjectConstant{
    /** 数据库链接 */
    public static final String  DBLINKID = "0";
    /** 表别名 map */
    public static final Map<String,String>  TABLERENAMES = JsonUtil.getJsonToBean("{\"ths_business_consult_project\":\"ThsBusinessConsultProject\",\"ths_business_consult_project_relevant_unit\":\"ThsBusinessConsultProjectRelevantUnit\"}",Map.class);
    /** 子表model map */
    public static final Map<String,String>  TABLEFIELDKEY = JsonUtil.getJsonToBean("{\"tableField206\":\"ths_business_consult_project_relevant_unit\"}",Map.class);
    /** 整个表单配置json */
    public static final String  getFormData(){
        StringBuilder sb = new StringBuilder();
        sb.append("{\"formRef\":\"formRef\",\"formModel\":\"dataForm\",\"size\":\"default\",\"labelPosition\":\"right\",\"labelWidth\":100,\"formRules\":\"rules\",\"popupType\":\"general\",\"generalWidth\":\"600px\",\"fullScreenWidth\":\"100%\",\"drawerWidth\":\"600px\",\"gutter\":15,\"disabled\":false,\"span\":24,\"colon\":false,\"hasCancelBtn\":true,\"cancelButtonText\":\"取消\",\"hasConfirmBtn\":true,\"confirmButtonText\":\"确定\",\"hasPrintBtn\":false,\"printButtonText\":\"打印\",\"primaryKeyPolicy\":1,\"concurrencyLock\":false,\"logicalDelete\":false,\"printId\":\"\",\"formStyle\":\"\",\"classNames\":[],\"className\":[],\"classJson\":\"\",\"funcs\":{\"onLoad\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"beforeSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    return new Promise((resolve, reject) => {\\n        // 在此编写代码\\n        \\n        // 继续执行\\n        resolve()\\n    })\\n}\",\"afterSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"idGlobal\":232,\"fields\":[{\"__config__\":{\"jnpfKey\":\"tableGrid\",\"label\":\"表格容器\",\"showLabel\":false,\"tag\":\"Table\",\"tagIcon\":\"icon-ym icon-ym-generator-tableGrid\",\"className\":[],\"defaultValue\":[],\"layout\":\"rowFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"noShow\":false,\"borderType\":\"solid\",\"borderColor\":\"#E2E0E0\",\"borderWidth\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":104,\"children\":[{\"__config__\":{\"jnpfKey\":\"billRule\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-documents\",\"className\":[],\"defaultValue\":null,\"layout\":\"colFormItem\",\"required\":false,\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"trigger\":\"change\",\"rule\":\"zixunxiangmudengji\",\"ruleName\":\"咨询项目登记\",\"formId\":231,\"renderKey\":1693967475331},\"style\":{\"width\":\"100%\"},\"readonly\":true,\"placeholder\":\"系统自动生成\",\"__vModel__\":\"code\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"}],\"formId\":103,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":120,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":102,\"renderKey\":1692011761249}},{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":105,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461215569962604997\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":224,\"renderKey\":1692154977113},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461215716352203205\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单位工程\",\"enCode\":\"2\"},{\"id\":\"461215686035773893\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单项工程\",\"enCode\":\"1\"},{\"id\":\"461215646122777029\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"建设项目\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"consultingTypeId\"}],\"formId\":106,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":121,\"children\":[{\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":107,\"renderKey\":1692011761249}},{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":108,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\"}],\"formId\":109,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":122,\"children\":[{\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":110,\"renderKey\":1692011761249}},{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":111,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"}],\"formId\":112,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":123,\"children\":[{\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":113,\"renderKey\":1692011761249}},{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":114,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"}],\"formId\":115,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":124,\"children\":[{\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":116,\"renderKey\":1692011761249}},{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":117,\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属建设项目\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":220,\"renderKey\":1692146658154},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectId\"}],\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"static\",\"dictionaryType\":\"\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"fullName\":\"未提交\",\"id\":\"0\"},{\"fullName\":\"已提交\",\"id\":\"1\"},{\"fullName\":\"已審批\",\"id\":\"2\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"对应系统流程id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":232,\"renderKey\":1695693963563},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sysFlowId\"}],\"formId\":118,\"renderKey\":1692011761249,\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"formId\":125,\"children\":[{\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\"},{\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":189,\"renderKey\":1692015505454},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"sequence\"}],\"tableName\":\"ths_business_consult_project\"}}],\"formId\":119,\"renderKey\":1692011761249}}],\"formId\":101,\"renderKey\":1692011761249,\"componentName\":\"tableGrid101\",\"tableName\":\"ths_business_consult_project\"}},{\"__config__\":{\"jnpfKey\":\"tableGrid\",\"label\":\"表格容器\",\"showLabel\":false,\"tag\":\"Table\",\"tagIcon\":\"icon-ym icon-ym-generator-tableGrid\",\"className\":[],\"defaultValue\":[],\"layout\":\"rowFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"noShow\":false,\"borderType\":\"solid\",\"borderColor\":\"#E2E0E0\",\"borderWidth\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTr\",\"children\":[{\"__config__\":{\"jnpfKey\":\"tableGridTd\",\"merged\":false,\"colspan\":1,\"rowspan\":1,\"children\":[{\"__config__\":{\"jnpfKey\":\"table\",\"label\":\"设计子表\",\"tipLabel\":\"\",\"showLabel\":false,\"tagIcon\":\"icon-ym icon-ym-generator-table\",\"className\":[],\"tag\":\"JnpfInputTable\",\"defaultValue\":[],\"layout\":\"rowFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"noShow\":false,\"showTitle\":true,\"type\":\"table\",\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"相关单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":226,\"renderKey\":1692156744411,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"单位类型，字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461458417995753541\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":227,\"renderKey\":1692156774595,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461458608811419717\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"委托单位\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"unitTypeId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":229,\"renderKey\":1692156821147,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactUser\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":230,\"renderKey\":1692156823245,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactPhone\"}],\"tableName\":\"ths_business_consult_project_relevant_unit\",\"formId\":206,\"renderKey\":1692098233045,\"componentName\":\"table206\"},\"disabled\":false,\"actionText\":\"添加\",\"showSummary\":0,\"addType\":1,\"addTableConf\":{\"popupTitle\":\"选择数据\",\"popupType\":\"dialog\",\"popupWidth\":\"800px\",\"interfaceId\":\"\",\"interfaceName\":\"\",\"templateJson\":[],\"hasPage\":true,\"pageSize\":20,\"columnOptions\":[],\"relationOptions\":[]},\"summaryField\":[],\"tableConf\":{},\"defaultValue\":[],\"__vModel__\":\"tableField206\"}],\"formId\":205,\"renderKey\":1692098225497,\"tableName\":\"ths_business_consult_project\"}}],\"formId\":204,\"renderKey\":1692098225497}}],\"formId\":203,\"renderKey\":1692098225497,\"componentName\":\"tableGrid203\",\"tableName\":\"ths_business_consult_project\"}}]}");
        return sb.toString();
    }
    /** 列表字段配置json */
    public static final String  getColumnData(){
        StringBuilder sb = new StringBuilder();
                sb.append("{\"ruleList\":[],\"searchList\":[{\"label\":\"项目编码\",\"prop\":\"code\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"项目名称\",\"prop\":\"name\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"所属合同，management_center_contract合同ID\",\"prop\":\"contractId\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"label\":\"项目负责人系统管理中用户表ID\",\"prop\":\"managerUserId\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"},{\"label\":\"项目状态（未提交，已提交，已安排）\",\"prop\":\"status\",\"jnpfKey\":\"select\",\"value\":\"\",\"searchType\":1,\"searchMultiple\":false,\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"},{\"label\":\"承办部门，所属部门组织架构\",\"prop\":\"deptId\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"jnpfKey\":\"datePicker\",\"value\":\"\",\"searchType\":3,\"searchMultiple\":false,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"}],\"hasSuperQuery\":true,\"childTableStyle\":1,\"showSummary\":false,\"summaryField\":[],\"columnList\":[{\"label\":\"项目编码\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"项目名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"所属合同，management_center_contract合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"label\":\"项目类型，数据字典配置\",\"prop\":\"projectTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\"},{\"label\":\"委托日期\",\"prop\":\"commissionDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\"},{\"label\":\"编审类型，数据字典配置\",\"prop\":\"editorialTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\"},{\"label\":\"委托单位名称\",\"prop\":\"entrustedCompany\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\"},{\"label\":\"紧急程度\",\"prop\":\"emergencyDegreeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\"},{\"label\":\"计划完成时间\",\"prop\":\"planCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\"},{\"label\":\"委托单位联系人\",\"prop\":\"entrustedUnitUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\"},{\"label\":\"承办部门，所属部门组织架构\",\"prop\":\"deptId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"label\":\"委托单位联系电话\",\"prop\":\"entrustedUnitPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\"},{\"label\":\"项目负责人系统管理中用户表ID\",\"prop\":\"managerUserId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"},{\"label\":\"要求完成时间\",\"prop\":\"requireCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\"},{\"label\":\"项目状态（未提交，已提交，已安排）\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\"},{\"id\":\"consultingTypeId\",\"fullName\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461215569962604997\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":224,\"renderKey\":1692154977113},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461215716352203205\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单位工程\",\"enCode\":\"2\"},{\"id\":\"461215686035773893\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单项工程\",\"enCode\":\"1\"},{\"id\":\"461215646122777029\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"建设项目\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"consultingTypeId\"},{\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\"},{\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\"},{\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\"},{\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\"},{\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\"},{\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\"},{\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"},{\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\"},{\"id\":\"projectId\",\"fullName\":\"所属建设项目\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属建设项目\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":220,\"renderKey\":1692146658154},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectId\"},{\"id\":\"sysFlowId\",\"fullName\":\"对应系统流程id\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"对应系统流程id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dynamic\",\"dictionaryType\":\"\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":223,\"renderKey\":1692154753729},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"sysFlowId\"},{\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"},{\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\"},{\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":189,\"renderKey\":1692015505454},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"sequence\"},{\"id\":\"tableField206-name\",\"fullName\":\"设计子表-相关单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"相关单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":226,\"renderKey\":1692156744411,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"tableField206-unitTypeId\",\"fullName\":\"设计子表-单位类型，字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"单位类型，字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461458417995753541\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":227,\"renderKey\":1692156774595,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461458608811419717\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"委托单位\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"unitTypeId\"},{\"id\":\"tableField206-contactUser\",\"fullName\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":229,\"renderKey\":1692156821147,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactUser\"},{\"id\":\"tableField206-contactPhone\",\"fullName\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":230,\"renderKey\":1692156823245,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactPhone\"}],\"defaultColumnList\":[{\"label\":\"项目编码\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"项目名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":true},{\"label\":\"所属合同，management_center_contract合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":true},{\"label\":\"项目类型，数据字典配置\",\"prop\":\"projectTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\",\"checked\":true},{\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"prop\":\"consultingTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"consultingTypeId\",\"fullName\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461215569962604997\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":224,\"renderKey\":1692154977113},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461215716352203205\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单位工程\",\"enCode\":\"2\"},{\"id\":\"461215686035773893\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单项工程\",\"enCode\":\"1\"},{\"id\":\"461215646122777029\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"建设项目\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"consultingTypeId\",\"checked\":false},{\"label\":\"编审类型，数据字典配置\",\"prop\":\"editorialTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictiona");
                sb.append("ryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\",\"checked\":true},{\"label\":\"委托单位名称\",\"prop\":\"entrustedCompany\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\",\"checked\":true},{\"label\":\"紧急程度\",\"prop\":\"emergencyDegreeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\",\"checked\":true},{\"label\":\"计划完成时间\",\"prop\":\"planCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\",\"checked\":true},{\"label\":\"委托单位联系人\",\"prop\":\"entrustedUnitUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\",\"checked\":true},{\"label\":\"承办部门，所属部门组织架构\",\"prop\":\"deptId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"委托单位联系电话\",\"prop\":\"entrustedUnitPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\",\"checked\":true},{\"label\":\"项目负责人系统管理中用户表ID\",\"prop\":\"managerUserId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\",\"checked\":true},{\"label\":\"要求完成时间\",\"prop\":\"requireCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\",\"checked\":true},{\"label\":\"所属建设项目\",\"prop\":\"projectId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectId\",\"fullName\":\"所属建设项目\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属建设项目\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":220,\"renderKey\":1692146658154},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectId\",\"checked\":false},{\"label\":\"对应系统流程id\",\"prop\":\"sysFlowId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"sysFlowId\",\"fullName\":\"对应系统流程id\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"对应系统流程id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dynamic\",\"dictionaryType\":\"\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":223,\"renderKey\":1692154753729},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"sysFlowId\",\"checked\":false},{\"label\":\"项目状态（未提交，已提交，已安排）\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\",\"checked\":true},{\"label\":\"委托日期\",\"prop\":\"commissionDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\",\"checked\":true},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"inputNumber\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":189,\"renderKey\":1692015505454},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"sequence\",\"checked\":false},{\"label\":\"设计子表-相关单位名称\",\"prop\":\"tableField206-name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-name\",\"fullName\":\"设计子表-相关单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"相关单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":226,\"renderKey\":1692156744411,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":false},{\"label\":\"设计子表-单位类型，字典配置\",\"prop\":\"tableField206-unitTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-unitTypeId\",\"fullName\":\"设计子表-单位类型，字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"单位类型，字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461458417995753541\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":227,\"renderKey\":1692156774595,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461458608811419717\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"委托单位\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"unitTypeId\",\"checked\":false},{\"label\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"prop\":\"tableField206-contactUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-contactUser\",\"fullName\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":229,\"renderKey\":1692156821147,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactUser\",\"checked\":false},{\"label\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"prop\":\"tableField206-contactPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-contactPhone\",\"fullName\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":230,\"renderKey\":1692156823245,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactPhone\",\"checked\":false}],\"type\":1,\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"hasTreeQuery\":false,\"treeTitle\":\"左侧标题\",\"treeDataSource\":\"dictionary\",\"treeDictionary\":\"\",\"treeRelation\":\"\",\"treeSynType\":0,\"treeInterfaceId\":\"\",\"treeInterfaceName\":\"\",\"treeTemplateJson\":[],\"treePropsUrl\":\"\",\"treePropsName\":\"\",\"treePropsValue\":\"id\",\"treePropsChildren\":\"children\",\"treePropsLabel\":\"fullName\",\"groupField\":\"\",\"parentField\":\"\",\"printIds\":[],\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"},{\"value\":\"download\",\"icon\":\"icon-ym icon-ym-btn-download\",\"label\":\"导出\"},{\"value\":\"batchRemove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"批量删除\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"},{\"value\":\"detail\",\"icon\":\"icon-ym icon-ym-generator-menu\",\"label\":\"详情\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\",\"rowStyle\":\"(record, index) => {\\r\\n   \\r\\n}\",\"cellStyle\":\"(record, rowIndex, column) => {\\r\\n   \\r\\n}\"},\"uploaderTemplateJson\":{}}");
        return sb.toString();
    }
    /** app列表字段配置json */
    public static final String  getAppColumnData(){
        StringBuilder sb = new StringBuilder();
                sb.append("{\"ruleListApp\":[],\"searchList\":[],\"hasSuperQuery\":false,\"columnList\":[{\"label\":\"项目编码\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"项目名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"所属合同，management_center_contract合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"label\":\"项目类型，数据字典配置\",\"prop\":\"projectTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\"},{\"label\":\"委托日期\",\"prop\":\"commissionDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\"},{\"label\":\"编审类型，数据字典配置\",\"prop\":\"editorialTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\"},{\"label\":\"委托单位名称\",\"prop\":\"entrustedCompany\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\"},{\"label\":\"紧急程度\",\"prop\":\"emergencyDegreeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\"},{\"label\":\"计划完成时间\",\"prop\":\"planCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\"},{\"label\":\"委托单位联系人\",\"prop\":\"entrustedUnitUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\"},{\"label\":\"承办部门，所属部门组织架构\",\"prop\":\"deptId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"label\":\"委托单位联系电话\",\"prop\":\"entrustedUnitPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\"},{\"label\":\"项目负责人系统管理中用户表ID\",\"prop\":\"managerUserId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"},{\"label\":\"要求完成时间\",\"prop\":\"requireCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\"},{\"label\":\"项目状态（未提交，已提交，已安排）\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\"},{\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\"},{\"id\":\"consultingTypeId\",\"fullName\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461215569962604997\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":224,\"renderKey\":1692154977113},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461215716352203205\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单位工程\",\"enCode\":\"2\"},{\"id\":\"461215686035773893\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单项工程\",\"enCode\":\"1\"},{\"id\":\"461215646122777029\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"建设项目\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"consultingTypeId\"},{\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\"},{\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\"},{\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\"},{\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\"},{\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\"},{\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\"},{\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\"},{\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\"},{\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\"},{\"id\":\"projectId\",\"fullName\":\"所属建设项目\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属建设项目\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":220,\"renderKey\":1692146658154},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectId\"},{\"id\":\"sysFlowId\",\"fullName\":\"对应系统流程id\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"对应系统流程id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dynamic\",\"dictionaryType\":\"\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":223,\"renderKey\":1692154753729},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"sysFlowId\"},{\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\"},{\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\"},{\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":189,\"renderKey\":1692015505454},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"sequence\"},{\"id\":\"tableField206-name\",\"fullName\":\"设计子表-相关单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"相关单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":226,\"renderKey\":1692156744411,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"tableField206-unitTypeId\",\"fullName\":\"设计子表-单位类型，字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"单位类型，字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461458417995753541\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":227,\"renderKey\":1692156774595,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461458608811419717\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"委托单位\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"unitTypeId\"},{\"id\":\"tableField206-contactUser\",\"fullName\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":229,\"renderKey\":1692156821147,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactUser\"},{\"id\":\"tableField206-contactPhone\",\"fullName\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":230,\"renderKey\":1692156823245,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactPhone\"}],\"defaultColumnList\":[{\"label\":\"项目编码\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"项目编码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目编码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":163,\"renderKey\":1692011855793},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"项目名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"项目名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":164,\"renderKey\":1692011866427},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":true},{\"label\":\"所属合同，management_center_contract合同ID\",\"prop\":\"contractId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"contractId\",\"fullName\":\"所属合同，management_center_contract合同ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属合同，management_center_contract合同ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":219,\"renderKey\":1692146643536},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contractId\",\"checked\":true},{\"label\":\"项目类型，数据字典配置\",\"prop\":\"projectTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectTypeId\",\"fullName\":\"项目类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":173,\"renderKey\":1692011981159},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectTypeId\",\"checked\":true},{\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"prop\":\"consultingTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"consultingTypeId\",\"fullName\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461215569962604997\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":224,\"renderKey\":1692154977113},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461215716352203205\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单位工程\",\"enCode\":\"2\"},{\"id\":\"461215686035773893\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"单项工程\",\"enCode\":\"1\"},{\"id\":\"461215646122777029\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"建设项目\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"consultingTypeId\",\"checked\":false},{\"label\":\"编审类型，数据字典配置\",\"prop\":\"editorialTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"editorialTypeId\",\"fullName\":\"编审类型，数据字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"编审类型，数据字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461455362000689605\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":178,\"renderKey\":1692012394725},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461455476438079941\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"审核\",\"enCode\":\"1\"},{\"id\":\"461455449456122309\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"编制\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"编制\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"editorialTypeId\",\"checked\":true},{\"label\":\"委托单位名称\",\"prop\":\"entrustedCompany\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedCompany\",\"fullName\":\"委托单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":176,\"renderKey\":1692012370174},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedCompany\",\"checked\":true},{\"label\":\"紧急程度\",\"prop\":\"emergencyDegreeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"emergencyDegreeId\",\"fullName\":\"紧急程度\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"紧急程度\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461207064278997445\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":177,\"renderKey\":1692012392242},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461207837813512645\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"普通\",\"enCode\":\"0\"},{\"id\":\"461207808294001093\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"紧急\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"emergencyDegreeId\",\"checked\":true},{\"label\":\"计划完成时间\",\"prop\":\"planCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"planCompleteDate\",\"fullName\":\"计划完成时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"计划完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":185,\"renderKey\":1692015450587},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"planCompleteDate\",\"checked\":true},{\"label\":\"委托单位联系人\",\"prop\":\"entrustedUnitUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitUser\",\"fullName\":\"委托单位联系人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":180,\"renderKey\":1692015321322},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitUser\",\"checked\":true},{\"label\":\"承办部门，所属部门组织架构\",\"prop\":\"deptId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"deptId\",\"fullName\":\"承办部门，所属部门组织架构\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"承办部门，所属部门组织架构\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":183,\"renderKey\":1692015406554},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"deptId\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"createUser\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"createUser\",\"label\":\"创建人\",\"showLabel\":true,\"tag\":\"JnpfOpenData\",\"tagIcon\":\"icon-ym icon-ym-generator-founder\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"formId\":196,\"renderKey\":1692097001313},\"style\":{\"width\":\"100%\"},\"type\":\"currUser\",\"readonly\":true,\"placeholder\":\"\",\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"委托单位联系电话\",\"prop\":\"entrustedUnitPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"entrustedUnitPhone\",\"fullName\":\"委托单位联系电话\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"委托单位联系电话\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":179,\"renderKey\":1692015319037},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData,");
                sb.append(" setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"entrustedUnitPhone\",\"checked\":true},{\"label\":\"项目负责人系统管理中用户表ID\",\"prop\":\"managerUserId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"managerUserId\",\"fullName\":\"项目负责人系统管理中用户表ID\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"项目负责人系统管理中用户表ID\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":184,\"renderKey\":1692015408903},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"managerUserId\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":187,\"renderKey\":1692015495020},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"createTime\",\"checked\":true},{\"label\":\"要求完成时间\",\"prop\":\"requireCompleteDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"requireCompleteDate\",\"fullName\":\"要求完成时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"要求完成时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":181,\"renderKey\":1692015371571},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"requireCompleteDate\",\"checked\":true},{\"label\":\"所属建设项目\",\"prop\":\"projectId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"projectId\",\"fullName\":\"所属建设项目\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"所属建设项目\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":220,\"renderKey\":1692146658154},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"projectId\",\"checked\":false},{\"label\":\"对应系统流程id\",\"prop\":\"sysFlowId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"sysFlowId\",\"fullName\":\"对应系统流程id\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"对应系统流程id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dynamic\",\"dictionaryType\":\"\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":223,\"renderKey\":1692154753729},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[],\"props\":{\"label\":\"fullName\",\"value\":\"id\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"sysFlowId\",\"checked\":false},{\"label\":\"项目状态（未提交，已提交，已安排）\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"项目状态（未提交，已提交，已安排）\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"项目状态（未提交，已提交，已安排）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461209642463140293\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":199,\"renderKey\":1692097510153},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461210186736998853\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已安排\",\"enCode\":\"2\"},{\"id\":\"461210111268886981\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"已提交\",\"enCode\":\"1\"},{\"id\":\"461210068797364677\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"未提交\",\"enCode\":\"0\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"status\",\"checked\":true},{\"label\":\"委托日期\",\"prop\":\"commissionDate\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"datePicker\",\"sortable\":false,\"width\":null,\"id\":\"commissionDate\",\"fullName\":\"委托日期\",\"__config__\":{\"jnpfKey\":\"datePicker\",\"label\":\"委托日期\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfDatePicker\",\"tagIcon\":\"icon-ym icon-ym-generator-date\",\"className\":[],\"defaultValue\":null,\"defaultCurrent\":false,\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"startTimeRule\":false,\"startTimeType\":1,\"startTimeTarget\":1,\"startTimeValue\":null,\"startRelationField\":\"\",\"endTimeRule\":false,\"endTimeType\":1,\"endTimeTarget\":1,\"endTimeValue\":null,\"endRelationField\":\"\",\"formId\":174,\"renderKey\":1692012000510},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请选择\",\"format\":\"YYYY-MM-DD\",\"startTime\":null,\"endTime\":null,\"disabled\":false,\"clearable\":true,\"__vModel__\":\"commissionDate\",\"checked\":true},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"inputNumber\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":189,\"renderKey\":1692015505454},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"sequence\",\"checked\":false},{\"label\":\"设计子表-相关单位名称\",\"prop\":\"tableField206-name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-name\",\"fullName\":\"设计子表-相关单位名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"相关单位名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":226,\"renderKey\":1692156744411,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":false},{\"label\":\"设计子表-单位类型，字典配置\",\"prop\":\"tableField206-unitTypeId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"select\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-unitTypeId\",\"fullName\":\"设计子表-单位类型，字典配置\",\"__config__\":{\"jnpfKey\":\"select\",\"label\":\"单位类型，字典配置\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfSelect\",\"tagIcon\":\"icon-ym icon-ym-generator-select\",\"className\":[],\"defaultValue\":\"\",\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"change\",\"dataType\":\"dictionary\",\"dictionaryType\":\"461458417995753541\",\"propsUrl\":\"\",\"propsName\":\"\",\"templateJson\":[],\"formId\":227,\"renderKey\":1692156774595,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"options\":[{\"id\":\"461458608811419717\",\"parentId\":\"0\",\"hasChildren\":false,\"children\":null,\"fullName\":\"委托单位\",\"enCode\":\"1\"}],\"props\":{\"label\":\"fullName\",\"value\":\"enCode\"},\"placeholder\":\"请选择\",\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"unitTypeId\",\"checked\":false},{\"label\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"prop\":\"tableField206-contactUser\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-contactUser\",\"fullName\":\"设计子表-客户联系人 （可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":229,\"renderKey\":1692156821147,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactUser\",\"checked\":false},{\"label\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"prop\":\"tableField206-contactPhone\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField206-contactPhone\",\"fullName\":\"设计子表-客户联系电话（可以手动填写或者从客户联系人带过来）\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_business_consult_project\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":230,\"renderKey\":1692156823245,\"isSubTable\":true,\"parentVModel\":\"tableField206\",\"relationTable\":\"ths_business_consult_project_relevant_unit\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"contactPhone\",\"checked\":false}],\"sortList\":[],\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\"}}");
        return sb.toString();
    }
    /** 表列表 */
    public static final String  getTableList(){
        StringBuilder sb = new StringBuilder();
        sb.append("[{\"relationField\":\"\",\"relationTable\":\"\",\"table\":\"ths_business_consult_project\",\"tableName\":\"咨询项目列表\",\"tableField\":\"\",\"typeId\":\"1\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"ID 主键\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"code\",\"field\":\"code\",\"fieldName\":\"项目编码\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"name\",\"field\":\"name\",\"fieldName\":\"项目名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"contract_id\",\"field\":\"contractId\",\"fieldName\":\"所属合同，management_center_contract合同ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"project_id\",\"field\":\"projectId\",\"fieldName\":\"所属建设项目\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"consulting_type_id\",\"field\":\"consultingTypeId\",\"fieldName\":\"咨询类型，（可研，估算，预算，结算，招标控制价）字典配置\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"editorial_type_id\",\"field\":\"editorialTypeId\",\"fieldName\":\"编审类型，数据字典配置\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"project_type_id\",\"field\":\"projectTypeId\",\"fieldName\":\"项目类型，数据字典配置\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"emergency_degree_id\",\"field\":\"emergencyDegreeId\",\"fieldName\":\"紧急程度\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"plan_complete_date\",\"field\":\"planCompleteDate\",\"fieldName\":\"计划完成时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"dept_id\",\"field\":\"deptId\",\"fieldName\":\"承办部门，所属部门组织架构\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"manager_user_id\",\"field\":\"managerUserId\",\"fieldName\":\"项目负责人系统管理中用户表ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"entrusted_company\",\"field\":\"entrustedCompany\",\"fieldName\":\"委托单位名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"entrusted_unit_user\",\"field\":\"entrustedUnitUser\",\"fieldName\":\"委托单位联系人\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"entrusted_unit_phone\",\"field\":\"entrustedUnitPhone\",\"fieldName\":\"委托单位联系电话\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"require_complete_date\",\"field\":\"requireCompleteDate\",\"fieldName\":\"要求完成时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"commission_date\",\"field\":\"commissionDate\",\"fieldName\":\"委托日期\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"remark\",\"field\":\"remark\",\"fieldName\":\"备注\",\"dataType\":\"varchar\",\"dataLength\":\"6000\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"creator_id\",\"field\":\"creatorId\",\"fieldName\":\"创建人\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"create_time\",\"field\":\"createTime\",\"fieldName\":\"创建时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"last_modify_time\",\"field\":\"lastModifyTime\",\"fieldName\":\"最后一次修改时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"tenant_id\",\"field\":\"tenantId\",\"fieldName\":\"租户ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"status\",\"field\":\"status\",\"fieldName\":\"项目状态（0未提交，1已提交，2已审批 ，3已安排已安排）\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"area_id\",\"field\":\"areaId\",\"fieldName\":\"地区id（清单计价的所属地区）\",\"dataType\":\"varchar\",\"dataLength\":\"10\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sys_flow_id\",\"field\":\"sysFlowId\",\"fieldName\":\"对应系统流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"stage_dict_id\",\"field\":\"stageDictId\",\"fieldName\":\"所属阶段ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowid\",\"field\":\"flowid\",\"fieldName\":\"流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]},{\"relationField\":\"id\",\"relationTable\":\"ths_business_consult_project\",\"table\":\"ths_business_consult_project_relevant_unit\",\"tableName\":\"项目参与方\",\"tableField\":\"consultId\",\"typeId\":\"0\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"ID 主键\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"consult_id\",\"field\":\"consultId\",\"fieldName\":\"外键咨询项目ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"unit_type_id\",\"field\":\"unitTypeId\",\"fieldName\":\"单位类型，字典配置\",\"dataType\":\"varchar\",\"dataLength\":\"255\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"name\",\"field\":\"name\",\"fieldName\":\"相关单位名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"contact_user\",\"field\":\"contactUser\",\"fieldName\":\"客户联系人 （可以手动填写或者从客户联系人带过来）\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"contact_phone\",\"field\":\"contactPhone\",\"fieldName\":\"客户联系电话（可以手动填写或者从客户联系人带过来）\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"creator_id\",\"field\":\"creatorId\",\"fieldName\":\"创建人\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"create_time\",\"field\":\"createTime\",\"fieldName\":\"创建时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"last_modify_time\",\"field\":\"lastModifyTime\",\"fieldName\":\"最后一次修改时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"tenant_id\",\"field\":\"tenantId\",\"fieldName\":\"租户ID\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"remark\",\"field\":\"remark\",\"fieldName\":\"备注\",\"dataType\":\"varchar\",\"dataLength\":\"6000\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]}]");
        return sb.toString();
    }
}
