package jnpf.controller.docking;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.xuyanwu.spring.file.storage.FileInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.docking.vm.*;
import jnpf.entity.*;
import jnpf.errors.BadRequestAlertException;
import jnpf.model.thsbusinessbudgetprojectdetail.ThsBusinessBudgetProjectDetailForm;
import jnpf.permission.service.UserService;
import jnpf.service.*;
import jnpf.service.docking.ClientService;
import jnpf.service.docking.CloudV1Service;
import jnpf.util.*;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static jnpf.util.UserProvider.cutToken;

/**
 * @auther wxy
 * @date 2023-09-10 10:00
 */

@Tag(name = "客户端对接接口", description = "客户端对接接口V1")
@Api(value = "客户端对接接口", tags = "客户端对接接口V1")
@RestController
@RequestMapping("/api/client/cloud/v1")
public class CloudV1Controller {

    @Autowired
    ConfigurableEnvironment environment;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private UserService userService;

    @Resource
    private ClientService clientService;

    @Resource
    private ThsBusinessBudgetProjectDetailService thsBusinessBudgetProjectDetailService;

    @Resource
    private ThsBusinessBudgetProjectMachineService thsBusinessBudgetProjectMachineService;

    @Resource
    private ThsBusinessBudgetProjectApprovalOpinionsService thsBusinessBudgetProjectApprovalOpinionsService;

    @Resource
    private ThsfileQdyBudgetService thsfileQdyBudgetService;

//    @Resource
//    private ThsAdminClient thsAdminClient;

    @Resource
    private CloudV1Service cloudV1Service;


    private final Logger log = LoggerFactory.getLogger(CloudV1Controller.class);

    @Autowired
    private ThsBusinessBudgetProjectDetailRelationService thsBusinessBudgetProjectDetailRelationService;


    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectFlowDetailsUserService thsBusinessBudgetProjectFlowDetailsUserService;

    @Autowired
    private ThsBusinessBudgetProjectFlowHistoryService thsBusinessBudgetProjectFlowHistoryService;


    @Operation(summary = "保存客户端打开信息")
    @ApiOperation(value = "保存客户端打开信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "queryParam", dataType = "String")
    })
    @GetMapping("/saveOpenStatus")
    public ResponseEntity<ReturnResult> saveOpenStatus(@RequestParam String openId) {
        ReturnResult result = new ReturnResult();
        try {
            clientService.saveOpenStatus(openId, "save");
            result.setMessage("保存成功");
            result.setCode("200");
            result.setSuccess(true);
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setMessage("保存失败！");
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }


    @Operation(summary = "校验token并获取token信息")
    @ApiOperation(value = "校验token并获取token信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "token值", required = true, paramType = "queryParam", dataType = "String")
    })
    @GetMapping("/checkAndGetTokenInfo")
    public ResponseEntity<ReturnResult<JWTTokenVM>> checkAndGetTokenInfo(@RequestParam String token) {
        ReturnResult<JWTTokenVM> result = new ReturnResult<>();
        try {
//            Map<String, Object> tokenInfo = SecurityUtils.getTokenInfo(token);
            String tokens = cutToken(token);
            UserInfo userInfo = null;
            if (token != null && StpUtil.getLoginIdByToken(tokens) != null) {
                userInfo = (UserInfo) StpUtil.getTokenSessionByToken(tokens).get("userInfo");
            }

            // log.error(JSONUtil.toJsonStr(userInfo));
            JWTTokenVM vm = new JWTTokenVM();
            vm.setToken(token);
            vm.setId(userInfo.getUserId());
            vm.setAccountName(userInfo.getUserId());
            vm.setUserName(userInfo.getUserName());
            vm.setExpiration(userInfo.getOverdueTime());
            System.out.println(vm);
            result.setData(vm);
            result.setSuccess(true);
            result.setCode("200");
            result.setMessage("token校验成功！");
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setMessage("token校验失败！");
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }


    @Operation(summary = "工程详情获取接口，获取该工程及该工程下所有子级（树结构）")
    @ApiOperation(value = "工程详情获取接口，获取该工程及该工程下所有子级（树结构）")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "projectId", value = "工程文件ID", required = true, paramType = "queryParam", dataType = "String")
    })
    @GetMapping("/getProjectTreeInfo")
    public ResponseEntity<ReturnResult<List<CloudDocVM>>> getProjectTreeInfo(@RequestParam String projectId) {
        log.info("工程文件获取信息");
        ReturnResult<List<CloudDocVM>> result = new ReturnResult<>();
        try {
            List<CloudDocVM> list = cloudV1Service.getClientProjectInfoTree(projectId);
            result.setData(list);
            result.setCode("200");
            result.setSuccess(true);
            if (!CollectionUtils.isEmpty(list)) {
                // 单位工程
                result.setMessage(list.get(0).getLockedInfo().getAllowEditMessage());
            }

        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setCode("400");
            result.setSuccess(false);
            result.setMessage("获取工程文件失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }


    @Operation(summary = "工程文件下载接口")
    @ApiOperation(value = "工程文件下载接口")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "projectId", value = "具体工程文件ID", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "aduitRole", value = "当前是编制阶段还是审核阶段", required = true, paramType = "queryParam", dataType = "Integer"),
            @ApiImplicitParam(name = "fileType", value = "文件类型 0：编审文件 1：原始送审文件 2：关系文件", required = true, paramType = "queryParam", dataType = "String")
    })
    @GetMapping("/downloadProjectFile")
    public void downloadProjectFile(@RequestParam(value = "projectId") String projectId,
                                    @RequestParam(value = "aduitRole", required = false) Integer aduitRole,
                                    @RequestParam(value = "fileType", required = false) Integer fileType) throws IOException {

        HttpServletResponse response = ServletUtil.getResponse();
        log.info("工程文件开始下载");
        boolean splitFileFlag = false;
        try {
            Optional<ThsBusinessBudgetProjectDetailEntity> detailEntityOptional = Optional.ofNullable(thsBusinessBudgetProjectDetailService.getInfo(projectId));
            if (detailEntityOptional.isPresent()) {
                // 如果是 文件后缀是qdy3，说明是建行文件，需要拆分
                String lowerCaseFileName = ObjectUtil.isNotEmpty(detailEntityOptional.get().getFileSavePath()) ? detailEntityOptional.get().getFileSavePath().toLowerCase() : "";
                if (lowerCaseFileName.endsWith(".qdy3")) {
                    splitFileFlag = true;
                }
            }
            if (splitFileFlag) {

                ThsBusinessBudgetProjectDetailForm budgetProjectDetailsDTO = JsonUtil.getJsonToBean(detailEntityOptional.get(), ThsBusinessBudgetProjectDetailForm.class);
                if (ObjectUtil.isEmpty(budgetProjectDetailsDTO)) return;
                String path = budgetProjectDetailsDTO.getFileSavePath();
                String fileSuffix = ".Qdy6";
                if (!StringUtils.isEmpty(path)) {
                    fileSuffix = path.substring(path.lastIndexOf("."));
                }
                downloadTempFile(response, budgetProjectDetailsDTO.getFileSavePath(), budgetProjectDetailsDTO.getId() + fileSuffix);
            } else {
                if (aduitRole == null)
                    aduitRole = 0;
                if (ObjectUtil.isEmpty(fileType))
                    fileType = 0;
                if (fileType.equals(0)) {

                    if (aduitRole.equals(0)) {

                        ThsBusinessBudgetProjectDetailForm budgetProjectDetailsDTO = JsonUtil.getJsonToBean(detailEntityOptional.get(), ThsBusinessBudgetProjectDetailForm.class);
                        if (ObjectUtil.isEmpty(budgetProjectDetailsDTO)) return;
                        String path = budgetProjectDetailsDTO.getFileSavePath();
                        String fileSuffix = ".Qdy6";
                        if (!StringUtils.isEmpty(path)) {
                            fileSuffix = path.substring(path.lastIndexOf("."));
                        }
                        downloadTempFile(response, budgetProjectDetailsDTO.getFileSavePath(), budgetProjectDetailsDTO.getId() + fileSuffix);

                    } else {
                        ThsBusinessBudgetProjectDetailForm budgetProjectDetailsDTO = JsonUtil.getJsonToBean(thsBusinessBudgetProjectDetailService.getInfoByAuditFileId(projectId), ThsBusinessBudgetProjectDetailForm.class);
                        if (ObjectUtil.isEmpty(budgetProjectDetailsDTO)) return;
                        // 下载审核文件
                        downloadTempFile(response, budgetProjectDetailsDTO.getAuditFileSavePath(), projectId + ".Qdy6");
                    }
                } else if (fileType.equals(1)) {
                    // 下载原始送审文件
                    ThsBusinessBudgetProjectDetailForm budgetProjectDetailsDTO = JsonUtil.getJsonToBean(thsBusinessBudgetProjectDetailService.getInfoByOriginalSendFileId(projectId), ThsBusinessBudgetProjectDetailForm.class);
                    if (ObjectUtil.isEmpty(budgetProjectDetailsDTO)) return;
                    downloadTempFile(response, budgetProjectDetailsDTO.getOriginalSendFileSavePath(), projectId + ".Qdy6");
                } else if (fileType.equals(2)) {
                    // 下载关系文件
                    ThsBusinessBudgetProjectDetailForm budgetProjectDetailsDTO = JsonUtil.getJsonToBean(thsBusinessBudgetProjectDetailService.getInfoByRelationalFileId(projectId), ThsBusinessBudgetProjectDetailForm.class);
                    if (ObjectUtil.isEmpty(budgetProjectDetailsDTO)) return;
                    downloadTempFile(response, budgetProjectDetailsDTO.getRelationalFileSavePath(), projectId);
                }
            }
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.setContentType("application/json; charset=UTF-8");
            response.getWriter().write("{\"code\": 400, \"message\": \"文件下载失败\"}");
            log.error(e.getMessage(), e);
        }

    }


    /**
     * 下载临时生成的文件
     *
     * @param response 响应
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @return
     * <AUTHOR>
     * @date 2023/6/13 10:52
     */
    public void downloadTempFile(HttpServletResponse response, String filePath, String fileName) throws IOException {
        // 设置内容格式
        response.setContentType("application/octet-stream");
        try {
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        } catch (UnsupportedEncodingException e) {
            log.error("文件编码异常 : {}", e.getMessage());
        }
        if (StringUtil.isEmpty(filePath)) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.setContentType("application/json; charset=UTF-8");
            response.getWriter().write("{\"code\": 400, \"message\": \"文件下载失败,文件不存在\"}");
            return;
        }

        Path path = Paths.get(filePath);
        String directoryPath = path.getParent().normalize() + File.separator;
        String CurrentFileName = path.getFileName().toString();
        // 将文件流写入输出流

        byte[] inputStream = FileUploadUtils.downloadFileByte(directoryPath, CurrentFileName, false);

        if (inputStream != null) {
            try {
                InputStream is = new ByteArrayInputStream(inputStream);
                IOUtils.copy(is, response.getOutputStream());

                response.getOutputStream().flush();
            } catch (FileNotFoundException e) {
                log.error("文件未找到异常 : {}", e.getMessage());
            } catch (IOException e) {
                log.error("文件读写异常 : {}", e.getMessage());
            }
        } else {
            log.error("文件未找到异常 : {}", filePath);
        }
    }


    /**
     * @param request
     * @description: 提交当前正在编辑的文件
     * @param: @param uploadFileVM
     * @return: org.springframework.http.ResponseEntity<jnpf.docking.vm.ReturnResult < jnpf.docking.vm.FileLockedVM>>
     * @author: wxy
     * @date: 2024/8/21 9:14
     */
    @Operation(summary = "工程文件上传接口")
    @ApiOperation(value = "工程文件上传接口")
    @PostMapping("/uploadProjectBase64")
    @Transactional
    public ResponseEntity<ReturnResult<FileLockedVM>> uploadProjectBase64(@RequestBody UploadFileVM uploadFileVM, HttpServletRequest request) {
        log.info("工程文件开始上传");
        String file = uploadFileVM.getFile();
        String fileType = uploadFileVM.getFileType();
        String fileId = uploadFileVM.getFileId();
        String docVersion = uploadFileVM.getDocVersion();
        String checkSum = uploadFileVM.getCheckSum();
        String clientId = uploadFileVM.getClientId();
        String clientName = uploadFileVM.getClientName();
        String isUnlock = uploadFileVM.getIsUnlock();
        String editStatus = uploadFileVM.getEditStatus();
        String remark = uploadFileVM.getRemark();
        String totalMoney = uploadFileVM.getTotalMoney();
        String sendMoney = uploadFileVM.getSendMoney();
        if (ObjectUtil.isNotEmpty(sendMoney) && sendMoney.equals("-1")) {
            sendMoney = "0";
        }
        String auditMoney = uploadFileVM.getAuditMoney();
        if (ObjectUtil.isNotEmpty(auditMoney) && auditMoney.equals("-1")) {
            auditMoney = "0";
        }
        String informationPriceDate = uploadFileVM.getInformationPriceDate();
        String ignoreLock = uploadFileVM.getIgnoreLock();
        String projectName = uploadFileVM.getProjectName();
        String buildArea = uploadFileVM.getBuildArea();
        ReturnResult<FileLockedVM> result = null;

        Optional<ThsBusinessBudgetProjectDetailEntity> detailEntityOptional = Optional.ofNullable(thsBusinessBudgetProjectDetailService.getInfo(fileId));
        if (!detailEntityOptional.isPresent()) return null;
        String filePath = "";
        if (ObjectUtil.isNotEmpty(detailEntityOptional.get().getFileSavePath())) {
            int lastSlashIndex = filePath.lastIndexOf('/');
            filePath = lastSlashIndex != -1 ? filePath.substring(0, detailEntityOptional.get().getFileSavePath().lastIndexOf('/') + 1) : "";
        }
        if (ObjectUtil.isEmpty(filePath)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateStr = format.format(new Date());
            filePath = BusinessConstantValues.BUDGET_FILES_ROOT_PATH + "/" + dateStr + "/";
        }

        FileInfo relationalFileInfo = null;
        FileInfo originalSendFileInfo = null;
        FileInfo auditFileInfo = null;
        FileInfo fileInfo = null;
        // 提交关系文件
        if (ObjectUtil.isNotEmpty(uploadFileVM.getRelationalFile())) {
            relationalFileInfo = cloudV1Service.uploadBudgetFileBase64(uploadFileVM.getRelationalFile(), "编审关系文件", filePath);
        }
        // 拆分后的文件提交原始送审文件
        if (ObjectUtil.isNotEmpty(uploadFileVM.getOriginalSendFile())) {
            originalSendFileInfo = cloudV1Service.uploadBudgetFileBase64(uploadFileVM.getOriginalSendFile(), "协同计价原始送审文件", filePath);

        }
        if (uploadFileVM.getAuditRole().equals(0)) {
            // 编制文件作为主文件提交编制文件
            result = cloudV1Service.uploadProjectBase64(file, fileType, fileId, docVersion, checkSum, clientId, clientName, isUnlock, editStatus, remark, totalMoney, sendMoney, auditMoney, informationPriceDate, ignoreLock, projectName, filePath, request);
            // 拆分后的文件提交审核文件
            if (ObjectUtil.isNotEmpty(uploadFileVM.getAuditFile())) {
                auditFileInfo = cloudV1Service.uploadBudgetFileBase64(uploadFileVM.getAuditFile(), "协同计价审核文件", filePath);
            }

        }

        ThsBusinessBudgetProjectDetailEntity budgetProjectDetailEntity = thsBusinessBudgetProjectDetailService.getInfo(fileId);


        if (ObjectUtil.isNotEmpty(auditFileInfo)) {
            // 5.0版本上传文件接口，没有返回id
            if (StringUtil.isEmpty(auditFileInfo.getId())) {
                auditFileInfo.setId(UUID.randomUUID().toString());
            }
            String auditFileId = ObjectUtil.isNotEmpty(budgetProjectDetailEntity.getAuditFileId()) ? budgetProjectDetailEntity.getAuditFileId() : auditFileInfo.getId();
            budgetProjectDetailEntity.setAuditFileId(auditFileId);
            budgetProjectDetailEntity.setAuditFileHash(uploadFileVM.getAuditFileHash());
            String auditFileVersion = uploadFileVM.getAuditFileVersion();
            if (auditFileVersion != null && !auditFileVersion.isEmpty()) {
                budgetProjectDetailEntity.setAuditFileVersion(Integer.valueOf(auditFileVersion));
            } else {
                // 处理 null 或空字符串的情况，视情况而定
                budgetProjectDetailEntity.setAuditFileVersion(0); // 或者其他默认值
            }
            budgetProjectDetailEntity.setAuditFileSavePath(auditFileInfo.getPath() + auditFileInfo.getFilename());
        }

        if (ObjectUtil.isNotEmpty(relationalFileInfo)) {
            // 5.0版本上传文件接口，没有返回id
            if (StringUtil.isEmpty(relationalFileInfo.getId())) {
                relationalFileInfo.setId(UUID.randomUUID().toString());
            }
            String relationalFileId = ObjectUtil.isNotEmpty(budgetProjectDetailEntity.getRelationalFileId()) ? budgetProjectDetailEntity.getRelationalFileId() : relationalFileInfo.getId();
            budgetProjectDetailEntity.setRelationalFileId(relationalFileId);
            budgetProjectDetailEntity.setRelationalFileHash(uploadFileVM.getRelationalFileHash());
            budgetProjectDetailEntity.setRelationalFileVersion(uploadFileVM.getRelationalFileVersion());
            budgetProjectDetailEntity.setRelationalFileSavePath(relationalFileInfo.getPath() + relationalFileInfo.getFilename());
        }

        if (ObjectUtil.isNotEmpty(originalSendFileInfo)) {
            // 5.0版本上传文件接口，没有返回id
            if (StringUtil.isEmpty(originalSendFileInfo.getId())) {
                originalSendFileInfo.setId(UUID.randomUUID().toString());
            }
            String originalSendFileId = ObjectUtil.isNotEmpty(budgetProjectDetailEntity.getOriginalSendFileId()) ? budgetProjectDetailEntity.getOriginalSendFileId() : originalSendFileInfo.getId();
            budgetProjectDetailEntity.setOriginalSendFileId(originalSendFileId);
            budgetProjectDetailEntity.setOriginalSendFileHash(uploadFileVM.getOriginalSendFileHash());
            budgetProjectDetailEntity.setOriginalSendFileVersion(Integer.valueOf(uploadFileVM.getOriginalSendFileVersion()));
            budgetProjectDetailEntity.setOriginalSendFileSavePath(originalSendFileInfo.getPath() + originalSendFileInfo.getFilename());
        }

        if (ObjectUtil.isNotEmpty(fileInfo)) {
            budgetProjectDetailEntity.setFileSavePath(fileInfo.getPath() + fileInfo.getFilename());
            budgetProjectDetailEntity.setFileName(fileInfo.getFilename());
            budgetProjectDetailEntity.setChecksum(checkSum);
            budgetProjectDetailEntity.setFileVersion(budgetProjectDetailEntity.getFileVersion() + 1);
            budgetProjectDetailEntity.setFileSize(new BigDecimal(fileInfo.getSize() + "").divide(new BigDecimal("1024"), 2, RoundingMode.HALF_UP));
        }

        // 记录本次是以编制文件提交还是审核文件作为主文件提交的 0：编制文件作为主文件提交的，1：审核文件作为主文件提交的 （判断两个文件都存在是是否文件交换打开）
        budgetProjectDetailEntity.setItemUnit(uploadFileVM.getDw());
        if (!StringUtils.isEmpty(buildArea)) {
            budgetProjectDetailEntity.setBuildArea(new BigDecimal(buildArea));
        }
//        budgetProjectDetailEntity.setLastAuditRole(uploadFileVM.getAuditRole());
        // 提交文件永远作为编制人提交永远是0
        budgetProjectDetailEntity.setLastAuditRole(0);

        //  页面打开时无需获取客户端提交的送审造价，采用页面编辑的送审造价
        // 原始送审造价
        // if (ObjectUtil.isNotEmpty(uploadFileVM.getSendMoney())) {
        //     BigDecimal bigDecimalSendMoney = new BigDecimal(sendMoney);
        //     if (bigDecimalSendMoney.compareTo(new BigDecimal(0)) >= 0) {
        //         budgetProjectDetailEntity.setSendTotalPrice(bigDecimalSendMoney);
        //     }
        // }
        try {
            //  获取第一次提交流程
            List<ThsBusinessBudgetProjectFlowHistoryEntity> projectFlowHistoryList = thsBusinessBudgetProjectFlowHistoryService.findByDetailIdEqualsOrderBySequenceAsc(budgetProjectDetailEntity.getId());
            //  判断是否提交过流程
            if (projectFlowHistoryList.isEmpty()) {
                //  提交将编制造价付给审查造价
                budgetProjectDetailEntity.setAuditMoney(budgetProjectDetailEntity.getTotalPrice());
            } else {
                //  获取第一个流程的金额
                ThsBusinessBudgetProjectFlowHistoryEntity first = projectFlowHistoryList.get(0);
                budgetProjectDetailEntity.setAuditMoney(first.getTotalPrice());
            }
            thsBusinessBudgetProjectDetailService.saveOrUpdate(budgetProjectDetailEntity);
            thsBusinessBudgetProjectDetailService.calcBusinessBudgetProjectDetailAllMoneyEx(budgetProjectDetailEntity.getBudgetProjectId());

            // 如果是编制完成自动提交到一审
            if (StringUtil.isNotEmpty(editStatus) && editStatus.equals("1") && ObjectUtil.isNotEmpty(budgetProjectDetailEntity.getStatus()) && budgetProjectDetailEntity.getStatus().equals(1)) {

                //  编制的同时提交审核
                Map<String, Object> resMap = cloudV1Service.submitApproval(remark, budgetProjectDetailEntity, ObjectUtil.isNotEmpty(detailEntityOptional.get().getTotalPrice()) ? detailEntityOptional.get().getTotalPrice() : budgetProjectDetailEntity.getTotalPrice());
                if (!(boolean) resMap.get("operateFlag")) {
                    throw new RuntimeException("提交审核失败" + resMap.get("operateMessage"));
                }
            }

        } catch (RuntimeException e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RuntimeException(e);
        }
        return ResponseEntity.ok(result);
    }


    /**
     * 操作文件锁-客户端锁定编辑
     *
     * @return
     */
    @ApiOperation(value = "操作文件锁 单独操作时使用")
    @Operation(summary = "操作文件锁 单独操作时使用")
    @PostMapping("/operationFileLock")
    public ResponseEntity<ReturnResult<FileLockedVM>> operationFileLock(@RequestBody @Valid OperationFileLockVM vm) {
        log.info("工程文件操作锁");
        ReturnResult<FileLockedVM> result = new ReturnResult<>();
        FileLockedVM lockedVM = new FileLockedVM();
        try {
            Optional<ThsBusinessBudgetProjectDetailEntity> listServiceOne = Optional.ofNullable(thsBusinessBudgetProjectDetailService.getInfo(vm.getFileId()));
            if (listServiceOne.isPresent()) {
                UserInfo userInfo = userProvider.get();
                String userId = null;
                if (userInfo.getUserId() != null) {
                    userId = userInfo.getUserId();
                } else {
                    throw new BadRequestAlertException("当前请求获取不到用户信息", "userId not found", "userId not found");
                }
                Integer auditRole = 0;
                if (listServiceOne.get().getStatus() == 2) {
                    auditRole = 1;
                }

                boolean splitFileFlag = false;
                String lowerCaseFileName = ObjectUtil.isNotEmpty(listServiceOne.get().getFileSavePath()) ? listServiceOne.get().getFileSavePath().toLowerCase() : "";
                if (lowerCaseFileName.endsWith(".qdy3")) {
                    splitFileFlag = true;
                }
                if ((auditRole == 1) && (!splitFileFlag)) {
                    result.setCode("403");
                    result.setSuccess(false);
                    result.setMessage("审核阶段不能锁定编辑文件");
                    return ResponseEntity.ok(result);
                }

                ReturnResult returnResult = cloudV1Service.fileLockPermissionVerify(listServiceOne.get());
                if (!returnResult.getSuccess()) {
                    result.setCode("403");
                    result.setSuccess(false);
                    result.setMessage(returnResult.getMessage());
                    return ResponseEntity.ok(result);
                }
                // 创建机器记录

                ThsBusinessBudgetProjectMachineEntity budgetProjectMachine = thsBusinessBudgetProjectMachineService.findByMachineCodeEqualsAndMachineUserEqualsAndIsDeleteEquals(vm.getLockedClientId(), userId, false);
                if (budgetProjectMachine == null) {
                    budgetProjectMachine = new ThsBusinessBudgetProjectMachineEntity();
                    budgetProjectMachine.setMachineCode(vm.getLockedClientId());
                    budgetProjectMachine.setMachineName(vm.getLockedClientName());
                    budgetProjectMachine.setMachineUser(userId);
                    budgetProjectMachine.setIsDelete(false);
                    budgetProjectMachine.setCreator(vm.getLockedClientId());
                    budgetProjectMachine.setCreationTime(Date.from(Instant.now()));
                    thsBusinessBudgetProjectMachineService.save(budgetProjectMachine);
                }
                ThsBusinessBudgetProjectDetailForm docListDTO = JsonUtil.getJsonToBean(listServiceOne.get(), ThsBusinessBudgetProjectDetailForm.class);
                // 校验版本是否一致
                // 获取文件具体信息

                if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                    if (ObjectUtil.isEmpty(docListDTO.getFileVersion())) {
                        lockedVM.setDocVersion(0);
                        docListDTO.setFileVersion(0);
                    } else {
                        lockedVM.setDocVersion(docListDTO.getFileVersion());
                    }
                } else {
                    if (ObjectUtil.isEmpty(docListDTO.getAuditFileVersion())) {
                        lockedVM.setDocVersion(0);
                        docListDTO.setAuditFileVersion(0);
                    } else {
                        lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                    }
                }

                if (!StringUtils.isEmpty(docListDTO.getLockedBy())) {
                    lockedVM.setLocked(true);
                    lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                    lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                    lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                    lockedVM.setLockedUserId(docListDTO.getLockedBy());
                    lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                } else {
                    lockedVM.setLocked(false);
                }
                if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                    lockedVM.setChecksum(docListDTO.getChecksum());
                } else {
                    lockedVM.setChecksum(docListDTO.getAuditFileHash());
                }
                lockedVM.setEditStatus(docListDTO.getStatus().toString());
                // 锁定时校验
                if (vm.getIsLocked()) {
                    if ((auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR) && vm.getDocVersion() != docListDTO.getFileVersion())
                            || (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_AUDITOR) && vm.getDocVersion() != docListDTO.getAuditFileVersion())) {
                        result.setMessage("请获取最新版本后再锁定！");
                        result.setSuccess(false);
                        result.setCode("403");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    }
                    // 用户权限校验
                    if (!StringUtils.isEmpty(docListDTO.getLockedBy()) && !docListDTO.getLockedBy().contains(userId)) {
                        result.setMessage("无锁定权限，无法锁定！");
                        result.setSuccess(false);
                        result.setCode("403");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    }

                    String receiverId = "";
                    if (!StringUtils.isEmpty(receiverId) && !receiverId.contains(userId)) {
                        result.setMessage("您不是当前处理人，无法锁定！");
                        result.setSuccess(false);
                        result.setCode("403");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    }

                    if (docListDTO.getStatus().equals(1)) {
                        result.setMessage("文件编制完成，不允许锁定！");
                        result.setSuccess(false);
                        result.setCode("403");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        result.setCode("200");
                        return ResponseEntity.ok(result);
                    }
                }

                if (StringUtils.isEmpty(docListDTO.getLockedBy())) {
                    if (vm.getIsLocked()) {
                        docListDTO.setLockedBy(userId);
                        docListDTO.setLockedDate(Date.from(Instant.now()));
                        docListDTO.setLockedMachine(budgetProjectMachine.getId());
                        docListDTO = thsBusinessBudgetProjectDetailService.saveBudgetProjectDetailForm(docListDTO);

                        result.setCode("200");
                        result.setMessage("锁定成功");
                        result.setSuccess(true);
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(true);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    } else {
                        result.setMessage("项目文件未锁定，无需解锁");
                        result.setSuccess(true);
                        result.setCode("200");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    }
                } else {
                    if (!vm.getIsLocked()) {
                        // 已经解锁的不用重复解锁
                        if (StringUtils.isEmpty(docListDTO.getLockedBy())) {
                            result.setMessage("该文件已解锁，请更新！");
                            result.setSuccess(false);
                            lockedVM = new FileLockedVM();
                            lockedVM.setLocked(false);
                            if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                                lockedVM.setChecksum(docListDTO.getChecksum());
                                lockedVM.setDocVersion(docListDTO.getFileVersion());
                            } else {
                                lockedVM.setChecksum(docListDTO.getAuditFileHash());
                                lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                            }
                            lockedVM.setEditStatus(docListDTO.getStatus().toString());
                            lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                            result.setData(lockedVM);
                            return ResponseEntity.ok(result);
                        }
                        // 解锁时校验终端ID
                        if (!vm.getLockedClientId().equals(budgetProjectMachine.getMachineCode())) {
                            result.setMessage("客户端不一致，解锁失败");
                            result.setSuccess(false);
                            result.setCode("403");
                            lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                            result.setData(lockedVM);
                            return ResponseEntity.ok(result);
                        }
                        // 解锁时校验用户ID
                        if (!userId.equals(docListDTO.getLockedBy())) {
                            result.setMessage("当前登录用户无解锁权限！");
                            result.setSuccess(false);
                            result.setCode("403");
                            lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                            result.setData(lockedVM);
                            return ResponseEntity.ok(result);
                        }
                        // 解锁
                        docListDTO.setLockedMachine(null);
                        docListDTO.setLockedDate(null);
                        docListDTO.setLockedBy(null);
                        if (vm.getEditStatus() != null) {
                            docListDTO.setStatus(Integer.valueOf(vm.getEditStatus()));
                        }
                        docListDTO = thsBusinessBudgetProjectDetailService.saveBudgetProjectDetailForm(docListDTO);
                        result.setMessage("解锁成功");
                        result.setCode("200");
                        result.setSuccess(true);
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(false);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    } else {
                        // 锁定
                        docListDTO.setLockedBy(userId);
                        docListDTO.setLockedDate(Date.from(Instant.now()));
                        docListDTO.setLockedMachine(budgetProjectMachine.getId());
                        docListDTO = thsBusinessBudgetProjectDetailService.saveBudgetProjectDetailForm(docListDTO);

                        result.setMessage("锁定成功");
                        result.setSuccess(true);
                        result.setCode("200");
                        lockedVM = new FileLockedVM();
                        lockedVM.setLocked(true);
                        if (auditRole.equals(BusinessConstantValues.AUDIT_ROLE_EDITOR)) {
                            lockedVM.setChecksum(docListDTO.getChecksum());
                            lockedVM.setDocVersion(docListDTO.getFileVersion());
                        } else {
                            lockedVM.setChecksum(docListDTO.getAuditFileHash());
                            lockedVM.setDocVersion(docListDTO.getAuditFileVersion());
                        }
                        lockedVM.setEditStatus(docListDTO.getStatus().toString());
                        lockedVM.setLockedClientId(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineCode()) ? budgetProjectMachine.getMachineCode() : "");
                        lockedVM.setLockedClientName(ObjectUtil.isNotEmpty(budgetProjectMachine.getMachineName()) ? budgetProjectMachine.getMachineName() : "");
                        lockedVM.setLockedUserId(docListDTO.getLockedBy());
                        lockedVM.setLockedUserName(ObjectUtil.isNotEmpty(docListDTO.getLockedBy()) ? userService.getInfo(docListDTO.getLockedBy()).getRealName() : null);
                        lockedVM.setLockedDate(docListDTO.getLockedDate().toInstant());
                        lockedVM = cloudV1Service.lockedToVMWithStatus(lockedVM, docListDTO);
                        result.setData(lockedVM);
                        return ResponseEntity.ok(result);
                    }

                }
            } else {
                result.setMessage("fileId不存在");
                result.setSuccess(false);
                result.setCode("400");
                lockedVM = new FileLockedVM();
                result.setData(lockedVM);
            }
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            lockedVM = new FileLockedVM();
            result.setData(lockedVM);
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("锁定/解锁文件失败！");
            lockedVM = new FileLockedVM();
            result.setData(lockedVM);
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }


    @Operation(summary = "材料库中转调用Python接口")
    @ApiOperation(value = "材料库中转调用Python接口")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "keyWord", value = "keyWord", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "queryParam", dataType = "String"),

    })
    @GetMapping("/queryEngineeringPriceData")
    public ResponseEntity<Object> queryEngineeringPriceData(@RequestParam(value = "keyWord", required = false) String keyWord,
                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                            @RequestParam(value = "endTime", required = false) String endTime) {
        log.debug("材料库中转调用Python接口: keyWord{} ,startTime{},endTime{}", keyWord, startTime, endTime);
        environment.getProperty("");
        String pyApiUrl = environment.getProperty("config.thsware.data-center.py-api-url");
        if (StrUtil.isBlank(pyApiUrl)) {
            log.error("配置错误, pyApiUrl为空, 请检查Nacos中system-config.yaml");
            return ResponseEntity.internalServerError().body("配置错误");
        }
        UserInfo userInfo = userProvider.get();
        try {
            JSONObject params = new JSONObject();
            params.put("instruction", "C0301");
            params.put("keyword", keyWord);
            params.put("tenant_id", userInfo.getTenantId());
            if (StringUtil.isNotEmpty(startTime)) {
                params.put("budget_time_begin", startTime);
            }
            if (StringUtil.isNotEmpty(endTime)) {
                params.put("budget_time_end", endTime);
            }
            // 创建一个HttpRequest对象，并设置Authorization头部
            HttpRequest request = HttpRequest.post(pyApiUrl)
                    .header("Authorization", userProvider.get().getToken())
                    .body(params.toString());

            // 发送POST请求并获取HttpResponse
            HttpResponse response = request.execute();
            // 获取响应体作为字符串
            String result = response.body();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("调用Python服务错误, URL: {}", pyApiUrl, e);
            return ResponseEntity.internalServerError().body("调用错误");
        }
    }

    /**
     * @description: 根据单位工程ID获取这个项目的所有树结构，导出QDG接口
     * @param: @param projectId
     * @return: org.springframework.http.ResponseEntity<java.lang.Object>
     * @author: wxy
     * @date: 2024/4/13 14:55
     */
    @Operation(summary = "根据单位工程ID获取这个项目的所有树结构，导出QDG接口")
    @ApiOperation(value = "根据单位工程ID获取这个项目的所有树结构，选中的工程导出")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "projectId", value = "projectId", required = true, paramType = "queryParam", dataType = "String"),
    })
    @GetMapping("/getExportProjectTreeInfo")
    public ResponseEntity<Object> getExportProjectTreeInfo(@RequestParam String projectId) {
        log.info("工程文件获取信息");
        ReturnResult<List<Map<String, Object>>> result = new ReturnResult<>();
        try {
            List<Map<String, Object>> list = cloudV1Service.getClientProjectInfoTreeForExportQDG(projectId);

            List<Map<String, Object>> resultMap = TreeUtils.getEasyTreesMap(list, "id", "pid", "children");
            result.setData(resultMap);
            result.setSuccess(true);
            result.setCode("200");
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("获取工程文件失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);

    }


    @Operation(summary = "中转清单查询接口V1")
    @ApiOperation(value = "中转清单查询接口V1")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "keyWord", value = "keyWord", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "queryParam", dataType = "String"),

    })
    @PostMapping("/inventoryQuery")
    public ResponseEntity<Object> AiBudgetQuery(@Valid @RequestBody Map<String, Object> map) {
        log.debug("中转清单查询接口V1: {}", map);

        environment.getProperty("");
        String pyApiUrl = environment.getProperty("config.thsware.data-center.py-api-url");
        if (StrUtil.isBlank(pyApiUrl)) {
            log.error("配置错误, pyApiUrl为空, 请检查Nacos中system-config.yaml");
            return ResponseEntity.internalServerError().body("配置错误");
        }
        UserInfo userInfo = userProvider.get();
        try {
            JSONObject params = new JSONObject(map);
            params.put("instruction", "C0203");
            params.put("tenant_id", userInfo.getTenantId());

            // 创建一个HttpRequest对象，并设置Authorization头部
            HttpRequest request = HttpRequest.post(pyApiUrl)
                    .header("Authorization", userProvider.get().getToken())
                    .body(params.toString());

            // 发送POST请求并获取HttpResponse
            HttpResponse response = request.execute();
            // 获取响应体作为字符串
            String result = response.body();

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("调用Python服务错误, URL: {}", pyApiUrl, e);
            return ResponseEntity.internalServerError().body("调用错误");
        }
    }

    /**
     * @param xmjjId
     * @param xmjjCode
     * @description:
     * @param: @param detailId
     * @return: org.springframework.http.ResponseEntity<java.lang.Object>
     * @author: wxy
     * @date: 2024/5/6 16:38
     */
    @GetMapping("/getProjectApprovalOpinionsByDetailIdAndXMJJ")
    public ResponseEntity<Object> getProjectApprovalOpinionsByDetailIdAndXMJJ(@RequestParam(value = "detailId", required = true) String detailId,
                                                                              @RequestParam(value = "xmjjId", required = true) String xmjjId,
                                                                              @RequestParam(value = "xmjjCode", required = false) String xmjjCode) {

        log.info("清单审批意见");
        ReturnResult<List<Map<String, Object>>> result = new ReturnResult<>();
        try {
            List<Map<String, Object>> list = thsBusinessBudgetProjectApprovalOpinionsService.getListByDetailIdAndXMJJ(detailId, xmjjId);
            result.setData(list);
            result.setSuccess(true);
            result.setCode("200");
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("获取审批意见失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * @description: 获取还有审核标识的XMJJIDList
     * @param: @param null
     * @return:
     * @author: wxy
     * @date: 2024/6/25 13:46
     */

    @GetMapping("/getHasApprovalOpinionsXMJJIdsByDetailId")
    public ResponseEntity<Object> getHasApprovalOpinionsXMJJIdsByDetailId(@RequestParam(value = "detailId", required = true) String detailId) {

        log.info("含有有审批意见的清单的数据");
        ReturnResult<Map<String, Object>> result = new ReturnResult<>();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<ThsfileQdyBudgetEntity> thsfileQdyBudgetEntityList = thsfileQdyBudgetService.getHasApprovalOpinionsListByDetailId(detailId);

            //  根据条件查询所有审核意见
            List<ThsBusinessBudgetProjectApprovalOpinionsEntity> listByDetailId = thsBusinessBudgetProjectApprovalOpinionsService.getListByDetailId(detailId);
            List<Integer> iIdList = listByDetailId.stream().map(ThsBusinessBudgetProjectApprovalOpinionsEntity::getIid).collect(Collectors.toList());

            // 分部分项含有审批意见的清单定额
            List<Integer> idList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(thsfileQdyBudgetEntityList)) {
                idList.addAll(thsfileQdyBudgetEntityList.stream().map(ThsfileQdyBudgetEntity::getIid).collect(Collectors.toList()));
            }
            // 将iIdList中的值合并到idList中并去重
            if (ObjectUtil.isNotEmpty(iIdList)) {
                idList.addAll(iIdList);
            }
            // 去重
            idList = idList.stream().distinct().collect(Collectors.toList());
            resultMap.put("ApprovalIds", idList);
            result.setData(resultMap);
            result.setSuccess(true);
            result.setCode("200");
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("获取审批意见失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * @description: 根据单位工程ID获取这个项目的所有树结构，导出QDG接口
     * @param: @param projectId
     * @return: org.springframework.http.ResponseEntity<java.lang.Object>
     * @author: wxy
     * @date: 2024/4/13 14:55
     */
    @Operation(summary = "根据单位工程ID获取这个项目的所有树结构，导出QDG接口")
    @ApiOperation(value = "根据选中的工程导出")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "projectId", value = "projectId", required = true, paramType = "queryParam", dataType = "String"),
            @ApiImplicitParam(name = "openType", value = "openType:0 ,导出编制文件 1:导出审核文件", required = true, paramType = "queryParam", dataType = "Integer"),
    })
    @GetMapping("/getSelectExportProjectTreeInfo")
    public ResponseEntity<Object> getSelectExportProjectTreeInfo(@RequestParam String projectId,
                                                                 @RequestParam(value = "openType", required = false) Integer openType) {
        log.info("工程文件获取信息");

        openType = (openType == null) ? 0 : openType;

        List<ThsBusinessBudgetProjectDetailRelationEntity> listByRelationId = thsBusinessBudgetProjectDetailRelationService.getListByRelationId(projectId);

        List<String> ids = listByRelationId.stream().map(ThsBusinessBudgetProjectDetailRelationEntity::getDetailId).collect(Collectors.toList());

        ReturnResult<List<Map<String, Object>>> result = new ReturnResult<>();
        try {
            // 不管是编制数据还是审核数据都是同一个结构
            List<Map<String, Object>> list = cloudV1Service.getSelectExportProjectTreeInfo(ids, openType);

            List<Map<String, Object>> resultMap = TreeUtils.getEasyTreesMap(list, "id", "pid", "children");
            result.setData(resultMap);
            result.setCode("200");
            result.setSuccess(true);
        } catch (BadRequestAlertException e) {
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            result.setCode("400");
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("获取工程文件失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);

    }


    /**
     * @Description: Todo 批量修改工料机价格
     * @Param: * @param projectId
     * @return: org.springframework.http.ResponseEntity<jnpf.docking.vm.ReturnResult < java.util.List < jnpf.docking.vm.CloudDocVM>>>
     * @Author: hrj
     * @Date: 15:22 2024/6/11
     */
    @Operation(summary = "批量修改工料机价格")
    @ApiOperation(value = "批量修改工料机价格")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "projectId", value = "工程文件ID", required = true, paramType = "queryParam", dataType = "String")
    })
    @GetMapping("/getCalcProjectTreeInfo")
    public ResponseEntity<ReturnResult<List<CloudDocVM>>> getCalcProjectTreeInfo(@RequestParam String projectId) {
        log.info("批量修改工料机价格");
        ReturnResult<List<CloudDocVM>> result = new ReturnResult<>();
        try {
//            List<String> projectIds = Arrays.stream(projectId.split(",")).collect(Collectors.toList());

            List<ThsBusinessBudgetProjectDetailRelationEntity> listByRelationId = thsBusinessBudgetProjectDetailRelationService.getListByRelationId(projectId);

            List<String> projectIds = listByRelationId.stream().map(ThsBusinessBudgetProjectDetailRelationEntity::getDetailId).collect(Collectors.toList());

            List<CloudDocVM> calcProjectTreeInfoTree = cloudV1Service.getCalcProjectTreeInfoTree(projectIds);
            result.setSuccess(true);
            result.setCode("200");
            result.setData(calcProjectTreeInfoTree);


        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode("400");
            result.setMessage("获取项目结构失败！");
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(result);
    }


    @PostMapping("/createProjectApprovalOpinions")
    @Operation(summary = "创建清单审批记录")
    public ActionResult createProjectApprovalOpinions(@RequestBody @Valid ThsBusinessBudgetProjectApprovalOpinionsEntity entity) {
        String detailId = entity.getDetailId();
        boolean b = thsBusinessBudgetProjectApprovalOpinionsService.operApprovalOpinionsPermission(detailId);
        if (b) {
            ActionResult projectApprovalOpinions = thsBusinessBudgetProjectApprovalOpinionsService.createProjectApprovalOpinions(entity);
            return projectApprovalOpinions;
        }
        return ActionResult.fail("不是当前审批人,不能添加审批意见");
    }

    @PostMapping("/deleteProjectApprovalOpinions")
    @Operation(summary = "多选删除")
    public ActionResult deleteProjectApprovalOpinions(@RequestBody @Valid Map<String, Object> objectMap) {
        if (!objectMap.containsKey("ids")) {
            return ActionResult.fail("删除错误");
        }
        if (!objectMap.containsKey("detailId")) {
            return ActionResult.fail("單位工程不存在，参数错误");
        }
        String detailId = objectMap.get("detailId").toString();

        boolean b = thsBusinessBudgetProjectApprovalOpinionsService.operApprovalOpinionsPermission(detailId);
        try {
            if (b) {
                b = thsBusinessBudgetProjectApprovalOpinionsService.deleteAll((List<String>) objectMap.get("ids"));
                if (b) {
                    return ActionResult.success(b);
                }
            }
        } catch (RuntimeException runtimeException) {
            log.error(runtimeException.getMessage());
        }
        return ActionResult.fail("删除失败");
    }

}
