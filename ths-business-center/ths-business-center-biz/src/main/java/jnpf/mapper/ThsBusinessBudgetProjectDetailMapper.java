package jnpf.mapper;


import jnpf.base.mapper.SuperMapper;
import jnpf.entity.ThsBusinessBudgetProjectDetailEntity;
import jnpf.model.statistics.ThsUserBusinessStatisticsModel;
import jnpf.model.statistics.ThsUserBusinessStatisticsOtherFeeModel;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 造价编制项目结构
 * 版本： V3.5
 * 版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * 作者： JNPF开发平台组
 * 日期： 2023-09-06
 */
public interface ThsBusinessBudgetProjectDetailMapper extends SuperMapper<ThsBusinessBudgetProjectDetailEntity> {
    @Select("SELECT MAX(sequence) FROM ths_business_budget_project_detail")
    Integer selectMaxSequence();


    @Select("SELECT a.* FROM ths_business_budget_project_detail a " +
            "LEFT JOIN thsfile_files b ON a.thsfile_id = b.id " +
            "WHERE ((b.analysis_type = 2  AND Analysis_Mode=0) or (b.analysis_type = 9  AND Analysis_Mode=1)) " +
            "AND (a.file_analysis_status IS NULL OR a.file_analysis_status = 0)")
    @Results({
            @Result(column = "BUDGET_PROJECT_ID", property = "budgetProjectId"),
            @Result(column = "TOTAL_PRICE", property = "totalPrice"),
            @Result(column = "ITEM_TYPE", property = "itemType"),
            @Result(column = "FILE_ANALYSIS_STATUS", property = "fileAnalysisStatus"),
            @Result(column = "THSFILE_ID", property = "thsfileId"),
            @Result(column = "INFORMATION_PRICE_DATE", property = "informationPriceDate"),
            @Result(column = "SEND_TOTAL_PRICE", property = "sendTotalPrice"),
            @Result(column = "TENANT_ID", property = "tenantId"),
    })
    List<ThsBusinessBudgetProjectDetailEntity> selectAllByThsFileIdAnalysisStatus();


    @Select("SELECT MAX(CHANGED_VER) FROM ths_business_budget_project_detail where CHANGED_ORIGINAL_ID=#{changedOriginalId} ;")
    Integer selectMaxChangedVerByChangedOriginalId(String changedOriginalId);


    @Select("SELECT a.* FROM ths_business_budget_project_detail a " +
            "LEFT JOIN ths_business_budget_project_version b ON b.id = a.budget_project_id " +
            "WHERE b.is_finalize = 1 AND a.item_type =1 AND  b.consult_project_id = #{consultProjectId} ;")
    @ResultType(HashMap.class)
    HashMap<String, Object> getBudgetProjectDetailByConsultProjectId(String consultProjectId);

    @Update("UPDATE ths_business_budget_project_detail SET current_version_flag = 0, CALC_FLAG = 0 " +
            "WHERE (changed_original_id = #{changedOriginalId} AND id != #{id1}) OR (id = #{id2})")
    int updateAllNoneCurrentVersionFlagBusinessBudgetProjectDetail(@Param("changedOriginalId") String changedOriginalId,
                                                                   @Param("id1") String id1,
                                                                   @Param("id2") String id2);


    @Update("UPDATE ths_business_budget_project_detail " +
            "SET name=#{entity.name} ," +
            " code=#{entity.code} ," +
            " total_price=#{entity.totalPrice} ," +
            " file_name=#{entity.fileName} ," +
            " status=#{entity.status} ," +
            "file_save_path=#{entity.fileSavePath}," +
            "file_size=#{entity.fileSize}," +
            "file_version=#{entity.fileVersion}," +
            "file_analysis_status=#{entity.fileAnalysisStatus}," +
            "thsfile_id=#{entity.thsfileId}," +
            "checksum=#{entity.checksum}," +
            "cur_flow_detail_id=#{entity.curFlowDetailId}," +
            "budget_money=#{entity.budgetMoney}," +
            "measure_money=#{entity.measureMoney}," +
            "other_money=#{entity.otherMoney}," +
            "labor_money=#{entity.laborMoney}," +
            "material_money=#{entity.materialMoney}," +
            "machine_money=#{entity.machineMoney}," +
            "regulation_money=#{entity.regulationMoney}," +
            "tax_money=#{entity.taxMoney}," +
            "overhead_money=#{entity.overheadMoney}," +
            "profit_money=#{entity.profitMoney}," +
            "prj_other_money=#{entity.prjOtherMoney}," +
            "equipment_money=#{entity.equipmentMoney}," +
            "material_estimation_money=#{entity.materialEstimationMoney}," +
            "safety_civilization_money=#{entity.safetyCivilizationMoney}," +
            "audit_money=#{entity.auditMoney}," +
            "other_file_flag=#{entity.otherFileFlag}," +
            "Remark=#{entity.Remark}," +
            "original_file_name=#{entity.originalFileName}," +
            "audit_file_id=#{entity.auditFileId}," +
            "audit_file_hash=#{entity.auditFileHash}," +
            "audit_file_version=#{entity.auditFileVersion}," +
            "audit_file_save_path=#{entity.auditFileSavePath}," +
            "original_send_file_id=#{entity.originalSendFileId}," +
            "original_send_file_hash=#{entity.originalSendFileHash}," +
            "original_send_file_version=#{entity.originalSendFileVersion}," +
            "original_send_file_save_path=#{entity.originalSendFileSavePath}," +
            "relational_file_id=#{entity.relationalFileId}," +
            "relational_file_hash=#{entity.relationalFileHash}," +
            "relational_file_version=#{entity.relationalFileVersion}," +
            "relational_file_save_path=#{entity.relationalFileSavePath}," +
            "last_audit_role =#{entity.lastAuditRole}, " +
            "opened =#{entity.opened}, " +
            "locked_by =#{entity.lockedBy}, " +
            "locked_date =#{entity.lockedDate}, " +
            "locked_machine =#{entity.lockedMachine} " +
            "WHERE id=#{entity.id}")
    boolean updateBusinessBudgetProjectDetailById(@Param("entity") ThsBusinessBudgetProjectDetailEntity entity);


    /**
     * @param signingBeginDate
     * @param signingEndDate
     * @param status
     * @param finishBeginTime
     * @param finishEndTime
     * @param editorialTypeIds
     * @param businessTypeIdList
     * @param userIds
     * @description: 仅支持MYSQL语法 获取编制人单位工程的造价
     * @param: @param contractCode
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 17:12
     */
    @Select(" select pd.budget_project_id,pd.id,pd.pid as dxId,pd.pid,pd.send_total_price ,pd.total_price,pd.item_type,  \n" +
            "        case when COALESCE(pd.send_total_price, 0) > COALESCE(pd.total_price, 0) then COALESCE(pd.send_total_price, 0) - COALESCE(pd.total_price, 0) else 0 end as deduction_amount, \n" +
            "        case when COALESCE(pd.send_total_price, 0) < COALESCE(pd.total_price, 0) then COALESCE(pd.total_price, 0) - COALESCE(pd.send_total_price, 0) else 0 end as grant_amount, \n" +
            "   cp.consulting_type_id ,cp.editorial_type_id , \n" +
            "   mc.code as contract_code , \n" +
            "   (select count(1) from ths_business_budget_project_detail_user where pd.id=detail_id) as user_count,\n " +
            "   pdu.user_id as user_id,\n " +
            "   case  when pd.`status`=3  then 1 else 0 end as record_count ,\n" +
            "   case when pd.`status`=3 then '' else pd.Pid end as pid" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " inner join ths_manage_contract mc  on  cp.contract_id=mc.id\n" +
            " inner join ths_business_budget_project_detail_user pdu  on pd.id=pdu.detail_id  and pd.budget_project_id=pdu.budget_project_id \n" +
            " where pd.item_type=3\n" +
            " and pd.calc_flag=1\n" +
            " and pd.current_version_flag=1 \n" +
            " AND IF( ${editorialTypeIds} is null or ${editorialTypeIds}='',1=1, cp.editorial_type_id in (${editorialTypeIds}))\n" +   // 查看项目明细的时候需要区分编制项目和审核项目
            " AND IF( #{businessTypeIdList} is null or #{businessTypeIdList} ='null',1=1, cp.business_type_id in (${businessTypeIdList}))\n" +
            " and IF(#{contractCode}=''or #{contractCode} is null,1=1,mc.code =#{contractCode}) \n" +
            " AND IF(#{signingBeginDate} is null,1=1,cp.commission_date >= #{signingBeginDate}) \n" +
            " AND IF(#{signingEndDate} is null,1=1,cp.commission_date <= #{signingEndDate}) \n" +
            " AND IF(#{status} is null,1=1, IF(#{status} = -1, pd.status != 3, pd.status=#{status})) " +
            " AND IF(#{finishBeginTime} is null,1=1,pd.finish_time >= #{finishBeginTime}) \n" +
            " AND IF(#{finishEndTime} is null,1=1,pd.finish_time <= #{finishEndTime}) " +
            " AND IF(#{userIds} is null or #{userIds}='null' ,1=1, pdu.user_id in (${userIds}))\n" +
            "; "
    )
    @Results({
//            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "record_count", property = "recordCount"),
            @Result(column = "deduction_amount", property = "deductionAmount"),
            @Result(column = "grant_amount", property = "grantAmount"),
            @Result(column = "send_total_price", property = "sendTotalPrice"),
            @Result(column = "contract_code", property = "contractCode"),
            @Result(column = "user_count", property = "userCount"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "total_price", property = "totalPrice"),
            @Result(column = "item_type", property = "itemType"),
    })
    List<ThsUserBusinessStatisticsModel> selectBzCostListByFilterStr(@Param("contractCode") String contractCode,
                                                                     @Param("signingBeginDate") String signingBeginDate,
                                                                     @Param("signingEndDate") String signingEndDate,
                                                                     @Param("status") Integer status,
                                                                     @Param("finishBeginTime") String finishBeginTime,
                                                                     @Param("finishEndTime") String finishEndTime,
                                                                     @Param("editorialTypeIds") String editorialTypeIds,
                                                                     @Param("businessTypeIdList") String businessTypeIdList,
                                                                     @Param("userIds") String userIds);


    /**
     * @param signingBeginDate
     * @param signingEndDate
     * @param status
     * @param finishBeginTime
     * @param finishEndTime
     * @param editorialTypeIds
     * @param businessTypeIdList
     * @param userIds
     * @param flowDetailIds
     * @description: 查询审核人造价数据
     * @param: @param contractCode
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 17:12
     */
    @Select(" select pd.budget_project_id,pd.id,pd.pid as dxId,pd.pid,pd.send_total_price ,pd.total_price,pd.item_type,\n" +
            "        case when COALESCE(pd.send_total_price, 0) > COALESCE(pd.total_price, 0) then COALESCE(pd.send_total_price, 0) - COALESCE(pd.total_price, 0) else 0 end as deduction_amount, \n" +
            "        case when COALESCE(pd.send_total_price, 0) < COALESCE(pd.total_price, 0) then COALESCE(pd.total_price, 0) - COALESCE(pd.send_total_price, 0) else 0 end as grant_amount, \n" +
            "   cp.consulting_type_id ,cp.editorial_type_id , \n" +
            "   mc.code as contract_code  , \n" +
            "   ( select count(1)\n" +
            "     from ths_business_budget_project_flow_details_user\n" +
            "     where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id " +
            "    ) as user_count,\n " +
            "   pfdu.user_id as user_id,\n " +
            "   case  when pd.`status`=3  then 1 else 0 end as record_count ,\n" +
            "   case when pd.`status`=3 then '' else pd.pid end as pid" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " inner join ths_manage_contract mc  on  cp.contract_id=mc.id\n" +
            " inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            " inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id \n" +
            " where pd.item_type=3\n" +
            " and pd.calc_flag=1\n" +
            " and pd.current_version_flag=1 \n" +
            " AND IF( ${editorialTypeIds} is null or ${editorialTypeIds}='',1=1, cp.editorial_type_id in (${editorialTypeIds}))\n" +   // 查看项目明细的时候需要区分编制项目和审核项目
            " AND IF(#{businessTypeIdList} is null or #{businessTypeIdList} ='null',1=1, cp.business_type_id in (${businessTypeIdList}))\n" +
            " and IF(#{contractCode}=''or #{contractCode} is null,1=1,mc.code =#{contractCode}) \n" +
            " AND IF(#{signingBeginDate} is null,1=1,cp.commission_date >= #{signingBeginDate}) \n" +
            " AND IF(#{signingEndDate} is null,1=1,cp.commission_date <= #{signingEndDate}) \n" +
            " AND IF(#{status} is null,1=1, IF(#{status} = -1, pd.status != 3, pd.status=#{status})) " +
            " AND IF(#{finishBeginTime} is null,1=1,pd.finish_time >= #{finishBeginTime}) \n" +
            " AND IF(#{finishEndTime} is null,1=1,pd.finish_time <= #{finishEndTime}) " +
            " AND IF( ${userIds} is null or ${userIds}='',1=1, pfdu.user_id in (${userIds}))\n" +
            " AND pfd.id in ( ${flowDetailIds} )  \n" +   // 某一个角色的流程IDs
            "; "
    )
    @Results({
            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "record_count", property = "recordCount"),
            @Result(column = "deduction_amount", property = "deductionAmount"),
            @Result(column = "grant_amount", property = "grantAmount"),
            @Result(column = "send_total_price", property = "sendTotalPrice"),
            @Result(column = "contract_code", property = "contractCode"),
            @Result(column = "user_count", property = "userCount"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "total_price", property = "totalPrice"),
            @Result(column = "item_type", property = "itemType"),
    })
    List<ThsUserBusinessStatisticsModel> selectShCostListByFilterStr(@Param("contractCode") String contractCode,
                                                                     @Param("signingBeginDate") String signingBeginDate,
                                                                     @Param("signingEndDate") String signingEndDate,
                                                                     @Param("status") Integer status,
                                                                     @Param("finishBeginTime") String finishBeginTime,
                                                                     @Param("finishEndTime") String finishEndTime,
                                                                     @Param("editorialTypeIds") String editorialTypeIds,
                                                                     @Param("businessTypeIdList") String businessTypeIdList,
                                                                     @Param("userIds") String userIds,
                                                                     @Param("flowDetailIds") String flowDetailIds
    );

    /**
     * 查询项目经理造价数据
     *
     * @description:
     * @param: @param null
     * @return:
     * @author: wxy
     * @date: 2024/4/20 17:12
     */
    @Select(" select pd.budget_project_id,pd.id,pd.pid as dxId,pd.pid,pd.send_total_price ,pd.total_price,pd.item_type,\n" +
            "        case when COALESCE(pd.send_total_price, 0) > COALESCE(pd.total_price, 0) then COALESCE(pd.send_total_price, 0) - COALESCE(pd.total_price, 0) else 0 end as deduction_amount, \n" +
            "        case when COALESCE(pd.send_total_price, 0) < COALESCE(pd.total_price, 0) then COALESCE(pd.total_price, 0) - COALESCE(pd.send_total_price, 0) else 0 end as grant_amount, \n" +
            "   cp.consulting_type_id ,cp.editorial_type_id , \n" +
            "   mc.code as contract_code  , \n" +
            "   1 as user_count,\n " +
            "   cp.responsible_person_one as user_id,\n " +
            "   case  when pd.`status`=3  then 1 else 0 end as record_count ,\n" +
            "   case when pd.`status`=3 then '' else pd.Pid end as pid" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " inner join ths_manage_contract mc  on  cp.contract_id=mc.id\n" +
            " where pd.item_type=3\n" +
            " and pd.calc_flag=1\n" +
            " and pd.current_version_flag=1 \n" +
            " AND IF( ${editorialTypeIds} is null or ${editorialTypeIds}='',1=1, cp.editorial_type_id in (${editorialTypeIds}))\n" +   // 查看项目明细的时候需要区分编制项目和审核项目
            " AND IF(#{businessTypeIdList} is null or #{businessTypeIdList} ='null',1=1, cp.business_type_id in (${businessTypeIdList})) \n" +
            " and IF(#{contractCode}=''|| #{contractCode} is null,1=1,mc.code =#{contractCode}) \n" +
            " AND IF(#{signingBeginDate} is null,1=1,cp.commission_date >= #{signingBeginDate}) \n" +
            " AND IF(#{signingEndDate} is null,1=1,cp.commission_date <= #{signingEndDate}) \n" +
            " AND IF(#{status} is null,1=1, IF(#{status} = -1, pd.status != 3, pd.status=#{status})) " +
            " AND IF(#{finishBeginTime} is null,1=1,pd.finish_time >= #{finishBeginTime}) \n" +
            " AND IF(#{finishEndTime} is null,1=1,pd.finish_time <= #{finishEndTime}) " +
            " AND IF(${userIds} is null or ${userIds}='' ,1=1, cp.responsible_person_one in (${userIds}))\n" +
            "; "
    )
    @Results({
            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "record_count", property = "recordCount"),
            @Result(column = "deduction_amount", property = "deductionAmount"),
            @Result(column = "grant_amount", property = "grantAmount"),
            @Result(column = "send_total_price", property = "sendTotalPrice"),
            @Result(column = "contract_code", property = "contractCode"),
            @Result(column = "user_count", property = "userCount"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "total_price", property = "totalPrice"),
            @Result(column = "item_type", property = "itemType"),
    })
    List<ThsUserBusinessStatisticsModel> selectXMJLCostListByFilterStr(@Param("contractCode") String contractCode,
                                                                       @Param("signingBeginDate") String signingBeginDate,
                                                                       @Param("signingEndDate") String signingEndDate,
                                                                       @Param("status") Integer status,
                                                                       @Param("finishBeginTime") String finishBeginTime,
                                                                       @Param("finishEndTime") String finishEndTime,
                                                                       @Param("editorialTypeIds") String editorialTypeIds,
                                                                       @Param("businessTypeIdList") String businessTypeIdList,
                                                                       @Param("userIds") String userIds);

    /**
     * @description: 查询单项层费用
     * @param: @param ids
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 17:11
     */
    @Select(" select pd.budget_project_id,pd.id,pd.id as dxId,0 as send_total_price ,0 as total_price,pd.item_type,\n" +
            "   0 as deduction_amount , 0 as  grant_amount , \n" +
            "   case  when pd.id<>'' and pd.`status`=3  then 1 else 0 end as record_count ,\n" +
            "   case when pd.id<>'' and pd.`status`=3 then '' else pd.Pid end as pid" +
            " from ths_business_budget_project_detail pd\n" +
            " where pd.id in (${ids}) ;"
    )
    @Results({
            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "record_count", property = "recordCount"),
            @Result(column = "deduction_amount", property = "deductionAmount"),
            @Result(column = "grant_amount", property = "grantAmount"),
            @Result(column = "send_total_price", property = "sendTotalPrice"),
            @Result(column = "contract_code", property = "contractCode"),
            @Result(column = "user_count", property = "userCount"),
            @Result(column = "user_id", property = "userId"),
            @Result(column = "total_price", property = "totalPrice"),
            @Result(column = "item_type", property = "itemType"),
    })
    List<ThsUserBusinessStatisticsModel> selectCostListByIds(@Param("ids") String ids);


    /**
     * @Description: Todo 先计算所有单位工程的工程造价
     * @Param: * @param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:29 2024/4/12
     */
    @Select("SELECT\n" +
            " pd.id,\n" +
            " pd.budget_project_id AS budgetProjectId,\n" +
            " pd.pid AS dxid,\n" +
            " pd.`status`,\n" +
            " pd.send_total_price AS sndMoney,\n" +
            " pd.total_price AS totalMoney,\n" +
            "CASE\n" +
            "  WHEN total_price - send_total_price > 0 THEN\n" +
            "  0 ELSE send_total_price - total_price \n" +
            " END AS hjMoney,\n" +
            "CASE\n" +
            "  \n" +
            "  WHEN COALESCE(total_price, 0) - COALESCE(send_total_price, 0) > 0 THEN\n" +
            "  COALESCE(total_price, 0) - COALESCE(send_total_price, 0) ELSE 0 \n" +
            " END AS hzMoney,\n" +
            "CASE\n" +
            "  WHEN pd.STATUS = 3 THEN\n" +
            "  1 ELSE 0 \n" +
            " END AS recordCount,\n" +
            "CASE\n" +
            "  WHEN pd.STATUS = 3 THEN\n" +
            "  0 ELSE pd.Pid \n" +
            " END AS pid FROM\n" +
            "  ths_business_budget_project_detail pd\n" +
            "  INNER JOIN ths_business_budget_project_version pl ON pl.id = pd.budget_project_id\n" +
            "  INNER JOIN ths_business_consult_project c ON c.id = pl.consult_project_id\n" +
            "  INNER JOIN ths_manage_contract mc ON c.contract_id = mc.id \n" +
            "WHERE\n" +
            "  pd.item_type = 3 \n" +
            "  AND pd.calc_flag = 1 \n" +
            "  AND pd.current_version_flag = 1 \n" +
            " AND IF(#{contractCode}='' or #{contractCode} is null,1=1,mc.code =#{contractCode}) \n" +
            " AND IF(#{startCommissionDate} is null,1=1,c.commission_date >= #{startCommissionDate}) \n" +
            " AND IF(#{endCommissionDate} is null,1=1,c.commission_date <= #{endCommissionDate}) \n" +
            " AND IF(#{status} is null,1=1, IF(#{status} = -1, pd.status != 3, pd.status=#{status})) " +
            " AND IF(#{startCompleteDate} is null,1=1,pd.finish_time >= #{startCompleteDate}) \n" +
            " AND IF(#{endCompleteDate} is null,1=1,pd.finish_time <= #{endCompleteDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, c.business_type_id in (${businessTypeIdList})) "
    )
    @ResultType(List.class)
    List<Map<String, Object>> getUnitEngineeringCost(@Param("contractCode") String contractCode,
                                                     @Param("startCommissionDate") String startCommissionDate,
                                                     @Param("endCommissionDate") String endCommissionDate,
                                                     @Param("status") Integer status,
                                                     @Param("startCompleteDate") String startCompleteDate,
                                                     @Param("endCompleteDate") String endCompleteDate,
                                                     @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                                     @Param("businessTypeIdList") String businessTypeIdList);

    @Select("SELECT\n" +
            " a.manager_user_id as YXRYID,\n" +
            " SUM(\n" +
            " a.estimated_cost ) AS YJZJ,\n" +
            " SUM(\n" +
            " b.total_money ) AS YJSF,\n" +
            "CASE\n" +
            "  \n" +
            "  WHEN a.business_type_id = #{businessTypeId} THEN\n" +
            "  1 ELSE 0 \n" +
            " END AS PrjType,\n" +
            "CASE\n" +
            "  \n" +
            "  WHEN a.business_type_id = #{businessTypeId}  THEN\n" +
            "  0 ELSE 1 \n" +
            " END AS ZJCount,\n" +
            "CASE\n" +
            "  \n" +
            "  WHEN a.business_type_id = #{businessTypeId} THEN\n" +
            "  1 ELSE 0 \n" +
            " END AS DKCount \n" +
            "FROM\n" +
            " ths_business_consult_project a LEFT JOIN ths_manage_contract b ON a.contract_id = b.id where a.manager_user_id is not null and a.manager_user_id <> '' " +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,b.code = #{code})" +
            " AND IF( #{isCompleted} is null or #{isCompleted}='' , 1=1 ,a.is_complete = #{isCompleted})" +
            " AND IF( #{startCompleteDate} is null,1=1,a.project_complete_time >= #{startCompleteDate}) " +
            " AND IF( #{endCompleteDate} is null,1=1,a.project_complete_time <= #{endCompleteDate}) " +
            " AND IF( #{startCommissionDate} is null,1=1,a.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,a.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, a.business_type_id in (${businessTypeIdList})) " +
            " group by a.manager_user_id,a.business_type_id,b.`code` \n")
    @ResultType(List.class)
    List<Map<String, Object>> marketingSituation(@Param("businessTypeId") String businessTypeId,
                                                 @Param("code") String code,
                                                 @Param("isCompleted") Integer isCompleted,
                                                 @Param("startCommissionDate") String startCommissionDate,
                                                 @Param("endCommissionDate") String endCommissionDate,
                                                 @Param("startCompleteDate") String startCompleteDate,
                                                 @Param("endCompleteDate") String endCompleteDate,
                                                 @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                                 @Param("businessTypeIdList") String businessTypeIdList);

    @Select("SELECT\n" +
            "  a.manager_user_id as YXRYID,\n" +
            "  SUM(b.actual_paid) AS SJSF,\n" +
            "CASE\n" +
            "    \n" +
            "    WHEN a.business_type_id = #{businessTypeId} THEN\n" +
            "    1 ELSE 0 \n" +
            "  END AS PrjType,\n" +
            "CASE\n" +
            "    \n" +
            "    WHEN a.business_type_id = #{businessTypeId} THEN\n" +
            "    0 ELSE 1 \n" +
            "  END AS ZJCount,\n" +
            "CASE\n" +
            "    \n" +
            "    WHEN a.business_type_id = #{businessTypeId} THEN\n" +
            "    1 ELSE 0 \n" +
            "  END AS DKCount \n" +
            "FROM\n" +
            "  ths_business_consult_project a LEFT JOIN ths_manage_contract b ON a.contract_id = b.id \n" +
            "  LEFT JOIN ths_manage_receivable cm ON b.ID = cm.contract_id " +
            "  INNER JOIN ths_manage_invoice_details mid on mid.contract_id = b.id " +
            " where a.manager_user_id is not null and a.manager_user_id <> '' " +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,b.code = #{code})" +
            " AND IF( #{isCompleted} is null or #{isCompleted}='' , 1=1 ,a.is_complete = #{isCompleted})" +
            " AND IF( #{startCompleteDate} is null,1=1,a.project_complete_time >= #{startCompleteDate}) " +
            " AND IF( #{endCompleteDate} is null,1=1,a.project_complete_time <= #{endCompleteDate}) " +
            " AND IF( #{startCommissionDate} is null,1=1,a.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,a.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, a.business_type_id in (${businessTypeIdList})) " +
            "  GROUP BY a.manager_user_id,a.business_type_id, b.`code` ")
    @ResultType(List.class)
    List<Map<String, Object>> consultCM(@Param("businessTypeId") String businessTypeId,
                                        @Param("code") String code,
                                        @Param("isCompleted") Integer isCompleted,
                                        @Param("startCommissionDate") String startCommissionDate,
                                        @Param("endCommissionDate") String endCommissionDate,
                                        @Param("startCompleteDate") String startCompleteDate,
                                        @Param("endCompleteDate") String endCompleteDate,
                                        @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                        @Param("businessTypeIdList") String businessTypeIdList);

    /**
     * @Description: 编制工程安装
     * @Param: * @param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 10:28 2024/4/22
     */
    @Select("select budgetProjectId,\n" +
            "   pid,\n" +
            "   prjType ,\n" +
            "   userId,\n" +
            "   Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from (" +
            " select pd.budget_project_id as budgetProjectId,\n" +
            "       pd.Pid,\n" +
            "       0 as prjType ,\n" +
            "       ufp.user_id as userId,\n" +
            "       total_price  as  totalMoney , \n" +
            "       (select COUNT(1) from ths_business_budget_project_detail_user where detail_id = pd.Id) as UserCount\n" +
            "from ths_business_budget_project_detail  pd\n" +
            "inner join ths_business_budget_project_version pl on pl.Id = pd.budget_project_id \n" +
            "inner join ths_business_budget_project_detail_user ufp on ufp.detail_id = pd.Id \n" +
            "inner join ths_business_consult_project c on c.ID = pl.consult_project_id LEFT JOIN ths_manage_contract tmc ON c.contract_id = tmc.id \n" +
            "where pd.item_type = 3\n" +
            "and pd.calc_flag = 1\n" +
            "and pl.status = 3 " +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,tmc.code = #{code})" +
            " AND IF( #{startCommissionDate} is null,1=1,c.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,c.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, c.business_type_id in (${businessTypeIdList})) " +
            ") as s1 ")
    @ResultType(List.class)
    List<Map<String, Object>> prepareEngineeringInstallation(@Param("code") String code,
                                                             @Param("startCommissionDate") String startCommissionDate,
                                                             @Param("endCommissionDate") String endCommissionDate,
                                                             @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                                             @Param("businessTypeIdList") String businessTypeIdList);


    /**
     * @Description: Todo 校核工程安装
     * @Param: * @param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 10:53 2024/4/22
     */
    @Select("select budgetProjectId,\n" +
            "   pid,\n" +
            "   prjType ,\n" +
            "   userId,\n" +
            "   Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from (" +
            " select pd.budget_project_id AS budgetProjectId,\n" +
            "       pd.Pid,\n" +
            "       1 as PrjType ,\n" +
            "       pfdu.user_id AS userId,\n" +
            "       total_price  as  totalMoney , \n" +
            "       (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            "from ths_business_budget_project_detail  pd\n" +
            "inner join ths_business_budget_project_version pl on pl.Id = pd.budget_project_id \n" +
            "inner join ths_business_consult_project c on c.ID = pl.consult_project_id LEFT JOIN ths_manage_contract tmc ON c.contract_id = tmc.id \n" +
            "inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            "inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            "where  pd.item_type = 3\n" +
            "and pd.calc_flag = 1\n" +
            "and pl.Status = 3\n" +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,tmc.code = #{code})" +
            " AND IF( #{startCommissionDate} is null,1=1,c.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,c.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, c.business_type_id in (${businessTypeIdList})) " +
            "and pfd.id in (select id from ths_sys_flow_details where role_type = 2 ) ) as s1 ")
    @ResultType(List.class)
    List<Map<String, Object>> verificationOfEngineeringInstallation(@Param("code") String code,
                                                                    @Param("startCommissionDate") String startCommissionDate,
                                                                    @Param("endCommissionDate") String endCommissionDate,
                                                                    @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                                                    @Param("businessTypeIdList") String businessTypeIdList);

    /**
     * @Description: Todo 核准工程安装
     * @Param: * @param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 10:56 2024/4/22
     */
    @Select("select budgetProjectId,\n" +
            "   pid,\n" +
            "   prjType ,\n" +
            "   userId,\n" +
            "   Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from (" +
            " select pd.budget_project_id AS budgetProjectId,\n" +
            "       pd.Pid,\n" +
            "       2 as PrjType ,\n" +
            "       pfdu.user_id AS userId,\n" +
            "       total_price  as  totalMoney , \n" +
            "       (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            "from ths_business_budget_project_detail  pd\n" +
            "inner join ths_business_budget_project_version pl on pl.Id = pd.budget_project_id \n" +
            "inner join ths_business_consult_project c on c.ID = pl.consult_project_id LEFT JOIN ths_manage_contract tmc ON c.contract_id = tmc.id \n" +
            "inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            "inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            "where  pd.item_type = 3\n" +
            "and pd.calc_flag = 1\n" +
            "and pl.Status = 3\n" +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,tmc.code = #{code})" +
            " AND IF( #{startCommissionDate} is null,1=1,c.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,c.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, c.business_type_id in (${businessTypeIdList})) " +
            "and pfd.id in (select id from ths_sys_flow_details where role_type = 3 ) ) as s1")
    @ResultType(List.class)
    List<Map<String, Object>> approvedEngineeringInstallation(@Param("code") String code,
                                                              @Param("startCommissionDate") String startCommissionDate,
                                                              @Param("endCommissionDate") String endCommissionDate,
                                                              @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                                              @Param("businessTypeIdList") String businessTypeIdList);


    /**
     * @Description: Todo 编制其他费
     * @Param: * @param ids
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 13:43 2024/4/22
     */
    @Select("select budgetProjectId,\n" +
            "   prjType ,\n" +
            "   userId,\n" +
            "   Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from (" +
            " select pd.budget_project_id as budgetProjectId,\n" +
            "        0 as prjType,\n" +
            "        pd.creator_id as userId,\n" +
            "       case when pd.`status` = 3 then  pd.total_price  else 0 end as totalMoney ,\n" +
            "       1 as userCount\n" +
            " from ths_business_budget_project_other_fee  qtf \n" +
            " inner join ths_business_budget_project_detail pd on pd.Id = qtf.detail_id\n" +
            " where  pd.item_type = 2\n" +
            " and pd.calc_flag = 1\n" +
            " and ( pd.total_price > 0 or  pd.creator_id is not null or  pd.creator_id <> '')\n" +
            " and not qtf.ID in (select pid from ths_business_budget_project_other_fee where detail_id = pd.ID)\n" +
            " and pd.ID in (#{ids})\n" +
            " union all\n" +
            " select pd.budget_project_id as budgetProjectId,\n" +
            "       0 as prjType,\n" +
            "        pd.creator_id as userId,\n" +
            "       case when pd.`status` = 3 then  pd.total_price  else 0 end as totalMoney ,\n" +
            "       1 as userCount    \n" +
            " from ths_business_budget_project_other_fee  qtf\n" +
            " inner join ths_business_budget_project_detail pd on pd.id = qtf.detail_id\n" +
            " where pd.item_type = 1\n" +
            " and pd.calc_flag = 1\n" +
            " and (pd.total_price > 0 or  pd.creator_id is not null or  pd.creator_id <> '')\n" +
            " and pd.ID in (select pid from ths_business_budget_project_detail where Id in (#{ids}))\n" +
            " and  not qtf.ID in (select pid from ths_business_budget_project_other_fee where detail_id = pd.ID)  " +
            "  ) as s1")
    @ResultType(List.class)
    List<Map<String, Object>> preparationOfOtherExpenses(@Param("ids") String ids);


    @Select(" select budgetProjectId,\n" +
            "         prjType ,\n" +
            "         userId,\n" +
            "         Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from ( " +
            " select pd.budget_project_id as budgetProjectId,\n" +
            "        1 as prjType,\n" +
            "        pfdu.user_id as userId,\n" +
            "       case when pd.`status` = 3 then  pd.total_price  else 0 end as totalMoney ,\n" +
            "         (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            " from ths_business_budget_project_other_fee  qtf\n" +
            " inner join ths_business_budget_project_detail pd on pd.Id = qtf.detail_id\n" +
            " inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            " inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            " where  (qtf.pid is null or qtf.pid = '')\n" +
            " and pfd.id in (select id from ths_sys_flow_details where role_type = 1 )\n" +
            " and pd.calc_flag = 1\n" +
            " and pd.item_type = 2\n" +
            " and (pd.total_price > 0 or qtf.creator_id is not null or qtf.creator_id <> '')\n" +
            " and pd.ID in (#{ids})\n" +
            " UNION ALL\n" +
            " select pd.budget_project_id as budgetProjectId,\n" +
            "       1 as PrjType,\n" +
            "       pfdu.user_id  as userId,\n" +
            "       case when pd.`status` = 3 then  pd.total_price  else 0 end as totalMoney ,\n" +
            "      (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            " from ths_business_budget_project_other_fee  qtf\n" +
            " inner join ths_business_budget_project_detail pd on pd.Id = qtf.detail_id\n" +
            " inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            " inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            " where  (qtf.pid is null or qtf.pid = '')\n" +
            " and pfd.id in (select id from ths_sys_flow_details where role_type = 1 )\n" +
            " and  pd.item_type = 1\n" +
            " and pd.calc_flag = 1\n" +
            " and (pd.total_price > 0 or qtf.creator_id is not null or qtf.creator_id <> '')\n" +
            " and pd.id in (select pid from ths_business_budget_project_detail where Id in (#{ids}))\n" +
            " ) as  s1")
    @ResultType(List.class)
    List<Map<String, Object>> otherVerificationFees(@Param("ids") String ids);


    @Select(" select budgetProjectId,\n" +
            "         prjType ,\n" +
            "         userId,\n" +
            "         Case when UserCount =0 then 0 else TotalMoney / UserCount end as totalMoney from (\n" +
            " select pd.budget_project_id AS budgetProjectId,\n" +
            "         pd.Pid,\n" +
            "         2 as PrjType ,\n" +
            "         pfdu.user_id AS userId,\n" +
            "         pd.total_price  as  totalMoney ,\n" +
            "      (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            " from ths_business_budget_project_other_fee  qtf\n" +
            " inner join ths_business_budget_project_detail pd on pd.Id = qtf.detail_id\n" +
            " inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            " inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            " where   (qtf.pid is null or qtf.pid = '')\n" +
            " and pfd.id in (select id from ths_sys_flow_details where role_type = 2 )\n" +
            " and pd.calc_flag = 1\n" +
            " and pd.item_type = 2\n" +
            " and (pd.total_price > 0 or qtf.creator_id is not null or qtf.creator_id <> '')\n" +
            " and pd.ID in (#{ids})\n" +
            " union all\n" +
            " select pd.budget_project_id AS budgetProjectId,\n" +
            "         pd.Pid,\n" +
            "         2 as PrjType ,\n" +
            "         pfdu.user_id AS userId,\n" +
            "         pd.total_price  as  totalMoney ,\n" +
            "      (select COUNT(1) from ths_business_budget_project_flow_details_user  where pd.id = detail_id  and pfd.id=flow_detail_id and  pfd.budget_project_id=budget_project_id ) as userCount\n" +
            " from ths_business_budget_project_other_fee  qtf\n" +
            " inner join ths_business_budget_project_detail pd on pd.Id = qtf.detail_id\n" +
            " inner join ths_business_budget_project_flow_details pfd on  pd.id=pfd.detail_id and pd.budget_project_id=pfd.budget_project_id\n" +
            " inner join ths_business_budget_project_flow_details_user pfdu on pfd.id=pfdu.flow_detail_id and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id\n" +
            " where (qtf.pid is null or qtf.pid = '')\n" +
            " and pfd.id in (select id from ths_sys_flow_details where role_type = 2 )\n" +
            " and  pd.item_type = 1\n" +
            " and pd.calc_flag = 1\n" +
            " and (pd.total_price > 0 or qtf.creator_id is not null or qtf.creator_id <> '')\n" +
            " and pd.id in (select pid from ths_business_budget_project_detail where Id in (#{ids}))\n" +
            " ) as s2")
    @ResultType(List.class)
    List<Map<String, Object>> approvalOfOtherFees(@Param("ids") String ids);


    @Select(" select dept.F_Id as deptId,\n" +
            "         u.F_Id as userId,\n" +
            "       dept.f_full_name as deptName,\n" +
            "       u.f_real_name as userName,\n" +
            "       b.prjType,\n" +
            "       b.prjTypeName\n" +
            " from base_user u ,\n" +
            "      base_organize dept ,\n" +
            "   ( select 0 as prjType ,'1.编审情况' as prjTypeName\n" +
            "             union all\n" +
            "             select 1 as prjType,'2.校核情况' as prjTypeName\n" +
            "             union all\n" +
            "             select 2 as prjType, '3.核准情况' as prjTypeName) b\n" +
            " where  dept.F_Id = u.f_organize_id ")
    @ResultType(List.class)
    List<Map<String, Object>> allUser();


    /**
     * @param status
     * @description: 查询单项层工程和建设项目其他费
     * @param: @param ids
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 17:34
     */
    @Select("SELECT " +
            "    pd.budget_project_id as budgetProjectId, " +
            "    CASE WHEN (#{status} IS NULL OR " +
            "               (#{status} = -1 AND pd.status != 3) OR " +
            "               (#{status} != -1 AND pd.status = #{status})) " +
            "         THEN pd.total_price ELSE 0 END AS totalPrice, " +
            "    CASE WHEN (#{status} IS NULL OR " +
            "               (#{status} = -1 AND pd.status != 3) OR " +
            "               (#{status} != -1 AND pd.status = #{status})) " +
            "         THEN pd.send_total_price ELSE 0 END AS sendTotalPrice, " +
            "    CASE WHEN (#{status} IS NULL OR " +
            "               (#{status} = -1 AND pd.status != 3) OR " +
            "               (#{status} != -1 AND pd.status = #{status})) " +
            "         THEN CASE WHEN pd.total_price - pd.send_total_price > 0 THEN 0 " +
            "                   ELSE pd.send_total_price - pd.total_price END " +
            "         ELSE 0 END AS deductionAmount, " +
            "    CASE WHEN (#{status} IS NULL OR " +
            "               (#{status} = -1 AND pd.status != 3) OR " +
            "               (#{status} != -1 AND pd.status = #{status})) " +
            "         THEN CASE WHEN pd.total_price - pd.send_total_price > 0 " +
            "                   THEN pd.total_price - pd.send_total_price ELSE 0 END " +
            "         ELSE 0 END AS grantAmount, " +
            "    pof.creator_id AS userId, " +
            "    0 AS recordCount, " +
            "    '' AS pid " +
            "FROM ths_business_budget_project_other_fee pof " +
            "INNER JOIN ths_business_budget_project_detail pd " +
            "    ON pd.budget_project_id = pof.budget_project_id " +
            "    AND pd.id = pof.detail_id " +
            "WHERE pd.calc_flag = 1 " +
            "    AND pof.total_price > 0 " +
            "    AND pof.creator_id IS NOT NULL " +
            "    AND (pof.pid IS NULL OR pof.pid = '') " +
            "    AND (" +
            "        (pd.item_type = 2 AND pd.id IN (${ids})) " +
            "        OR " +
            "        (pd.item_type = 1 AND pd.id IN (" +
            "            SELECT DISTINCT parent.pid " +
            "            FROM ths_business_budget_project_detail parent " +
            "            WHERE parent.id IN (${ids}) " +
            "                AND parent.pid IS NOT NULL" +
            "        ))" +
            "    )"
    )
    List<ThsUserBusinessStatisticsModel> selectOtherFeeListByDetailIds(@Param("ids") String ids, @Param("status") Integer status);


    /**
     * @param ids
     * @description: 查看项目明细查询单项工程其他费
     * @param: @param ids
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/23 10:22
     */
    @Select(" select pd.budget_project_id, pd.id ,pd.pid, case when pd.`status`=3 then 1 else 0 end as finished , " +
            "  pof.total_price as total_price ,pof.send_total_price as send_total_price,\n" +
            " case when pd.`status` = 3  then pof.total_price  else 0  end as total_price_Ex  ,\n" +
            " case when pd.`status` = 3   then pof.send_total_price else 0  end as send_total_price_Ex ,\n" +
            "  pof.creator_id as user_id\n " +
            " from ths_business_budget_project_other_fee pof\n" +
            " inner join ths_business_budget_project_detail pd  on pd.budget_project_id=pof.budget_project_id and pd.id=pof.detail_id \n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " where pd.item_type=2\n" +
            " and pd.calc_flag=1\n" +
            " AND IF( #{editorialTypeIds} is null,1=1, cp.editorial_type_id in (${editorialTypeIds}) )\n" +
            " and (pof.total_price>0 or pof.creator_id<>''  or pof.creator_id is not null)  \n" +
            " and (not pof.id in ( select pid from ths_business_budget_project_other_fee where detail_id=pd.id))  \n" +
            " and pd.id in (${ids}) \n" +
            " union all  \n" +
            " select pd.budget_project_id, pd.id ,pd.pid, case when pd.`status`=3 then 1 else 0 end as finished , " +
            "  pof.total_price as total_price ,pof.send_total_price as send_total_price,\n" +
            " case when pd.`status` = 3  then pof.total_price  else 0  end as total_price_Ex  ,\n" +
            " case when pd.`status` = 3   then pof.send_total_price else 0  end as send_total_price_Ex ,\n" +
            "  pof.creator_id as user_id\n " +
            " from ths_business_budget_project_other_fee pof\n" +
            " inner join ths_business_budget_project_detail pd  on pd.budget_project_id=pof.budget_project_id and pd.id=pof.detail_id \n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " where pd.item_type in (1,2)\n" +
            " and pd.calc_flag=1\n" +
            " AND IF( #{editorialTypeIds} is null,1=1, cp.editorial_type_id in (${editorialTypeIds}) )\n" +
            " and (pof.total_price>0 or pof.creator_id<>''  or pof.creator_id is not null) \n" +
            " and (not pof.id in ( select pid from ths_business_budget_project_other_fee where detail_id=pd.id))  \n" +
            " and pd.id in ( select pid  from  ths_business_budget_project_detail where id in (${ids}) ) \n" +
            "; "
    )
    List<ThsUserBusinessStatisticsOtherFeeModel> selectOtherFeeSingleListByDetailIds(@Param("ids") String ids,
                                                                                     @Param("editorialTypeIds") String editorialTypeIds);

    /**
     * @description: 根据IDS查找数据
     * @param: @param ids
     * @return: java.util.List<jnpf.model.statistics.ThsUserBusinessStatisticsModel>
     * @author: wxy
     * @date: 2024/4/20 17:11
     */
    @Select(" select pd.budget_project_id,pd.id,pd.pid ,pd.item_type, 0 as user_count, '' as  user_id ,\n" +
            "   cp.consulting_type_id ,cp.editorial_type_id , \n" +
            "   mc.code as contract_code  \n" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id \n" +
            " inner join ths_manage_contract mc  on  cp.contract_id=mc.id \n" +
            " where pd.id in (${ids}) ;"
    )
    @Results({
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "contract_code", property = "contractCode"),
            @Result(column = "user_count", property = "userCount"),
            @Result(column = "user_id", property = "userId"),
    })
    List<ThsUserBusinessStatisticsModel> selectProjectDetailListByIds(@Param("ids") String ids);

    @Select("SELECT GROUP_CONCAT(DISTINCT GetUserNameByUserId(u.user_id) SEPARATOR ',') as 'name'\n" +
            "from ths_business_budget_project_flow_details_user u WHERE u.flow_detail_id IN (\n" +
            "SELECT id FROM ths_business_budget_project_flow_details \n" +
            "where detail_id = #{detailId} AND  role_type = #{roleType}) AND detail_id = #{detailId}  ")
    String getUserByRoleType(@Param("detailId") String detailId, @Param("roleType") int roleType);

    @Select(" SELECT distinct pd.*,bu.f_real_name AS BZRY\n" +
            "  from  ths_business_budget_project_detail pd \n" +
            " LEFT JOIN ths_business_budget_project_detail_user pdu ON pd.id = pdu.detail_id AND pd.budget_project_id = pdu.budget_project_id \n" +
            " left join base_user bu on pdu.user_id=bu.F_Id \n" +
            "  WHERE 1=1\n" +
            "  and  ((pd.item_type=3 and pd.calc_flag = 1) or (pd.item_type!=3)) \n " +
            "  and pd.id in (${ids}); \n"
    )
    @ResultType(List.class)
    List<Map<String, Object>> selectAllProjectDetailList(@Param("ids") String ids);


    /**
     * @description: 项目查询统计，根据ids递归查找项目结构费用
     * @param: @param null
     * @return:
     * @author: wxy
     * @date: 2024/5/23 11:38
     */
    @Select(" select pd.budget_project_id,pd.id,pd.pid ,pd.name as projectName,  pd.item_type, pd.sequence,pd.total_price+pd.prj_other_money as totalPrice , \n" +
            "pd.build_area as buildArea,pd.item_unit as itemUnit ,'' as remark, \n" +
            " (select group_concat(bu.f_real_name,'')  from ths_business_budget_project_detail_user pdu  \n" +
            " left join base_user bu on bu.F_Id=pdu.user_id \n" +
            "where pdu.detail_id=pd.id and pdu.budget_project_id=pd.budget_project_id  \n" +
            " group by pdu.budget_project_id,pdu.detail_id) as BZR , sub.JHR,sub.HZR,sub.ZG \n" +
            " from ths_business_budget_project_detail pd\n" +
            " left join  ( select pfd.budget_project_id,pfd.detail_id ,\n" +
            " group_concat( case when pfd.role_type=2 then  fbu.f_real_name else null  end ,'') as JHR ,\n" +
            " group_concat( case when pfd.role_type=3 then fbu.f_real_name else null   end ,'') as HZR ,\n" +
            "group_concat( case when pfd.role_type=4 then fbu.f_real_name  else  null end ,'')  as ZG \n" +
            " from ths_business_budget_project_flow_details pfd \n" +
            " left join ths_business_budget_project_flow_details_user  pfdu on pfdu.flow_detail_id=pfd.id  and pfdu.detail_id=pfd.detail_id and pfdu.budget_project_id=pfd.budget_project_id  \n" +
            " left join base_user fbu on fbu.F_Id=pfdu.user_id " +
            " group by pfd.budget_project_id , pfd.detail_id ) sub on sub.detail_id=pd.id and sub.budget_project_id=pd.budget_project_id \n " +
            " where pd.id in (${ids}) ;"
    )
    @ResultType(List.class)
    List<Map<String, Object>> selectProjectDetailMapListByIds(@Param("ids") String ids);

    /**
     * @Description: Todo 工程造价类业务业务收入情况统计明细表
     * @Param: * @param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 16:09 2024/5/29
     */
    @Select("select pd.budget_project_id as budgetProjectId,\n" +
            " pd.total_price as totalMoney\n" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pl on pl.id = pd.budget_project_id\n" +
            " inner join ths_business_consult_project c on c.id = pl.consult_project_id\n" +
            " left join ths_manage_contract tmc on c.contract_id = tmc.id\n" +
            " where pd.item_type = 3\n" +
            " and pd.`status` = 3\n" +
            " and pd.calc_flag = 1\n" +
            " AND IF( #{code} is null or #{code}='' , 1=1 ,tmc.code = #{code})" +
            " AND IF( #{isCompleted} is null or #{isCompleted}='' , 1=1 ,c.is_complete = #{isCompleted})" +
            " AND IF( #{startCompleteDate} is null,1=1,c.project_complete_time >= #{startCompleteDate}) " +
            " AND IF( #{endCompleteDate} is null,1=1,c.project_complete_time <= #{endCompleteDate}) " +
            " AND IF( #{startCommissionDate} is null,1=1,c.commission_date >= #{startCommissionDate}) " +
            " AND IF( #{endCommissionDate} is null,1=1,c.commission_date <= #{endCommissionDate}) " +
            " AND IF( #{allBusinessIdStatus} is null or #{allBusinessIdStatus}='',1=1, c.business_type_id in (${businessTypeIdList})) "
    )
    @ResultType(List.class)
    List<Map<String, Object>> prjDWSql(@Param("code") String code,
                                       @Param("isCompleted") Integer isCompleted,
                                       @Param("startCommissionDate") String startCommissionDate,
                                       @Param("endCommissionDate") String endCommissionDate,
                                       @Param("startCompleteDate") String startCompleteDate,
                                       @Param("endCompleteDate") String endCompleteDate,
                                       @Param("allBusinessIdStatus") String allBusinessIdStatus,
                                       @Param("businessTypeIdList") String businessTypeIdList);


    /**
     * @Description: Todo 先计算所有单项工程的其他费
     * @Param: * @param budgetProjectIds
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 16:13 2024/5/29
     */
    @Select("SELECT\n" +
            " pd.budget_project_id as budgetProjectId,\n" +
            " qtf.total_price as totalMoney" +
            " FROM\n" +
            " ths_business_budget_project_other_fee qtf\n" +
            " INNER JOIN ths_business_budget_project_detail pd ON pd.id = qtf.detail_id\n" +
            " where qtf.pid is null or qtf.pid =''\n" +
            "  and pd.calc_flag = 1\n" +
            "  and (qtf.total_price > 0 or qtf.creator_id is not NULL)\n" +
            "  and pd.budget_project_id in (${budgetProjectIds})")
    @ResultType(List.class)
    List<Map<String, Object>> OtherFee(@Param("budgetProjectIds") String budgetProjectIds);


    /**
     * @description: 有封面的單項工程层
     * @param: @param null
     * @return:
     * @author: wxy
     * @date: 2024/7/16 15:32
     */

    @Select("  select pd.budget_project_id as budgetProjectId ,pd.id,pd.pid,pd.Name as GCName," +
            "  pd.status, pd.item_type,  " +
            "  pd.total_price + pd.prj_other_money as  totalMoney , pd.prj_other_money as prjOtherMoney," +
            "  0.0  as FeatMoney, \n" +
            "  IFNULL(pd.adjust_rate,1) as adjustRate ," +
            "  IFNULL(pd.cur_flow_detail_id,1) as curFlowDetailId ," +
            " case when  pd.have_cover=1  then pd.total_price " +
            "      when  exists (select 1 from ths_business_budget_project_detail pd1 where pd1.id=pd.pid  and pd1.have_cover=1 )  " +
            "      then  (select pd.total_price + pd.prj_other_money as  TotalMoney from ths_business_budget_project_detail pd1 where pd1.id=pd.pid)" +
            "      else 0.00 end as FeatTotalMoney , " +
            " case when  pd.have_cover=1  then 1 " +
            "      when  exists (select 1 from ths_business_budget_project_detail pd1 where pd1.id=pd.pid  and pd1.have_cover=1 )  then 1 " +
            "      else 0 end as TotalType , " +
            " 0.0 as BZMoney," +
            " 0.0 as JHMoney," +
            " 0.0 as SHMoney" +
            " from ths_business_budget_project_detail pd\n" +
            " inner join ths_business_budget_project_version pv on pd.budget_project_id=pv.id\n" +
            " inner join ths_business_consult_project cp on pv.consult_project_id=cp.id\n" +
            " inner join ths_manage_contract mc  on  cp.contract_id=mc.id\n" +
            " where pd.item_type in (2)\n" +
            " and pd.calc_flag=1\n" +
            " and pd.current_version_flag=1 \n" +
            " and pd.status=3  " +
            " and (pd.changed_from_id='' or pd.changed_from_id is null) \n" +
            " and IF(#{finishBeginTime} is null,1=1,pd.finish_time >= #{finishBeginTime})  \n" +
            " and IF(#{finishEndTime} is null,1=1,pd.finish_time <= #{finishEndTime})  \n" +
            " and  cp.business_type_id in (select dict.id from ths_sys_dict_details dict where dict.sys_dist_code='QJ-JHZXXMLX' and dict.item_value_two in (1,3,4,5,6,7,8))  \n" +   //  业务类型 ，自选类型不等于估算 不等于工程变更
            " and  cp.consulting_type_id in ( select dict.id from ths_sys_dict_details dict where dict.sys_dist_code='QJ-ZXLX' and dict.item_text not in ('估算','工程变更备案（预算）','工程变更备案（估算）') ) \n" +
            " and IF( #{budgetProjectIds} is null or #{budgetProjectIds} ='null',1=1, pd.budget_project_id in (${budgetProjectIds})) " +
            " and pd.id in () \n" +      // 单位工程对应的其他费
            " and pd.id not in () \n " +  // 排除其他费已经包含的单项工程
            "; "
    )
    @ResultType(List.class)
    List<Map<String, Object>> selectOtherFeeDXHaveCover(@Param("budgetProjectIds") String budgetProjectIds,
                                                        @Param("finishBeginTime") String finishBeginTime,
                                                        @Param("finishEndTime") String finishEndTime);


    /**
     * @param BeginDate
     * @param EndDate
     * @param IsRequestReturn
     * @param ReqRetBeginDate
     * @param ReqRetEndDate
     * @description:绩效计算有封面的项目数据抓取
     * @param: @param HTBH
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: wxy
     * @date: 2024/7/17 15:24
     */
    @Select("CALL p_GetFeat_ZJ_Project(" +
            "#{HTBH, jdbcType=VARCHAR}, " +
            "#{BeginDate, jdbcType=VARCHAR}, " +
            "#{EndDate, jdbcType=VARCHAR}, " +
            "#{IsRequestReturn, jdbcType=INTEGER}, " +
            "#{ReqRetBeginDate, jdbcType=VARCHAR}, " +
            "#{ReqRetEndDate, jdbcType=VARCHAR})")
    @Options(statementType = StatementType.CALLABLE)
    @ResultType(List.class)
    List<Map<String, Object>> callGetFeatZJ(
            @Param("HTBH") String HTBH,
            @Param("BeginDate") String BeginDate,
            @Param("EndDate") String EndDate,
            @Param("IsRequestReturn") Integer IsRequestReturn,
            @Param("ReqRetBeginDate") String ReqRetBeginDate,
            @Param("ReqRetEndDate") String ReqRetEndDate);




    /**
     * @param BeginDate
     * @param EndDate
     * @param IsRequestReturn
     * @param ReqRetBeginDate
     * @param ReqRetEndDate
     * @description:绩效计算研究分析类
     * @param: @param HTBH
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: wxy
     * @date: 2024/7/17 15:24
     */
    @Select("CALL p_GetFeat_YJFX_Project(" +
            "#{HTBH, jdbcType=VARCHAR}, " +
            "#{BeginDate, jdbcType=VARCHAR}, " +
            "#{EndDate, jdbcType=VARCHAR}, " +
            "#{IsRequestReturn, jdbcType=INTEGER}, " +
            "#{ReqRetBeginDate, jdbcType=VARCHAR}, " +
            "#{ReqRetEndDate, jdbcType=VARCHAR})")
    @Options(statementType = StatementType.CALLABLE)
    @ResultType(List.class)
    List<Map<String, Object>> callGetFeatYJFX(
            @Param("HTBH") String HTBH,
            @Param("BeginDate") String BeginDate,
            @Param("EndDate") String EndDate,
            @Param("IsRequestReturn") Integer IsRequestReturn,
            @Param("ReqRetBeginDate") String ReqRetBeginDate,
            @Param("ReqRetEndDate") String ReqRetEndDate);
    /**
     * @param BeginDate
     * @param EndDate
     * @param IsRequestReturn
     * @param ReqRetBeginDate
     * @param ReqRetEndDate
     * @description: 绩效计算贷款类项目的数据查询
     * @param: @param HTBH
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: wxy
     * @date: 2024/8/19 9:13
     */
    @Select("CALL p_GetFeat_DK_Project(" +
            "#{HTBH, jdbcType=VARCHAR}, " +
            "#{BeginDate, jdbcType=VARCHAR}, " +
            "#{EndDate, jdbcType=VARCHAR}, " +
            "#{IsRequestReturn, jdbcType=INTEGER}, " +
            "#{ReqRetBeginDate, jdbcType=VARCHAR}, " +
            "#{ReqRetEndDate, jdbcType=VARCHAR})")
    @Options(statementType = StatementType.CALLABLE)
    @ResultType(List.class)
    List<Map<String, Object>> callGetFeatZJ_DK(
            @Param("HTBH") String HTBH,
            @Param("BeginDate") String BeginDate,
            @Param("EndDate") String EndDate,
            @Param("IsRequestReturn") Integer IsRequestReturn,
            @Param("ReqRetBeginDate") String ReqRetBeginDate,
            @Param("ReqRetEndDate") String ReqRetEndDate);


    @Select(" select * from ths_business_budget_project_detail " +
            " where " +
            "   budget_project_id = #{projectId} and " +
            "   ( " +
            "     (item_type = 1) or " +
            "     (item_type <> 1 and current_version_flag = 1) " +
            "   ) " +
            " order by changed_ver desc, pid desc, item_type asc, sequence asc ")
    @ResultType(List.class)
    List<Map<String, Object>> getThsBusinessBudgetProjectDetailByProjectId(@Param("projectId") String projectId);


    @Select("SELECT * FROM ths_business_budget_project_detail " +
            "WHERE  IF( #{budgetProjectId} is null or #{budgetProjectId}='' , 1=0 ,budget_project_id = #{budgetProjectId}) " +
            "AND id NOT IN (SELECT DISTINCT pid FROM ths_business_budget_project_detail WHERE pid IS NOT NULL)  " +
            "AND current_version_flag=1 ")
    List<ThsBusinessBudgetProjectDetailEntity> selectLeafEntityListByBudgetProjectIdAndDetailId(@Param("budgetProjectId") String budgetProjectId);


    @Select("select * from ths_business_budget_project_detail " +
            "         where budget_project_id in (select id from ths_business_budget_project_version where consult_project_id in (${consultProjectId})" +
            " and is_finalize =1 ) and item_type =1")
    @Results({
            @Result(column = "budget_project_id", property = "budgetProjectId"),
            @Result(column = "consulting_type_id", property = "consultingTypeId"),
            @Result(column = "editorial_type_id", property = "editorialTypeId"),
            @Result(column = "send_total_price", property = "sendTotalPrice"),
            @Result(column = "total_price", property = "totalPrice"),
            @Result(column = "item_type", property = "itemType"),
            @Result(column = "prj_other_money", property = "prjOtherMoney"),
            @Result(column = "send_prj_other_money", property = "sendPrjOtherMoney"),
    })
    List<ThsBusinessBudgetProjectDetailEntity> selectByConsultProjectIds(@Param("consultProjectId") String consultProjectId);


    @Select("select max(finish_time) as costCompletionTime from ths_business_budget_project_detail where  budget_project_id in " +
            "(select id from ths_business_budget_project_version where is_finalize =1 and consult_project_id in (${consultProjectId})) and  item_type =1 and  status=3 ")
    Map<String, Object> getCostCompletionTime(@Param("consultProjectId") String consultProjectId);

}
