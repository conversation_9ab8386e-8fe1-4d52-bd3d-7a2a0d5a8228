package jnpf.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xuyanwu.spring.file.storage.FileInfo;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.base.model.ColumnDataModel;
import jnpf.database.util.DynamicDataSourceUtil;
import jnpf.entity.*;
import jnpf.errors.BadRequestAlertException;
import jnpf.exception.DataException;
import jnpf.mapper.ThsBusinessBudgetProjectVersionMapper;
import jnpf.model.QueryAllModel;
import jnpf.model.thsbusinessbudgetprojectdetail.ThsFileQdgStructureVo;
import jnpf.model.thsbusinessbudgetprojectdetailexcelreport.ThsBusinessBudgetProjectDetailExcelReportForm;
import jnpf.model.thsbusinessbudgetprojecttemplate.ThsBusinessBudgetProjectTemplateForm;
import jnpf.model.thsbusinessbudgetprojectversion.ThsBusinessBudgetProjectVersionConstant;
import jnpf.model.thsbusinessbudgetprojectversion.ThsBusinessBudgetProjectVersionForm;
import jnpf.model.thsbusinessbudgetprojectversion.ThsBusinessBudgetProjectVersionPagination;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.UserService;
import jnpf.service.*;
import jnpf.util.*;
import lombok.Cleanup;
import me.zhyd.oauth.log.Log;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static jnpf.util.MapUtils.generateInsertStatement;


/**
 * 协同计价版本管理
 * 版本： V3.5
 * 版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * 作者： JNPF开发平台组
 * 日期： 2023-09-05
 */
@Service
public class ThsBusinessBudgetProjectVersionServiceImpl extends ServiceImpl<ThsBusinessBudgetProjectVersionMapper, ThsBusinessBudgetProjectVersionEntity> implements ThsBusinessBudgetProjectVersionService {

    private final Logger log = LoggerFactory.getLogger(ThsBusinessBudgetProjectVersionServiceImpl.class);
    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private UserService userService;

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectDetailService thsBusinessBudgetProjectDetailService;

    @Lazy
    @Autowired
    private ThsBusinessConsultProjectService thsBusinessConsultProjectService;

    @Autowired
    private ThsBusinessBudgetProjectFlowDetailsUserService thsBusinessBudgetProjectFlowDetailsUserService;

    @Autowired
    private ThsBusinessBudgetProjectFlowDetailsService thsBusinessBudgetProjectFlowDetailsService;

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectDetailUserService thsBusinessBudgetProjectDetailUserService;


    @Autowired
    private ExecuteSqlUtils executeSqlUtils;

    @Autowired
    private Environment environment;

    @Autowired
    private ThsSysFlowDetailsService thsSysFlowDetailsService;
//    @Autowired
//    private ZbCalcCaseProjectApi zbCalcCaseProjectApi;


    @Autowired
    private ThsfileQdgStructureService thsfileQdgStructureService;


//    @Autowired
//    @Lazy
//    private LYIndicatorClient lyIndicatorClient;

    @Autowired
    private ThsfileQdyInformationService thsfileQdyInformationService;

    @Autowired
    private ThsfileQdyResourceSummaryService thsfileQdyResourceSummaryService;

    @Autowired
    private ThsfileQdyBudgetService thsfileQdyBudgetService;


    @Autowired
    private ThsfileQdyBudgetResourceComponentService thsfileQdyBudgetResourceComponentService;

    @Autowired
    private ThsfileQdyBudgetFeatureService thsfileQdyBudgetFeatureService;

    @Autowired
    private ThsfileQdyBudgetContontService thsfileQdyBudgetContontService;

    @Autowired
    private ThsfileQdyMeasureService thsfileQdyMeasureService;

    @Autowired
    private ThsfileQdyMeasureResourceComponentService thsfileQdyMeasureResourceComponentService;

    @Autowired
    private ThsfileQdyMeasureFeatureService thsfileQdyMeasureFeatureService;

    @Autowired
    private ThsfileQdyMeasureContontService thsfileQdyMeasureContontService;

    @Autowired
    @Lazy
    private ThsSysDictDetailsService thsSysDictDetailsService;

//    @Autowired
//    private LYIndicatorTemplateApi lyIndicatorTemplateApi;
//
//
//    @Autowired
//    private LYIndicatorApi lyIndicatorApi;

    @Autowired
    private ThsBusinessBudgetProjectInstructionsService thsBusinessBudgetProjectInstructionsService;

    @Autowired
    private ThsBusinessBudgetProjectDetailFileHistoryService thsBusinessBudgetProjectDetailFileHistoryService;

    @Lazy
    @Autowired
    private ThsBusinessBudgetProjectFlowHistoryService thsBusinessBudgetProjectFlowHistoryService;

    @Autowired
    private ThsBusinessBudgetProjectDetailFileService thsBusinessBudgetProjectDetailFileService;

    @Autowired
    private ThsBusinessBudgetProjectMachineService thsBusinessBudgetProjectMachineService;

    @Autowired
    private ThsBusinessBudgetProjectOtherFeeService thsBusinessBudgetProjectOtherFeeService;

    @Autowired
    private ThsBusinessBudgetProjectAuditRemarkService thsBusinessBudgetProjectAuditRemarkService;

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectDetailContractFileService thsBusinessBudgetProjectDetailContractFileService;


    @Autowired
    private ThsfileFilesService thsfileFilesService;

//    @Autowired
//    private ZbcalcCaseProjectTaskApi zbcalcCaseProjectTaskApi;

    @Autowired
    private UserService userApi;

    @Autowired
    private ThsBusinessBudgetProjectDetailExcelReportService thsBusinessBudgetProjectDetailExcelReportService;

    @Autowired
    private ThsBusinessBudgetProjectDetailExcelReportDetailsService thsBusinessBudgetProjectDetailExcelReportDetailsService;

    // 创建缓存，默认5分钟过期
    private static final TimedCache<String, byte[]> timedCache = CacheUtil.newTimedCache(1000 * 60 * 5);

    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getList(ThsBusinessBudgetProjectVersionPagination thsBusinessBudgetProjectVersionPagination) {
        return getTypeList(thsBusinessBudgetProjectVersionPagination, thsBusinessBudgetProjectVersionPagination.getDataType());
    }

    /**
     * 获取计价模板列表
     *
     * @param thsBusinessBudgetProjectVersionPagination
     * @return
     */
    @Override
    public List<ThsBusinessBudgetProjectTemplateForm> getTemplateFormList(ThsBusinessBudgetProjectVersionPagination thsBusinessBudgetProjectVersionPagination) {
        thsBusinessBudgetProjectVersionPagination.setTemplateCase(ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_TEMPLATE);
        List<ThsBusinessBudgetProjectVersionEntity> thsBusinessBudgetProjectVersionEntityList = getTypeList(thsBusinessBudgetProjectVersionPagination, thsBusinessBudgetProjectVersionPagination.getDataType());
        List<UserEntity> userEntityList = userService.getList(false);
        List<ThsBusinessBudgetProjectTemplateForm> thsBusinessBudgetProjectTemplateFormList = new ArrayList<ThsBusinessBudgetProjectTemplateForm>();

        for (ThsBusinessBudgetProjectVersionEntity entity : thsBusinessBudgetProjectVersionEntityList) {
            ThsBusinessBudgetProjectTemplateForm thsBusinessBudgetProjectTemplateForm = JsonUtil.getJsonToBean(entity, ThsBusinessBudgetProjectTemplateForm.class);
            // 负责人名称
            Optional<UserEntity> user = userEntityList.stream().filter(userEntity -> userEntity.getId().equals(entity.getIssuerId())).findFirst();
            if (ObjectUtil.isNotEmpty(user) && user.isPresent()) {
                thsBusinessBudgetProjectTemplateForm.setIssuerCode(user.get().getAccount());
                thsBusinessBudgetProjectTemplateForm.setIssuerName(user.get().getRealName());
            }
            // 创建人名称
            // 负责人名称
            user = userEntityList.stream().filter(userEntity -> userEntity.getId().equals(entity.getCreatorId())).findFirst();
            if (ObjectUtil.isNotEmpty(user) && user.isPresent()) {
                thsBusinessBudgetProjectTemplateForm.setCreatorCode(user.get().getAccount());
                thsBusinessBudgetProjectTemplateForm.setCreatorName(user.get().getRealName());
            }
            thsBusinessBudgetProjectTemplateFormList.add(thsBusinessBudgetProjectTemplateForm);
        }
        return thsBusinessBudgetProjectTemplateFormList;
    }

    /**
     * 列表查询
     */
    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getTypeList(ThsBusinessBudgetProjectVersionPagination thsBusinessBudgetProjectVersionPagination, String dataType) {
        String userId = userProvider.get().getUserId();
        Map<String, Class> tableClassMap = new HashMap<>();
        tableClassMap.put("ths_business_budget_project_version", ThsBusinessBudgetProjectVersionEntity.class);

        MPJLambdaWrapper<ThsBusinessBudgetProjectVersionEntity> wrapper = JoinWrappers
                .lambda("ths_business_budget_project_version", ThsBusinessBudgetProjectVersionEntity.class)
                .selectAll(ThsBusinessBudgetProjectVersionEntity.class);
        MPJLambdaWrapper<ThsBusinessBudgetProjectVersionEntity> wrapper2 = JoinWrappers
                .lambda("ths_business_budget_project_version", ThsBusinessBudgetProjectVersionEntity.class)
                .distinct().select(ThsBusinessBudgetProjectVersionEntity::getId);

        QueryAllModel queryAllModel = new QueryAllModel();
        queryAllModel.setWrapper(wrapper);
        queryAllModel.setClassMap(tableClassMap);
        queryAllModel.setDbLink(ThsBusinessBudgetProjectVersionConstant.DBLINKID);
        // 数据过滤
        boolean isPc = ServletUtil.getHeader("jnpf-origin").equals("pc");
        String columnData = !isPc ? ThsBusinessBudgetProjectVersionConstant.getAppColumnData() : ThsBusinessBudgetProjectVersionConstant.getColumnData();
        ColumnDataModel columnDataModel = JsonUtil.getJsonToBean(columnData, ColumnDataModel.class);
        String ruleJson = !isPc ? JsonUtil.getObjectToString(columnDataModel.getRuleListApp()) : JsonUtil.getObjectToString(columnDataModel.getRuleList());
        if (ObjectUtil.isNotEmpty(ruleJson) && !ruleJson.equals("null")) {
            queryAllModel.setRuleJson(ruleJson);
        }
        // 高级查询
        boolean hasSuperQuery = true;
        if (hasSuperQuery) {
            queryAllModel.setSuperJson(thsBusinessBudgetProjectVersionPagination.getSuperQueryJson());
        }
        // 数据权限
        boolean pcPermission = false;
        boolean appPermission = false;
        if (isPc && pcPermission && !userProvider.get().getIsAdministrator()) {
            queryAllModel.setModuleId(thsBusinessBudgetProjectVersionPagination.getMenuId());
            queryAllModel.setModuleId(thsBusinessBudgetProjectVersionPagination.getMenuId());
        }
        if (!isPc && appPermission && !userProvider.get().getIsAdministrator()) {
            queryAllModel.setModuleId(thsBusinessBudgetProjectVersionPagination.getMenuId());
        }
        // 拼接复杂条件
        wrapper = generaterSwapUtil.getConditionAllTable(queryAllModel);
        queryAllModel.setWrapper(wrapper2);
        wrapper2 = generaterSwapUtil.getConditionAllTable(queryAllModel);
        // 其他条件拼接
        otherConditions(thsBusinessBudgetProjectVersionPagination, wrapper, isPc);
        otherConditions(thsBusinessBudgetProjectVersionPagination, wrapper2, isPc);

        if ("0".equals(dataType)) {
            com.github.pagehelper.Page<Object> objects = PageHelper.startPage((int) thsBusinessBudgetProjectVersionPagination.getCurrentPage(), (int) thsBusinessBudgetProjectVersionPagination.getPageSize(), true);
            List<ThsBusinessBudgetProjectVersionEntity> userIPage = this.selectJoinList(ThsBusinessBudgetProjectVersionEntity.class, wrapper2);
            List<Object> collect = userIPage.stream().map(t -> t.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                wrapper.in(ThsBusinessBudgetProjectVersionEntity::getId, collect);
            }
            List<ThsBusinessBudgetProjectVersionEntity> result = this.selectJoinList(ThsBusinessBudgetProjectVersionEntity.class, wrapper);
            return thsBusinessBudgetProjectVersionPagination.setData(result, objects.getTotal());
        } else {
            List<ThsBusinessBudgetProjectVersionEntity> list = this.selectJoinList(ThsBusinessBudgetProjectVersionEntity.class, wrapper);
            if ("2".equals(dataType)) {
                List<String> selectIds = Arrays.asList(thsBusinessBudgetProjectVersionPagination.getSelectIds());
                return list.stream().filter(t -> selectIds.contains(t.getId())).collect(Collectors.toList());
            } else {
                return list;
            }
        }

    }

    /**
     * 其他条件拼接
     */
    private void otherConditions(ThsBusinessBudgetProjectVersionPagination thsBusinessBudgetProjectVersionPagination, MPJLambdaWrapper<ThsBusinessBudgetProjectVersionEntity> wrapper, boolean isPc) {
        String databaseName;
        try {
            @Cleanup Connection cnn = DynamicDataSourceUtil.getCurrentConnection();
            databaseName = cnn.getMetaData().getDatabaseProductName().trim();
        } catch (SQLException e) {
            throw new DataException(e.getMessage());
        }
        // 关键词
        if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getJnpfKeyword())) {
        }
        // 普通查询
        if (isPc) {
            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getName())) {
                String value = thsBusinessBudgetProjectVersionPagination.getName() instanceof List ?
                        JsonUtil.getObjectToString(thsBusinessBudgetProjectVersionPagination.getName()) :
                        String.valueOf(thsBusinessBudgetProjectVersionPagination.getName());
                wrapper.like(ThsBusinessBudgetProjectVersionEntity::getName, value);
            }

        }

        if (isPc) {
            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getCode())) {

                wrapper.like(ThsBusinessBudgetProjectVersionEntity::getCode, thsBusinessBudgetProjectVersionPagination.getCode());

            }

            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getName())) {

                wrapper.like(ThsBusinessBudgetProjectVersionEntity::getName, thsBusinessBudgetProjectVersionPagination.getName());

            }
            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getIsFinalize())) {

                wrapper.eq(ThsBusinessBudgetProjectVersionEntity::getIsFinalize, thsBusinessBudgetProjectVersionPagination.getIsFinalize());

            }
            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getStatus())) {
                List<Integer> statusList = (List<Integer>) thsBusinessBudgetProjectVersionPagination.getStatus();
                wrapper.in(ThsBusinessConsultProjectEntity::getStatus, statusList);
            }

            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getConsultProjectId())) {

                Object ConsultProjectIdObject = thsBusinessBudgetProjectVersionPagination.getConsultProjectId();
                if (ConsultProjectIdObject instanceof List) {
                    List<String> ConsultProjectIdList = (List<String>) ConsultProjectIdObject;

                    // 使用 stream 和 lambda 表达式创建查询条件
                    wrapper.in(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, ConsultProjectIdList);
                }
            }

            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getDeptId())) {

                wrapper.like(ThsBusinessBudgetProjectVersionEntity::getDeptId, thsBusinessBudgetProjectVersionPagination.getDeptId());

            }

            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getCategoryDictId())) {

                Object categoryDictIdObject = thsBusinessBudgetProjectVersionPagination.getCategoryDictId();
                if (categoryDictIdObject instanceof List) {
                    List<String> categoryDictIdList = (List<String>) categoryDictIdObject;

                    // 使用 stream 和 lambda 表达式创建查询条件
                    wrapper.in(ThsBusinessBudgetProjectVersionEntity::getCategoryDictId, categoryDictIdList);
                }
            }

            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionPagination.getTemplateCase())) {

                wrapper.eq(ThsBusinessBudgetProjectVersionEntity::getTemplateCase, thsBusinessBudgetProjectVersionPagination.getTemplateCase());
            }
        }
        // 排序
        if (StringUtil.isEmpty(thsBusinessBudgetProjectVersionPagination.getSidx())) {
            wrapper.orderByDesc(ThsBusinessBudgetProjectVersionEntity::getId);
        } else {
            try {
                String[] split = thsBusinessBudgetProjectVersionPagination.getSidx().split(",");
                for (String sidx : split) {
                    ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = new ThsBusinessBudgetProjectVersionEntity();
                    String oderTableField = thsBusinessBudgetProjectVersionEntity.getClass().getAnnotation(TableName.class).value();
                    if (sidx.startsWith("-")) {
                        Field declaredField = thsBusinessBudgetProjectVersionEntity.getClass().getDeclaredField(sidx.substring(1));
                        declaredField.setAccessible(true);
                        oderTableField = oderTableField + "." + declaredField.getAnnotation(TableField.class).value();
                        wrapper.select(oderTableField);
                        wrapper.orderByDesc(oderTableField);
                    } else {
                        Field declaredField = thsBusinessBudgetProjectVersionEntity.getClass().getDeclaredField(sidx);
                        declaredField.setAccessible(true);
                        oderTableField = oderTableField + "." + declaredField.getAnnotation(TableField.class).value();
                        wrapper.select(oderTableField);
                        wrapper.orderByAsc(oderTableField);
                    }
                }
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
        System.out.println("wrapper.getTargetSql().toString()");
        System.out.println(wrapper.getTargetSql());
    }

    @Override
    public ThsBusinessBudgetProjectVersionEntity getInfo(String id) {
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectVersionEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(ThsBusinessBudgetProjectVersionEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, ThsBusinessBudgetProjectVersionEntity entity) {
        return this.updateById(entity);
    }

    @Override
    public void delete(ThsBusinessBudgetProjectVersionEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }

    /**
     * 验证表单唯一字段，正则，非空 i-0新增-1修改
     */
    @Override
    public String checkForm(ThsBusinessBudgetProjectVersionForm form, int i) {
        boolean isUp = StringUtil.isNotEmpty(form.getId()) && !form.getId().equals("0");
        String id = "";
        String countRecover = "";
        if (isUp) {
            id = form.getId();
        }
        // 主表字段验证
        return countRecover;
    }

    /**
     * 通过部门获取协同计价版本
     *
     * @param deptId
     * @return
     */
    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getListBydeptId(String deptId) {
        String userId = userProvider.get().getUserId();
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectVersionEntity::getDeptId, deptId);
        return this.list(queryWrapper);
    }

    @Override
    public Integer getMaxSequence() {
        return this.getBaseMapper().selectMaxSequence();

    }

    @Override
    public Integer GetMaxVerByConsultId(String consultId) {
        return this.getBaseMapper().selectMaxVerByConsultID(consultId);
    }

    @Override
    public String checkTemplateForm(ThsBusinessBudgetProjectTemplateForm form, int i) {
        boolean isUp = StringUtil.isNotEmpty(form.getId()) && !form.getId().equals("0");
        String id = "";
        String countRecover = "";
        if (isUp) {
            id = form.getId();
        }
        // 主表字段验证
        return countRecover;
    }

    /**
     * @param entity
     * @param templateCase 0：项目创建计价文件结构 ，1：模板创建
     */
    @Override
    public boolean SaveBusinessBudgetProjectDetailJSXM(ThsBusinessBudgetProjectVersionEntity entity, int templateCase) {

        //  判断建设项目层节点是否存在
        boolean exists = thsBusinessBudgetProjectDetailService.lambdaQuery().eq(ThsBusinessBudgetProjectDetailEntity::getBudgetProjectId, entity.getId()).eq(ThsBusinessBudgetProjectDetailEntity::getItemType, ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_JSXM).exists();
        if (!exists) {
            //  初始化实体工程
            ThsBusinessBudgetProjectDetailEntity detailEntity = new ThsBusinessBudgetProjectDetailEntity();
            String mainId = RandomUtil.uuId();
            UserInfo userInfo = userProvider.get();
            UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());

            detailEntity.setId(mainId);
            // 创建人创建时间
            detailEntity.setCreateTime(Date.from(Instant.now()));
            detailEntity.setCreatorId(userInfo.getUserId());
            // 新建编制中
            detailEntity.setStatus(0);

            detailEntity.setCreateTime(Date.from(Instant.now()));

            if (thsBusinessBudgetProjectDetailService.getMaxSequence() != null) {
                entity.setSequence(thsBusinessBudgetProjectDetailService.getMaxSequence() + 1);
            } else {
                entity.setSequence(1);
            }
            detailEntity.setItemType(ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_JSXM);
            detailEntity.setBudgetProjectId(entity.getId());
            detailEntity.setPid("");
            detailEntity.setCurrentVersionFlag(true);
            //  设置文件名称
            switch (templateCase) {
                case ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT: {
                    // 建设项目名称
                    detailEntity.setName(entity.getName());
//                    ThsBusinessConsultProjectEntity ConsultProjectEntity = thsBusinessConsultProjectService.getInfo(entity.getConsultProjectId());
//                    if (ObjectUtil.isNotEmpty(ConsultProjectEntity))
//                        detailEntity.setName(ConsultProjectEntity.getName());
                }
                break;
                case ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_TEMPLATE:
                    detailEntity.setName(entity.getName());
                    break;
            }
            boolean res = thsBusinessBudgetProjectDetailService.save(detailEntity);
            // 根据其他费模板创建其他费数据
            res = res && thsBusinessBudgetProjectDetailService.copyBusinessBudgetProjectOtherFee(detailEntity);
            if (!res) {
                log.error("根据模版创建其他费新建失败");
                return false;
            }

            if (ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT == templateCase)
                res = res && thsBusinessBudgetProjectDetailService.createBudgetProjectFlowDetails(entity.getSysFlowId(), detailEntity);
            if (!res) {
                log.error("创建流程失败");
                return false;
            }
            return res;
        }
        return true;
    }

    /**
     * 删除协同计价版本管理及其子表
     *
     * @param entity
     * @return
     */
    @Override
    public boolean deleteBusinessBudgetProjectVersionAndChildrenTables(ThsBusinessBudgetProjectVersionEntity entity) {
        try {
            if (ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT == entity.getTemplateCase()) {
                // 删除审批人员表
                QueryWrapper<ThsBusinessBudgetProjectFlowDetailsUserEntity> budgetProjectFlowDetailsUserEntityQueryWrapper = new QueryWrapper<>();
                budgetProjectFlowDetailsUserEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectFlowDetailsUserEntity::getBudgetProjectId, entity.getId());
                thsBusinessBudgetProjectFlowDetailsUserService.remove(budgetProjectFlowDetailsUserEntityQueryWrapper);
                // 删除审批流程
                QueryWrapper<ThsBusinessBudgetProjectFlowDetailsEntity> budgetProjectFlowDetailsEntityQueryWrapper = new QueryWrapper<>();
                budgetProjectFlowDetailsEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectFlowDetailsEntity::getBudgetProjectId, entity.getId());
                thsBusinessBudgetProjectFlowDetailsService.remove(budgetProjectFlowDetailsEntityQueryWrapper);
                // 删除编制人员
                QueryWrapper<ThsBusinessBudgetProjectDetailUserEntity> budgetProjectDetailUserEntityQueryWrapper = new QueryWrapper<>();
                budgetProjectDetailUserEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailUserEntity::getBudgetProjectId, entity.getId());
                thsBusinessBudgetProjectDetailUserService.remove(budgetProjectDetailUserEntityQueryWrapper);
            }
            // 删除计价文件结构
            QueryWrapper<ThsBusinessBudgetProjectDetailEntity> budgetProjectDetailEntityQueryWrapper = new QueryWrapper<>();
            budgetProjectDetailEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailEntity::getBudgetProjectId, entity.getId());

            List<ThsBusinessBudgetProjectDetailEntity> budgetProjectDetailEntityList = thsBusinessBudgetProjectDetailService.list(budgetProjectDetailEntityQueryWrapper);

            if (ObjectUtil.isNotEmpty(budgetProjectDetailEntityList)) {
                for (ThsBusinessBudgetProjectDetailEntity budgetProjectDetailEntity : budgetProjectDetailEntityList) {
                    //  删除相关信息
                    thsBusinessBudgetProjectDetailService.deleteAll(budgetProjectDetailEntity);
                }
            }
            thsBusinessBudgetProjectDetailService.remove(budgetProjectDetailEntityQueryWrapper);

            // 删除详情
            this.delete(entity);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 我的编审项目列表
     *
     * @param UserId
     * @return
     */

    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getMyTaskToDoProjectBudgetListByUserId(String UserId) {
        return this.baseMapper.selectMyTaskToDoProjectBudgetListByUserId(UserId);
    }


    /**
     * 修改当前文件编制状态
     *
     * @param id     文件ID
     * @param status 状态
     * @return
     * <AUTHOR>
     * @date 2023/9/15 15:01
     */
    @Override
    public void updateCurrentStatus(String id, Integer status) {
        try {
            ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
            if (budgetProjectVersionEntity != null) {
                budgetProjectVersionEntity.setStatus(status);
                this.updateById(budgetProjectVersionEntity);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 取消终版
     *
     * @param id
     * @return
     */
    public boolean unsetFinalVersion(String id) {
        try {
            ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
            if (budgetProjectVersionEntity != null) {
                budgetProjectVersionEntity.setIsFinalize(false);
                return this.updateById(budgetProjectVersionEntity);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
        return false;
    }

    /**
     * 设定终版
     *
     * @param id
     * @return
     */
    public boolean setFinalVersion(String id) {
        boolean res = false;
        try {
            ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
            if (budgetProjectVersionEntity != null) {
                // 设置当前版本为终版
                budgetProjectVersionEntity.setIsFinalize(true);
                res = this.updateById(budgetProjectVersionEntity);
            }

            // 构建查询条件
            QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, budgetProjectVersionEntity.getConsultProjectId())
                    .ne(ThsBusinessBudgetProjectVersionEntity::getId, id);

            // 查询数据


            List<ThsBusinessBudgetProjectVersionEntity> existingEntities = this.list(queryWrapper);
            // 判断是否查询到数据
            if (existingEntities != null && !existingEntities.isEmpty()) {
                UpdateWrapper<ThsBusinessBudgetProjectVersionEntity> wrapper = new UpdateWrapper<>();
                wrapper.lambda().set(ThsBusinessBudgetProjectVersionEntity::getIsFinalize, false).eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, budgetProjectVersionEntity.getConsultProjectId()).ne(ThsBusinessBudgetProjectVersionEntity::getId, id);
                res = res && this.update(wrapper);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
        return res;
    }

    /***
     * @description:设置归档状态
     * @param: @param id
     * @return: boolean
     * @author: wxy
     * @date: 2024/1/31 16:13
     */
    @Override
    public boolean setArchiveStatus(Integer indicatorsArchiveStatus, Integer caseArchiveStatus, String id) {
        boolean res = false;
        try {
            ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
            if (budgetProjectVersionEntity != null) {
                // 设置归档状态
                budgetProjectVersionEntity.setCaseArchiveStatus(caseArchiveStatus);
                budgetProjectVersionEntity.setIndicatorsArchiveStatus(indicatorsArchiveStatus);
                res = this.updateById(budgetProjectVersionEntity);

                // 设置其他的为非终版
                // 构建查询条件
                QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, budgetProjectVersionEntity.getConsultProjectId())
                        .ne(ThsBusinessBudgetProjectVersionEntity::getId, id);
                // 查询数据
                List<ThsBusinessBudgetProjectVersionEntity> existingEntities = this.list(queryWrapper);
                // 判断是否查询到数据
                if (existingEntities != null && !existingEntities.isEmpty()) {
                    UpdateWrapper<ThsBusinessBudgetProjectVersionEntity> wrapper = new UpdateWrapper<>();
                    wrapper.lambda().set(ThsBusinessBudgetProjectVersionEntity::getCaseArchiveStatus, false)
                            .set(ThsBusinessBudgetProjectVersionEntity::getIndicatorsArchiveStatus, false)
                            .eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, budgetProjectVersionEntity.getConsultProjectId()).ne(ThsBusinessBudgetProjectVersionEntity::getId, id);
                    res = res && this.update(wrapper);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
        return res;
    }


    /**
     * @return java.lang.String
     * @Description 拷贝一个文件返回新的文件路径
     * <AUTHOR>
     * @Date 2023/10/12 19:24
     * @Param [budgetProjectDetails]
     **/
    @Override
    public String copyFileByPath(ThsBusinessBudgetProjectDetailEntity budgetProjectDetails, String path) throws Exception {
        String sourceFile = budgetProjectDetails.getFileSavePath();
        String targetName = UUID.randomUUID() + sourceFile.substring(sourceFile.lastIndexOf("."));
        String targetFile = sourceFile.substring(0, sourceFile.lastIndexOf("/") + 1) + targetName;
        // 拷贝文件


        String filePath = environment.getProperty("config.file-storage.local-plus[0].base-path");
        Log.debug(filePath);
        FileUtil.copy(filePath + sourceFile, filePath + targetFile, true);
        return targetFile;
    }

    /**
     * @return java.lang.String
     * @Description 拷贝一个文件返回新的文件路径
     * <AUTHOR>
     * @Date 2023/10/12 19:24
     * @Param [budgetProjectDetails]
     **/
    public String copyFileBySourceFile(String sourceFile, String path) throws Exception {
        String targetName;
        int index = sourceFile.lastIndexOf(".");
        if (index != -1) {
            targetName = UUID.randomUUID() + sourceFile.substring(index);
        } else {
            targetName = UUID.randomUUID().toString();
        }
        String targetFile = sourceFile.substring(0, sourceFile.lastIndexOf("/") + 1) + targetName;
        // 拷贝文件

        String filePath = environment.getProperty("config.file-storage.local-plus[0].base-path");
        Log.debug(filePath);
        FileUtil.copy(filePath + sourceFile, filePath + targetFile, true);
        return targetFile;
    }


    /**
     * @return void
     * @Description 添加流程表
     * <AUTHOR>
     * @Date 2023/10/12 8:59
     * @Param [sourceId, newProjectId, sqlList]
     **/
    public void addFlowTable(String sourceId, String newProjectId, ArrayList<String> sqlList, Map<String, String> mapString) {

        List<ThsBusinessBudgetProjectDetailUserEntity> budgetProjectDetailsUserList = thsBusinessBudgetProjectDetailUserService.findByProjectIdEqualsOrderBySequenceAsc(sourceId);
        List<ThsBusinessBudgetProjectFlowDetailsEntity> budgetProjectFlowDetailsList = thsBusinessBudgetProjectFlowDetailsService.findByBudgetProjectIdEqualsOrderBySequenceAsc(sourceId);
        List<ThsBusinessBudgetProjectFlowDetailsUserEntity> budgetProjectFlowDetailsUserList = thsBusinessBudgetProjectFlowDetailsUserService.findByBudgetProjectIdEqualsOrderBySequenceAsc(sourceId);
        // 设置   ths_business_budget_project_detail_user
        for (Map.Entry<String, String> entry : mapString.entrySet()) {
            List<ThsBusinessBudgetProjectDetailUserEntity> budgetProjectDetailsUsers = budgetProjectDetailsUserList.stream().filter(budgetProjectDetailsUser -> budgetProjectDetailsUser.getDetailId().equals(entry.getKey())).collect(Collectors.toList());
            budgetProjectDetailsUsers.forEach(budgetProjectDetailsUser -> {
                // 复制对象
                ThsBusinessBudgetProjectDetailUserEntity budgetProjectDetailsUserNew = new ThsBusinessBudgetProjectDetailUserEntity();
                BeanUtil.copyProperties(budgetProjectDetailsUser, budgetProjectDetailsUserNew);
                budgetProjectDetailsUserNew.setId(UUID.randomUUID().toString());
                budgetProjectDetailsUserNew.setDetailId(entry.getValue());
                budgetProjectDetailsUserNew.setBudgetProjectId(newProjectId);
                budgetProjectDetailsUserNew.setFinishState(0);
                sqlList.add(generateInsertStatement("ths_business_budget_project_detail_user", budgetProjectDetailsUserNew));
            });
        }

        // 设置 ths_business_budget_project_flow_details

        for (Map.Entry<String, String> entry : mapString.entrySet()) {
            List<ThsBusinessBudgetProjectFlowDetailsEntity> BudgetProjectFlowDetails = budgetProjectFlowDetailsList.stream().filter(budgetProjectDetail -> budgetProjectDetail.getDetailId().equals(entry.getKey())).collect(Collectors.toList());
            BudgetProjectFlowDetails.forEach(BudgetProjectFlowDetailEntity -> {
                // 复制对象
                ThsBusinessBudgetProjectFlowDetailsEntity budgetProjectFlowDetailsNew = new ThsBusinessBudgetProjectFlowDetailsEntity();
                BeanUtil.copyProperties(BudgetProjectFlowDetailEntity, budgetProjectFlowDetailsNew);
                budgetProjectFlowDetailsNew.setDetailId(entry.getValue());
                budgetProjectFlowDetailsNew.setBudgetProjectId(newProjectId);
                sqlList.add(generateInsertStatement("ths_business_budget_project_flow_details", budgetProjectFlowDetailsNew));
            });
        }

        //  设置 ths_business_budget_project_flow_details_user
        for (Map.Entry<String, String> entry : mapString.entrySet()) {
            List<ThsBusinessBudgetProjectFlowDetailsUserEntity> budgetProjectFlowDetailsUsers = budgetProjectFlowDetailsUserList.stream().filter(budgetProjectFlowDetailsUser -> budgetProjectFlowDetailsUser.getDetailId().equals(entry.getKey())).collect(Collectors.toList());
            budgetProjectFlowDetailsUsers.forEach(budgetProjectFlowDetailsUserEntity -> {
                // 复制对象
                ThsBusinessBudgetProjectFlowDetailsUserEntity BudgetProjectFlowDetailsUserNew = new ThsBusinessBudgetProjectFlowDetailsUserEntity();
                BeanUtil.copyProperties(budgetProjectFlowDetailsUserEntity, BudgetProjectFlowDetailsUserNew);
                BudgetProjectFlowDetailsUserNew.setId(UUID.randomUUID().toString());
                BudgetProjectFlowDetailsUserNew.setDetailId(entry.getValue());
                BudgetProjectFlowDetailsUserNew.setBudgetProjectId(newProjectId);
                BudgetProjectFlowDetailsUserNew.setFinishState(0);
                sqlList.add(generateInsertStatement("ths_business_budget_project_flow_details_user", BudgetProjectFlowDetailsUserNew));
            });
        }

        // 设置   budget_project_share_user
    }

    /**
     * @return void
     * @Description 添加流程表根据流程ID
     * <AUTHOR>
     * @Date 2023/10/12 8:59
     * @Param [sourceId, newProjectId, sqlList]
     **/
    @Override
    public void addFlowTableBySysFlowId(String sysFlowId, String newProjectId, ArrayList<String> sqlList, Map<String, String> mapString) {
        List<Map<String, Object>> realList = thsSysFlowDetailsService.getListByFlowId(sysFlowId);
        try {
            for (Map.Entry<String, String> entry : mapString.entrySet()) {
                realList.forEach(map -> {
                    ThsSysFlowDetailsEntity sysFlowDetailsEntity = JsonUtil.getJsonToBean(map, ThsSysFlowDetailsEntity.class);
                    // 复制对象
                    ThsBusinessBudgetProjectFlowDetailsEntity budgetProjectFlowDetailsNew = new ThsBusinessBudgetProjectFlowDetailsEntity();
                    BeanUtil.copyProperties(sysFlowDetailsEntity, budgetProjectFlowDetailsNew);
                    budgetProjectFlowDetailsNew.setDetailId(entry.getValue());
                    budgetProjectFlowDetailsNew.setBudgetProjectId(newProjectId);
                    sqlList.add(generateInsertStatement("ths_business_budget_project_flow_details", budgetProjectFlowDetailsNew));
                });
            }
        } catch (Exception e) {
            throw new RuntimeException("获取审批流程失败");
        }

    }

    /**
     * @param newProjectId
     * @param sqlList
     * @param mapString
     * @description: 其他费复制
     * @param: @param sourceId
     * @return: void
     * @author: wxy
     * @date: 2024/8/28 8:41
     */
    @Override
    public void addProjectOtherFeeTable(String sourceId, String newProjectId, ArrayList<String> sqlList, Map<String, String> mapString) {

        List<ThsBusinessBudgetProjectOtherFeeEntity> budgetProjectOtherFeeEntityList = thsBusinessBudgetProjectOtherFeeService.getListByBudgetProjectId(sourceId);
        if (ObjectUtil.isNotEmpty(budgetProjectOtherFeeEntityList)) {
            for (Map.Entry<String, String> entry : mapString.entrySet()) {
                List<ThsBusinessBudgetProjectOtherFeeEntity> budgetProjectOtherFees = budgetProjectOtherFeeEntityList.stream()
                        .filter(budgetProjectOtherFee -> budgetProjectOtherFee.getDetailId().equals(entry.getKey())).collect(Collectors.toList());
                budgetProjectOtherFees.forEach(budgetProjectOtherFee -> {
                    // 复制对象
                    ThsBusinessBudgetProjectOtherFeeEntity businessBudgetProjectOtherFeeNew = new ThsBusinessBudgetProjectOtherFeeEntity();
                    BeanUtil.copyProperties(budgetProjectOtherFee, businessBudgetProjectOtherFeeNew);
                    businessBudgetProjectOtherFeeNew.setId(UUID.randomUUID().toString());
                    businessBudgetProjectOtherFeeNew.setDetailId(entry.getValue());
                    businessBudgetProjectOtherFeeNew.setBudgetProjectId(newProjectId);
                    businessBudgetProjectOtherFeeNew.setSendQuantity(null);
                    businessBudgetProjectOtherFeeNew.setSendUnitPrice(null);
                    businessBudgetProjectOtherFeeNew.setSendTotalPrice(null);
                    businessBudgetProjectOtherFeeNew.setQuantity(null);
                    businessBudgetProjectOtherFeeNew.setUnitPrice(null);
                    businessBudgetProjectOtherFeeNew.setTotalPrice(null);
                    businessBudgetProjectOtherFeeNew.setCreateTime(DateUtil.date());
                    sqlList.add(generateInsertStatement("ths_business_budget_project_other_fee", businessBudgetProjectOtherFeeNew));
                });
            }
        }
    }

    /**
     * 拷贝文件，另存为模板时，不需要系统流程ID
     *
     * @param sourceId
     * @param operType
     * @return
     * <AUTHOR>
     * @Date 2023/10/13 19:20
     */
    @Override
    public Map<String, Object> copyBudgetProjects(String sourceId, Integer operType, Integer copyType) {
        return this.copyBudgetProjects(sourceId, operType, null, copyType);
    }


    /**
     * @param operType
     * @param sysFlowId
     * @param copyType  复制类型 ，0：复制全部数据，包括重出版本的 1：复制模板只复制最新版本
     * @description:
     * @param: @param sourceId
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @author: wxy
     * @date: 2024/3/23 16:16
     */
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> copyBudgetProjects(String sourceId, Integer operType, String sysFlowId, Integer copyType) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        UserInfo userInfo = userProvider.get();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 设置文件上传路径，暂时定义的常量
            String path = environment.getProperty("config.file-storage.local-plus[0].base-path");
            if (StringUtil.isEmpty(path)) {
                throw new BadRequestAlertException("未设置文件保存路径", "File save path not set", "File save path not set");
            }
            //  新的项目id
            String newProjectId = UUID.randomUUID().toString();

            // 返回的sql语句
            ArrayList<String> sqlList = new ArrayList<>();
            List<ThsBusinessBudgetProjectDetailEntity> budgetProjectDetailsList = null;
            if (copyType.equals(0)) {
                budgetProjectDetailsList = thsBusinessBudgetProjectDetailService.getListByBudgetProjectId(sourceId);

            } else {
                budgetProjectDetailsList = thsBusinessBudgetProjectDetailService.getCurrentVersionFlagListByBudgetProjectId(sourceId);
            }
            // 重构id
            Map<String, String> mapString = new HashMap<>();

            budgetProjectDetailsList.forEach(budgetProjectDetails -> {
                mapString.put(budgetProjectDetails.getId(), UUID.randomUUID().toString());
            });

            // 拷贝文件，保存到服务器，生成一个新的文件路径
            budgetProjectDetailsList.forEach(budgetProjectDetails -> {
                ThsBusinessBudgetProjectDetailEntity budgetProjectDetailsNew = new ThsBusinessBudgetProjectDetailEntity();
                BeanUtil.copyProperties(budgetProjectDetails, budgetProjectDetailsNew);

                // 复制编制文件
                if (budgetProjectDetailsNew.getItemType() == 3 && ObjectUtil.isNotEmpty(budgetProjectDetailsNew.getFileSavePath())) {
                    String newPath;
                    try {
                        newPath = copyFileByPath(budgetProjectDetailsNew, path);
                    } catch (Exception e) {
                        log.error("复制编制文件出现异常:{}", e.getMessage());
                        resultMap.put("message", "复制编制文件失败" + (e.getMessage().contains("File not exist") ? "，文件不存在！" : ""));
                        throw new RuntimeException(e);
                    }
                    budgetProjectDetailsNew.setFileName(FileNameUtil.getName(newPath));
                    budgetProjectDetailsNew.setOriginalFileName(FileNameUtil.getName(newPath));
                    budgetProjectDetailsNew.setFileSavePath(newPath);
                    budgetProjectDetailsNew.setFileVersion(1);
                    budgetProjectDetailsNew.setThsfileId(null);
                    budgetProjectDetailsNew.setChecksum(null);
                }
                // 复制审核文件
                if (budgetProjectDetailsNew.getItemType() == 3 && ObjectUtil.isNotEmpty(budgetProjectDetailsNew.getAuditFileSavePath())) {
                    String newPath;
                    try {
                        newPath = copyFileBySourceFile(budgetProjectDetailsNew.getAuditFileSavePath(), path);
                    } catch (Exception e) {
                        log.error("复制审核文件出现异常:{}", e.getMessage());
                        resultMap.put("message", "复制审核文件失败" + (e.getMessage().contains("File not exist") ? "，文件不存在！" : ""));
                        throw new RuntimeException(e);
                    }
                    budgetProjectDetailsNew.setAuditFileSavePath(newPath);
                    budgetProjectDetailsNew.setAuditFileVersion(1);
                    budgetProjectDetailsNew.setAuditFileId(UUID.randomUUID().toString());
                }

                // 复制关系文件
                if (budgetProjectDetailsNew.getItemType() == 3 && ObjectUtil.isNotEmpty(budgetProjectDetailsNew.getRelationalFileSavePath())) {
                    String newPath;
                    try {
                        newPath = copyFileBySourceFile(budgetProjectDetailsNew.getRelationalFileSavePath(), path);
                    } catch (Exception e) {
                        log.error("复制关系文件出现异常:{}", e.getMessage());
                        resultMap.put("message", "复制关系文件失败" + (e.getMessage().contains("File not exist") ? "，文件不存在！" : ""));
                        throw new RuntimeException(e);
                    }
                    budgetProjectDetailsNew.setRelationalFileSavePath(newPath);
                    budgetProjectDetailsNew.setRelationalFileVersion(1);

                    budgetProjectDetailsNew.setRelationalFileId(UUID.randomUUID().toString());
                }

                // 复制原始送审文件
                if (budgetProjectDetailsNew.getItemType() == 3 && ObjectUtil.isNotEmpty(budgetProjectDetailsNew.getOriginalSendFileSavePath())) {
                    String newPath;
                    try {
                        newPath = copyFileBySourceFile(budgetProjectDetailsNew.getOriginalSendFileSavePath(), path);
                    } catch (Exception e) {
                        log.error("复制原始送审文件出现异常:{}", e.getMessage());
                        resultMap.put("message", "复制原始送审文件失败" + (e.getMessage().contains("File not exist") ? "，文件不存在！" : ""));
                        throw new RuntimeException(e);
                    }
                    budgetProjectDetailsNew.setOriginalSendFileSavePath(newPath);
                    budgetProjectDetailsNew.setOriginalSendFileVersion(1);
                    budgetProjectDetailsNew.setOriginalSendFileId(UUID.randomUUID().toString());
                }

                //  复制报表
                List<ThsBusinessBudgetProjectDetailExcelReportEntity> projectDetailExcelReportList = thsBusinessBudgetProjectDetailExcelReportService.findListByDetailIds(Collections.singletonList(budgetProjectDetails.getId()));
                //  遍历原始报表
                projectDetailExcelReportList.forEach(projectDetailExcelReport -> {
                    ThsBusinessBudgetProjectDetailExcelReportEntity projectDetailExcelReportNew = new ThsBusinessBudgetProjectDetailExcelReportEntity();
                    BeanUtil.copyProperties(projectDetailExcelReport, projectDetailExcelReportNew);

                    ThsBusinessBudgetProjectDetailExcelReportForm excelReportForm = new ThsBusinessBudgetProjectDetailExcelReportForm();
                    excelReportForm.setDetailId(mapString.get(budgetProjectDetails.getId()));
                    excelReportForm.setDataReportId(projectDetailExcelReport.getId());

                    //  复制当前文件同时复制页面页脚
                    String excelFileId;
                    try {
                        excelFileId = thsBusinessBudgetProjectDetailExcelReportService.copyReportFormTemplateFile(excelReportForm);
                    } catch (Exception e) {
                        log.error("复制报表文件出现异常:{}", e.getMessage());
                        resultMap.put("message", "复制报表文件失败" + (e.getMessage().contains("File not exist") ? "，文件不存在！" : ""));
                        throw new RuntimeException(e);
                    }
                    //  重新复制
                    projectDetailExcelReportNew.setExcelFileId(excelFileId);
                    projectDetailExcelReportNew.setIsArchived(0);
                    projectDetailExcelReportNew.setCreatorId(userInfo.getUserId());
                    projectDetailExcelReportNew.setCreateTime(jnpf.util.DateUtil.getNowDate());
                    projectDetailExcelReportNew.setSequence(thsBusinessBudgetProjectDetailExcelReportService.getMaxSequence() + 1);
                    projectDetailExcelReportNew.setDetailId(mapString.get(budgetProjectDetails.getId()));
                    projectDetailExcelReportNew.setId(RandomUtil.uuId());
                    thsBusinessBudgetProjectDetailExcelReportService.save(projectDetailExcelReportNew);
                });

                budgetProjectDetailsNew.setBudgetProjectId(newProjectId);
                budgetProjectDetailsNew.setId(mapString.get(budgetProjectDetails.getId()));
                budgetProjectDetailsNew.setPid(mapString.get(budgetProjectDetails.getPid()));
                budgetProjectDetailsNew.setCreateTime(DateUtil.date());
                budgetProjectDetailsNew.setCreatorId(userInfo.getUserId());
                budgetProjectDetailsNew.setLastModifyTime(null);
                budgetProjectDetailsNew.setStatus(0);
                budgetProjectDetailsNew.setCurFlowDetailId(null);

                // 全部复制
                if (copyType.equals(0)) {
                    budgetProjectDetailsNew.setChangedOriginalId(mapString.get(budgetProjectDetails.getChangedOriginalId()));
                    budgetProjectDetailsNew.setChangedFromId(mapString.get(budgetProjectDetails.getChangedFromId()));
                } else {
                    budgetProjectDetailsNew.setCurrentVersionFlag(true);
                    budgetProjectDetailsNew.setChangedVer(0);
                    budgetProjectDetailsNew.setChangedOriginalId(null);
                    budgetProjectDetailsNew.setChangedFromId(null);
                    budgetProjectDetailsNew.setChangedUid(null);
                    budgetProjectDetailsNew.setChangedFlag(null);
                    budgetProjectDetailsNew.setChangedTime(null);
                }
                // 添加到sqlList中
                sqlList.add(generateInsertStatement("ths_business_budget_project_detail", budgetProjectDetailsNew));
            });

            // 流程一套
            if (operType == 0) {
                if (ObjectUtil.isEmpty(sysFlowId)) {
                    addFlowTable(sourceId, newProjectId, sqlList, mapString); // 复制文件中的流程和编制审核人等
                } else {
                    addFlowTableBySysFlowId(sysFlowId, newProjectId, sqlList, mapString);
                }
            }

            // 工程其他费 单项工程和建设项目工程其他费
            this.addProjectOtherFeeTable(sourceId, newProjectId, sqlList, mapString);

            resultMap.put("success", true);
            resultMap.put("newId", newProjectId);
            resultMap.put("sql", sqlList);

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("success", false);
        }
        // 计算方法执行时间
        long duration = System.currentTimeMillis() - startTime;
        log.error("方法执行时间：" + duration + " 毫秒");
        return resultMap;
    }

    /**
     * 复制文件功能
     *
     * @param source_id
     * @param oper_type
     * @return BaseResult
     * @throws URISyntaxException
     */
    @Transactional
    @Override
    public ActionResult copyBudgetProjectsFile(String source_id, String source_pid, Integer oper_type, String consult_project_id, String file_name) {
        Map<String, Object> params = new HashMap<>();
        params.put("source_id", source_id);
        params.put("oper_type", oper_type);
        UserInfo userInfo = userProvider.get();
        // 复制文件copyType=0 复制项目全部复制

        ThsBusinessBudgetProjectVersionEntity oldBudgetProject = this.getInfo(source_id);

        ThsBusinessConsultProjectEntity targetBusinessConsultProject = thsBusinessConsultProjectService.getInfo(consult_project_id);

        if (ObjectUtil.isEmpty(targetBusinessConsultProject)) {
            return ActionResult.fail("选择的咨询项目不存在");
        }
        if (ObjectUtil.isEmpty(targetBusinessConsultProject)) {
            return ActionResult.fail("源协同记录不存在");
        }

        if (ObjectUtil.isEmpty(targetBusinessConsultProject.getEditorialTypeId())) {
            return ActionResult.fail("所选咨询项目未设置编审类型");
        }

        if (ObjectUtil.isNotEmpty(oldBudgetProject.getEditorialTypeId()) && !oldBudgetProject.getEditorialTypeId().equals(targetBusinessConsultProject.getEditorialTypeId())) {
            return ActionResult.fail("编审类型不一致不能复制");
        }


        Map<String, Object> map = this.copyBudgetProjects(source_id, oper_type, 0);
        List<String> allSqlList = new ArrayList<>();
        if ((Boolean) map.get("success")) {
            String new_id = String.valueOf(map.get("newId"));
            List<String> sqlList = (List<String>) map.get("sql");
            // 主表数据复制的sql
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String nowTime = formatter.format(new Date());
            String userID = userInfo.getUserId();


            // 拼接保存对象
            int maxSequence = this.getMaxSequence();
            Integer ver = 0;
            if (this.GetMaxVerByConsultId(consult_project_id) != null) {
                ver = this.GetMaxVerByConsultId(consult_project_id);
            }
            ThsBusinessBudgetProjectVersionEntity budgetProject = new ThsBusinessBudgetProjectVersionEntity();
            budgetProject.setId(new_id);
            budgetProject.setPid(oldBudgetProject.getPid());
            budgetProject.setDeptId(oldBudgetProject.getDeptId());
            budgetProject.setConsultProjectId(consult_project_id);
            budgetProject.setSysFlowId(oldBudgetProject.getSysFlowId());
            budgetProject.setUseTemplateId(oldBudgetProject.getUseTemplateId());
            budgetProject.setName(file_name);
            budgetProject.setCreatorId(userID);
            budgetProject.setCreateTime(DateUtil.date());
            budgetProject.setObligUserId(userID);
            budgetProject.setTemplateCase(ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT);
            budgetProject.setVer(ver + 1);
            budgetProject.setStatus(0);
            budgetProject.setSequence(maxSequence);
            budgetProject.setIsFinalize(false);
            budgetProject.setSVerId(oldBudgetProject.getSVerId());
            budgetProject.setAreaId(oldBudgetProject.getAreaId());
            budgetProject.setTemplateId(oldBudgetProject.getTemplateId());
            budgetProject.setCategoryDictId(oldBudgetProject.getCategoryDictId());
            budgetProject.setEditorialTypeId(targetBusinessConsultProject.getEditorialTypeId());
            // 调用方法 生成保存sql
            String sql = generateInsertStatement("ths_business_budget_project_version", budgetProject);
            allSqlList.add(sql);

            allSqlList.addAll(sqlList);

            // 复制所有数据的sql
            try {
                if (!executeSqlUtils.executeSql(allSqlList)) {
                    return ActionResult.fail("Error"); // Error
                } else {
                    return ActionResult.success("复制成功");
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                return ActionResult.fail(ex.getMessage()); // ex.getMessage()
            }
        } else {

            return ActionResult.fail(String.valueOf(map.get("message")));
        }
    }


    /**
     * 另存为模板功能
     *
     * @param source_id
     * @param oper_type
     * @return BaseResult
     * @throws URISyntaxException
     */
    @Transactional
    @Override
    public ActionResult saveBudgetProjectsAsTemplate(String source_id, Integer oper_type) {
        Map<String, Object> params = new HashMap<>();
        params.put("source_id", source_id);
        params.put("oper_type", oper_type);
        UserInfo userInfo = userProvider.get();
        // 如果是模板只存最新版就好了
        Map<String, Object> map = this.copyBudgetProjects(source_id, oper_type, 0);
        List<String> allSqlList = new ArrayList<>();
        if ((Boolean) map.get("success")) {
            String new_id = String.valueOf(map.get("newId"));
            List<String> sqlList = (List<String>) map.get("sql");
            // 主表数据复制的sql
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String nowTime = formatter.format(new Date());
            String userID = userInfo.getUserId();

            // 拼接保存对象
            int maxSequence = this.getMaxSequence();
            ThsBusinessBudgetProjectVersionEntity oldBudgetProject = this.getInfo(source_id);
            ThsBusinessBudgetProjectVersionEntity budgetProject = new ThsBusinessBudgetProjectVersionEntity();
            budgetProject.setId(new_id);
            budgetProject.setName(oldBudgetProject.getName());
            budgetProject.setCreatorId(userID);
            budgetProject.setCreateTime(DateUtil.date());
            budgetProject.setObligUserId(userID);
            budgetProject.setTemplateCase(ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_TEMPLATE);
            budgetProject.setStatus(0);
            budgetProject.setSequence(maxSequence);
            budgetProject.setLastModifyTime(null);
            budgetProject.setIssuerId(null);
            budgetProject.setIssueTime(null);

            // 项目分类 从当前版本的咨询项目类型获取
            ThsBusinessConsultProjectEntity businessConsultProjectEntity = thsBusinessConsultProjectService.getInfo(oldBudgetProject.getConsultProjectId());
            budgetProject.setCategoryDictId(businessConsultProjectEntity.getProjectTypeId());

            // 调用方法 生成保存sql
            String sql = generateInsertStatement("ths_business_budget_project_version", budgetProject);
            allSqlList.add(sql);

            allSqlList.addAll(sqlList);
            // 复制所有数据的sql
            try {
                if (!executeSqlUtils.executeSql(allSqlList)) {
                    return ActionResult.fail("Error"); // Error
                } else {
                    return ActionResult.success("复制成功");
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                return ActionResult.fail(ex.getMessage()); // ex.getMessage()
            }
        } else {
            return ActionResult.fail(String.valueOf(map.get("message")));
        }
    }

    /**
     * 根据合同编号查询终版的协同版本IDs
     *
     * @param contractCode
     * @return
     */
    public List<String> getIdsByContractCode(String contractCode) {
        return this.baseMapper.selectIdsByContractCode(contractCode);
    }

    /**
     * 根据咨询项目编码查询的协同版本IDs
     *
     * @param consultCode
     * @return
     */
    public List<String> getIdsByConsultCode(String consultCode) {
        return this.baseMapper.selectIdsByContractCode(consultCode);
    }

    /**
     * 修改删除权限验证
     *
     * @param ids         项目ID
     * @param operateType 操作类型
     * @return {@link ActionResult}
     * <AUTHOR>
     * @date 2023/9/21 9:42
     */
    public ActionResult dataPermissionVerify(List<String> ids, String operateType) {
        //  标识
        AtomicBoolean flag = new AtomicBoolean(false);

        //  项目
        List<ThsBusinessBudgetProjectVersionEntity> budgetProjects = this.list();
        //  权限判断
        budgetProjects.forEach(budgetProject -> {
            if (ids.contains(budgetProject.getId())) {
                if (!budgetProject.getObligUserId().equals(userProvider.get().getUserId())) {
                    flag.set(true);
                }
            }
        });
        //  返回结果
        if (flag.get()) {
            log.info("进入 {} 项目无权限", operateType);
            return ActionResult.fail(403, "无权限");
        } else {
            return ActionResult.success();
        }
    }


    /**
     * 定义一个函数接口，用于获取对象中的金额属性值
     */
    private interface ValueGetter {
        BigDecimal getValue(ThsFileQdgStructureVo obj);
    }

    /**
     * 根据节点的ID获取子节点列表
     *
     * @param node  当前
     * @param nodes 所有
     * @return
     */
    private static List<ThsFileQdgStructureVo> getChildren(ThsFileQdgStructureVo node, List<ThsFileQdgStructureVo> nodes) {
        List<ThsFileQdgStructureVo> children = new ArrayList<>();
        for (ThsFileQdgStructureVo n : nodes) {
            if (n.getPid() != null && n.getPid().equals(node.getId())) {
                children.add(n);
            }
        }
        return children;
    }

    /**
     * 格式化SQL
     *
     * @param thsFileQdgStructureVos 数据
     * @param sb                     拼接
     */
    private static void formatSql(List<ThsFileQdgStructureVo> thsFileQdgStructureVos, StringBuilder sb) {
        thsFileQdgStructureVos.forEach(thsFileQdgStructureVo -> {
            String value = StrUtil.format("('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}'),", thsFileQdgStructureVo.getId(), thsFileQdgStructureVo.getPid(), thsFileQdgStructureVo.getIid(), thsFileQdgStructureVo.getIpid(), thsFileQdgStructureVo.getFileId(), thsFileQdgStructureVo.getCode(), thsFileQdgStructureVo.getName(), thsFileQdgStructureVo.getItemType(), thsFileQdgStructureVo.getItemUnit(), thsFileQdgStructureVo.getTotalMoney(), thsFileQdgStructureVo.getDekId(), thsFileQdgStructureVo.getDekName(), thsFileQdgStructureVo.getQdkId(), thsFileQdgStructureVo.getQdkName(), thsFileQdgStructureVo.getFeeName(), thsFileQdgStructureVo.getXxjName(), thsFileQdgStructureVo.getXxjDatetime(), thsFileQdgStructureVo.getIsAddedTax(), thsFileQdgStructureVo.getSequence(), thsFileQdgStructureVo.getBudgetMoney(), thsFileQdgStructureVo.getMeasureMoney(), thsFileQdgStructureVo.getOtherMoney(), thsFileQdgStructureVo.getLaborMoney(), thsFileQdgStructureVo.getMaterialMoney(), thsFileQdgStructureVo.getMachineMoney(), thsFileQdgStructureVo.getRegulationMoney(), thsFileQdgStructureVo.getTaxMoney(), thsFileQdgStructureVo.getOverheadMoney(), thsFileQdgStructureVo.getProfitMoney(), thsFileQdgStructureVo.getPrjOtherMoney(), thsFileQdgStructureVo.getEquipmentMoney(), thsFileQdgStructureVo.getMaterialEstimationMoney(), thsFileQdgStructureVo.getSefetyCivilizationMoney(), thsFileQdgStructureVo.getHavePrjData());
            sb.append(value.replace("'null'", "null").replace("''", "null"));
        });
    }

    /**
     * 远程调用自动创建相关数据
     *
     * @param budgetProjectVersionEntity 主数据
     * @return
     * <AUTHOR>
     * @date 2023/7/26 16:22
     */
    private void createIndicatorsRemotely(ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity) {
        try {
//            ZbcalcCaseProjectVo zbcalcCaseProjectVo = new ZbcalcCaseProjectVo();
//            zbcalcCaseProjectVo.setId(budgetProjectVersionEntity.getId());
//            zbcalcCaseProjectVo.setThsfileFilesId(budgetProjectVersionEntity.getId());
            // 根据ID获取字典对应的编号，根据分类编号获取指标库中的templateID和 CategoryId
            String categoryCode = "";
            String templateCode = "";
            String categoryId = "";
            String templateId = "";
            try {
                if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getCategoryDictId())) {
                    Map<String, Object> data = (Map<String, Object>) thsSysDictDetailsService.getInfo(budgetProjectVersionEntity.getCategoryDictId());
                    categoryCode = data.get("itemCode").toString();
                    // 模板编号
                    templateCode = data.get("itemValueTwo").toString();

                }
                // 根据模板编号和模板获取指标模板

//                ResponseEntity<Map<String, Object>> responseEntity =lyIndicatorTemplateApi.findZbcalcTemplateProjectCategoryByCategoryCodeAndTemplateCode(categoryCode, templateCode);
//
//                if (responseEntity.getStatusCode().value() == 200) {
//                    Map<String, Object> objectMap = responseEntity.getBody();
//                    if (objectMap != null) {
//                        if (objectMap.containsKey("id") && objectMap.containsKey("templateId")) {
//                            categoryId = objectMap.get("id").toString();
//                            templateId = objectMap.get("templateId").toString();
//                        } else {
//                            log.error("Missing expected keys in the response map.");
//                        }
//                    } else {
//                        log.error("Received null response body from lyIndicatorTemplateApi.");
//                    }
//                } else {
//                    log.error("Non-successful HTTP status: {}", responseEntity.getStatusCode());
//                }

                if (StringUtil.isEmpty(categoryId) || StringUtil.isEmpty(templateId)) {
                    throw new RuntimeException("未查找到指标模板");
                }

            } catch (RuntimeException e) {
                throw new RuntimeException("未查找到指标模板");
            }
//            zbcalcCaseProjectVo.setTemplateId(templateId);
//            zbcalcCaseProjectVo.setCategoryId(categoryId);
//            zbcalcCaseProjectVo.setName(budgetProjectVersionEntity.getName());
//            zbcalcCaseProjectVo.setCode(budgetProjectVersionEntity.getCode());
//            zbcalcCaseProjectVo.setCreatorId(budgetProjectVersionEntity.getCreatorId());
//            zbcalcCaseProjectVo.setModifierId(budgetProjectVersionEntity.getObligUserId());
//            zbcalcCaseProjectVo.setSourceType(1);
//
//            lyIndicatorApi.createZbcalcCaseProject(zbcalcCaseProjectVo);
        } catch (Exception e) {
            log.error("异常:", e);
            log.error("异常:{}", e.getMessage());
            //  出现异常则删除指标计算产生的数据
            deleteIndicatorsRemotely(budgetProjectVersionEntity.getId());
        }
    }

    /**
     * 远程调用删除相关数据
     *
     * @param projectId 项目ID
     * @return
     * <AUTHOR>
     * @date 2023/7/26 16:27
     */
    private void deleteIndicatorsRemotely(String projectId) {
//        ResponseEntity<BaseResult> baseResultResponseEntity = lyIndicatorApi.deleteAllByProjectId(projectId);
//        BaseResult baseResult = baseResultResponseEntity.getBody();
//        assert baseResult != null;
//        log.info("远程删除指标相关数据返回结果:{}", JSONUtil.parse(baseResult));
    }


    /**
     * 递归计算节点的总金额或预算金额
     *
     * @param node           当前
     * @param nodes          所有
     * @param totalAmountMap 累加
     * @param getter         获取对象中的金额属性值
     * @return
     */
    private static BigDecimal calculateTotalAmount(ThsFileQdgStructureVo node, List<ThsFileQdgStructureVo> nodes, Map<String, BigDecimal> totalAmountMap, ValueGetter getter) {
        BigDecimal totalAmount = Optional.ofNullable(getter.getValue(node)).orElse(BigDecimal.ZERO);
        // 获取子节点列表
        List<ThsFileQdgStructureVo> children = getChildren(node, nodes);

        // 如果子节点不为空，则递归计算子节点的总金额或预算金额并累加到当前节点的总金额或预算金额中
        if (!children.isEmpty()) {
            for (ThsFileQdgStructureVo child : children) {
                totalAmount = totalAmount.add(calculateTotalAmount(child, nodes, totalAmountMap, getter));
            }
        }
        totalAmountMap.put(node.getId(), totalAmount); // 将当前节点的总金额或预算金额存入映射
        return totalAmount;
    }


    /**
     * 异步创建指标计算相关数据
     *
     * @param id
     * @param token
     */
    @Override
    @DSTransactional
//    @Async
    public void openZbCalcProject(String id, String token) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();

        String projectId = "";
        try {
            //  清除所有缓存
            timedCache.clear();
            //  设置线程权限信息 异步操作必须将认证放入，否则就是无权限
            UserProvider.getUser().setToken(token);

            // 从指标计算主表同步数据至数据中心
//            zbcalcCaseProjectTaskApi.updateZbcalcCaseProjectInfo();
            //  1.判断当前文件是否已经生成指标计算记录
            Map<String, Object> exitMap = null;  // zbCalcCaseProjectApi.getZbcalcCaseProjectById(id);

            if (exitMap != null && exitMap.size() > 0) {
                //  如果已存在项目则前端执行跳转,否则等待加载
            } else {
                //  数据权限验证
                ActionResult dataPermissionVerify = dataPermissionVerify(Collections.singletonList(id), "计算");
                if (!dataPermissionVerify.getCode().equals(Integer.valueOf("200"))) {
//                    throw new DataPermissionException("无权限", "Invalid permission");
                }

                //  2.根据ID查询当前记录的模板ID和分类ID
                ThsBusinessBudgetProjectVersionEntity budgetProject = this.getInfo(id);

                //  3.远程调用自动创建相关数据
                createIndicatorsRemotely(budgetProject);

                assert budgetProject != null;
                projectId = budgetProject.getId();

                //  4.根据ID查询 Detail表 row_type为1:实体工程 2:单项工程 3:单位工程取 thsfile_qdg_structure 的数据,将各单位的金额进行汇总到单项,单项汇总到项目
                //  根据ID查询所有数据
                List<ThsBusinessBudgetProjectDetailEntity> budgetProjectDetails = thsBusinessBudgetProjectDetailService.getListByBudgetProjectId(id);
                List<ThsFileQdgStructureVo> thsFileQdgStructureVos = new ArrayList<>();
                //  将明细 row_type 等于1,2 的格式化成 thsfile_qdg_structure 结构,并获取 row_type 等于 3 thsfile_qdg_structure表中的数据
                budgetProjectDetails.forEach(budgetProjectDetail -> {
                    // 不是当前版本不需要参与指标计算
                    if (budgetProjectDetail.getItemType() != null && (budgetProjectDetail.getItemType() != 1) && !(budgetProjectDetail.getCurrentVersionFlag())) {
                        return;
                    }
                    //  查询 thsfile_qdg_structure 表记录中的相关数据
                    if (budgetProjectDetail.getItemType().equals(3)) {

                        //  单位层数据
                        ThsfileQdgStructureEntity thsfileQdgStructureEntity = thsfileQdgStructureService.findThsFileQdgStructureByFileId(budgetProjectDetail.getThsfileId());
                        if (ObjectUtil.isNotEmpty(thsfileQdgStructureEntity)) {
                            //  转对象
                            ThsFileQdgStructureVo thsFileQdgStructureVo = JsonUtil.getJsonToBean(thsfileQdgStructureEntity, ThsFileQdgStructureVo.class);
                            thsFileQdgStructureVo.setFileId(budgetProject.getId());
                            thsFileQdgStructureVo.setPid(budgetProjectDetail.getPid());

                            if (ObjectUtil.isNotEmpty(budgetProjectDetail.getInformationPriceDate())) {

                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                thsFileQdgStructureVo.setXxjDatetime(dateFormat.format(budgetProjectDetail.getInformationPriceDate()));
                            } else {
                                thsFileQdgStructureVo.setXxjDatetime(null);
                            }

                            thsFileQdgStructureVos.add(thsFileQdgStructureVo);
                        }
                    } else {
                        ThsFileQdgStructureVo thsFileQdgStructureVo = new ThsFileQdgStructureVo();
                        //  复制对象
                        BeanUtil.copyProperties(budgetProjectDetail, thsFileQdgStructureVo);
                        thsFileQdgStructureVo.setFileId(budgetProject.getId());
                        thsFileQdgStructureVo.setItemType(budgetProjectDetail.getItemType());
                        thsFileQdgStructureVos.add(thsFileQdgStructureVo);
                    }
                });

                ArrayList<String> sqlList = new ArrayList<>();


                //  处理金额汇总 创建一个映射，用于存储每个节点的总金额和预算金额
                Map<String, BigDecimal> totalAmountMap = new HashMap<>();
                Map<String, BigDecimal> totalBudgetMap = new HashMap<>();

                //  计算每个节点的总金额和预算金额并更新到映射中
                for (ThsFileQdgStructureVo node : thsFileQdgStructureVos) {
                    BigDecimal totalAmount = calculateTotalAmount(node, thsFileQdgStructureVos, totalAmountMap, ThsFileQdgStructureVo::getTotalMoney);
                    BigDecimal totalBudgetAmount = calculateTotalAmount(node, thsFileQdgStructureVos, totalBudgetMap, ThsFileQdgStructureVo::getBudgetMoney);
                    totalAmountMap.put(node.getId(), totalAmount);
                    totalBudgetMap.put(node.getId(), totalBudgetAmount);
                    // 更新节点的总金额
                    node.setTotalMoney(totalAmount);
                    // 更新节点的分部分项金额
                    node.setBudgetMoney(totalBudgetAmount);
                }

                //  5.将上面结果写入 zbfile_qdg_structure
                String zbfile_qdg_structure_sql = StrUtil.format("INSERT INTO zbfile_qdg_structure (id, pid, iid, ipid, file_id, code, name, item_type, item_unit, total_money, dek_id, dek_name, qdk_id, qdk_name, fee_name, xxj_name, xxj_datetime, is_added_tax, sequence, budget_money, measure_money, other_money, labor_money, material_money, machine_money, regulation_money, tax_money, overhead_money, profit_money, prj_other_money, equipment_money, material_estimation_money, safety_civilization_money, have_prj_data) VALUES");
                //  格式化SQL
                StringBuilder sb = new StringBuilder();
                formatSql(thsFileQdgStructureVos, sb);
                //  替换最后一个逗号为分号
                int lastCommaIndex = sb.lastIndexOf(",");
                sb.setCharAt(lastCommaIndex, ';');
                // 生成  Thsfile主记录
                ThsfileFilesEntity thsfileFilesEntity = new ThsfileFilesEntity();

                thsfileFilesEntity.setId(budgetProject.getId());
                thsfileFilesEntity.setItemType(0);
                thsfileFilesEntity.setName(budgetProject.getName());
                thsfileFilesEntity.setAnalysisMode(BusinessConstantValues.THS_FILE_FILES_ANALYSIS_MODE_IDLE);
                thsfileFilesEntity.setCreateTime(Date.from(Instant.now()));
                thsfileFilesEntity.setRemark("通过指标计算生成" + DateUtil.date());
                thsfileFilesEntity.setAnalysisMsg(null);
                if (ObjectUtil.isNotEmpty(thsfileFilesEntity.getAnalysisMode()) && thsfileFilesEntity.getAnalysisMode().equals(BusinessConstantValues.THS_FILE_FILES_ANALYSIS_MODE_IDLE)) {
                    thsfileFilesEntity.setAnalysisType(9);
                } else {
                    thsfileFilesEntity.setAnalysisType(2);
                }
                thsfileFilesEntity.setSplitFile(0);
                thsfileFilesEntity.setAnalysisLev(1);
                thsfileFilesEntity.setSourceId(2);
                String thsFileFiles_Sql = generateInsertStatement("thsfile_files", thsfileFilesEntity);


                //  6.查询 thsfile_qdg_structure 的记录的子表数据，替换 file id 保存到 zbfile_* 里面,生成写库的SQL语句
                copyQdgStructureChildData(budgetProject, budgetProjectDetails, sqlList);


//                //动态切换当前数据库， 后⾯代码的数据库操作将在第三⽅数据库进⾏
//                DynamicDataSourceContextHolder.push("MYSQL_QUOTE");

                // 指定数据库链接的方式切换数据库
                String username = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.username");
                String password = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.password");
                String jdbcUrl = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.url");
                String dbType = "MySQL";
                DynamicDataSourceUtil.switchToDataSource(username, password, jdbcUrl, dbType);
                log.info("指标计算切换数据库名称username: {}, jdbcUrl {} ", username, jdbcUrl);
                try {
                    // 生成解析服务主记录
                    // 查询是否是再次生成的
                    String QueryThsFileSql = "SELECT * FROM thsfile_files WHERE id = ?";
                    List<Map<String, Object>> thsfileList = executeSqlUtils.executeQuerySqlWithParameters(QueryThsFileSql, budgetProject.getId());

                    if ((thsfileList != null) && (thsfileList.size() < 1))
                        executeSqlUtils.executeUpdateSingleSql(thsFileFiles_Sql);

                    executeSqlUtils.executeUpdateSingleSql(zbfile_qdg_structure_sql + sb);
                    int i1 = 1;
                    log.info("zbfile_qdg_structure 共执行SQL: {} 条", i1);

                    //  更新主表总金额
                    String update_zbcalc_case_project_sql = StrUtil.format("UPDATE zbcalc_case_project SET total_cost = {}", thsFileQdgStructureVos.parallelStream().filter(thsFileQdgStructureVo -> ObjectUtil.isEmpty(thsFileQdgStructureVo.getPid())).findFirst().get().getTotalMoney());
                    //  执行SQL
                    int i2 = 1;
                    executeSqlUtils.executeUpdateSingleSql(update_zbcalc_case_project_sql);
                    log.info("修改主表总金额: {} 条", i2);

                    // 子表写库
                    executeSqlUtils.executeSql(sqlList);

                    // 获取当前数据源的连接， 可获取切换后的连接, 连接⼀定要关闭
                    @Cleanup Connection conn = DynamicDataSourceUtil.getCurrentConnection();
                    // 清除当前动态库， 后⾯的数据库操作将在主库进⾏
                    DynamicDataSourceContextHolder.poll();
                } catch (Exception e) {
                    //  出现异常手动事务回滚
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    log.error("异常:", e);
                    log.error("异常:{}", e.getMessage());
                    throw new RuntimeException(e);
                }

                //  7.自动根据模板创建项目概况,自动创建单项,单项概况,专业,专业概况(未实现)
                // hwIndicatorClient.remoteCallsAutomaticallyCreateRelatedData(budgetProject.getId());

                // 计算方法执行时间
                log.info("方法执行时间：{} 毫秒", System.currentTimeMillis() - startTime);
            }
            timedCache.put(id, id.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {

            //  出现异常手动事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("异常:", e);
            log.error("异常:{}", e.getMessage());

            //  出现异常则删除指标计算产生的数据
            if (ObjectUtil.isNotEmpty(projectId)) {
                deleteIndicatorsRemotely(projectId);
            }
        } finally {
            // 最终将数据源切换到租户库主库
            // 获取当前数据源的连接， 可获取切换后的连接, 连接⼀定要关闭
            try {
                @Cleanup Connection conn = DynamicDataSourceUtil.getCurrentConnection();
                // 清除当前动态库， 后⾯的数据库操作将在主库进⾏
                DynamicDataSourceContextHolder.poll();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

        }
    }

    /**
     * thsfile_qdg_structure子表的数据复制到指标一套表中
     *
     * @param budgetProject
     * @param budgetProjectDetails
     * @return
     */
    @Override
    public Map<String, Object> copyQdgStructureChildData(ThsBusinessBudgetProjectVersionEntity budgetProject, List<ThsBusinessBudgetProjectDetailEntity> budgetProjectDetails, ArrayList<String> sqlList) throws Exception {
        try {
            budgetProjectDetails.forEach(budgetProjectDetail -> {

                if (budgetProjectDetail.getItemType().equals(3) && ObjectUtil.isNotEmpty(budgetProjectDetail.getThsfileId())) {

                    // 复制thsfile_qdy_information表的数据
                    int i3 = 0;
                    List<ThsfileQdyInformationEntity> thsfileQdyInformationEntityList = thsfileQdyInformationService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyInformationEntity thsfileQdyInformation : thsfileQdyInformationEntityList) {
                        thsfileQdyInformation.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_information", thsfileQdyInformation));
                        i3++;
                    }

                    // 复制工料机汇总 thsfile_qdy_resource_summary
                    int i4 = 0;
                    List<ThsfileQdyResourceSummaryEntity> thsfileQdyResourceSummaryEntityList = thsfileQdyResourceSummaryService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyResourceSummaryEntity thsfileQdyResourceSummary : thsfileQdyResourceSummaryEntityList) {
                        thsfileQdyResourceSummary.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_resource_summary", thsfileQdyResourceSummary));
                        i4++;
                    }

                    // 复制分部分项清单thsfile_qdy_budget
                    int i5 = 0;
                    List<ThsfileQdyBudgetEntity> thsfileQdyBudgetEntityList = thsfileQdyBudgetService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyBudgetEntity thsfileQdyBudget : thsfileQdyBudgetEntityList) {
                        thsfileQdyBudget.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_budget", thsfileQdyBudget));
                        i5++;
                    }

                    // 复制分部分项工料机汇总thsfile_qdy_budget_resource_component
                    int i6 = 0;
                    List<ThsfileQdyBudgetResourceComponentEntity> thsfileQdyBudgetResourceComponentEntityList = thsfileQdyBudgetResourceComponentService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyBudgetResourceComponentEntity thsfileQdyBudgetResourceComponent : thsfileQdyBudgetResourceComponentEntityList) {
                        thsfileQdyBudgetResourceComponent.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_budget_resource_component", thsfileQdyBudgetResourceComponent));
                        i6++;
                    }

                    // 复制分部分项项目特征thsfile_qdy_budget_feature
                    int i7 = 0;
                    List<ThsfileQdyBudgetFeatureEntity> thsfileQdyBudgetFeatureEntityList = thsfileQdyBudgetFeatureService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyBudgetFeatureEntity thsfileQdyBudgetFeature : thsfileQdyBudgetFeatureEntityList) {
                        thsfileQdyBudgetFeature.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_budget_feature", thsfileQdyBudgetFeature));
                        i7++;
                    }

                    // 复制分部分项项目特征thsfile_qdy_budget_contont

                    int i8 = 0;
                    List<ThsfileQdyBudgetContontEntity> thsfileQdyBudgetContontEntityList = thsfileQdyBudgetContontService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyBudgetContontEntity thsfileQdyBudgetContont : thsfileQdyBudgetContontEntityList) {
                        thsfileQdyBudgetContont.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_budget_contont", thsfileQdyBudgetContont));
                        i8++;
                    }


                    // 复制措施项目清单thsfile_qdy_measure
                    int i9 = 0;
                    List<ThsfileQdyMeasureEntity> thsfileQdyMeasureEntityList = thsfileQdyMeasureService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyMeasureEntity thsfileQdyMeasure : thsfileQdyMeasureEntityList) {
                        thsfileQdyMeasure.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_measure", thsfileQdyMeasure));
                        i9++;
                    }

                    // 复制措施项目工料机汇总thsfile_qdy_measure_resource_component

                    int i10 = 0;
                    List<ThsfileQdyMeasureResourceComponentEntity> thsfileQdyMeasureResourceComponentEntityList = thsfileQdyMeasureResourceComponentService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyMeasureResourceComponentEntity thsfileQdyMeasureResourceComponent : thsfileQdyMeasureResourceComponentEntityList) {
                        thsfileQdyMeasureResourceComponent.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_measure_resource_component", thsfileQdyMeasureResourceComponent));
                        i10++;
                    }


                    // 复制措施项目项目特征thsfile_qdy_measure_feature
                    int i11 = 0;
                    List<ThsfileQdyMeasureFeatureEntity> thsfileQdyMeasureFeatureEntityList = thsfileQdyMeasureFeatureService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyMeasureFeatureEntity thsfileQdyMeasureFeature : thsfileQdyMeasureFeatureEntityList) {
                        thsfileQdyMeasureFeature.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_measure_feature", thsfileQdyMeasureFeature));
                        i11++;
                    }

                    // 复制措施项目项目特征thsfile_qdy_measure_contont
                    int i12 = 0;
                    List<ThsfileQdyMeasureContontEntity> thsfileQdyMeasureContontEntityList = thsfileQdyMeasureContontService.getListByFileId(budgetProjectDetail.getThsfileId());
                    for (ThsfileQdyMeasureContontEntity thsfileQdyMeasureContont : thsfileQdyMeasureContontEntityList) {
                        thsfileQdyMeasureContont.setFileId(budgetProject.getId());
                        sqlList.add(generateInsertStatement("zbfile_qdy_measure_contont", thsfileQdyMeasureContont));
                        i12++;
                    }

                    log.info("文件: {}, zbfile_qdy_information 共执行 {} 条", budgetProjectDetail.getThsfileId(), i3);
                    log.info("文件: {}, zbfile_qdy_resource_summary 共执行 {} 条", budgetProjectDetail.getThsfileId(), i4);
                    log.info("文件: {}, zbfile_qdy_budget 共执行 {} 条", budgetProjectDetail.getThsfileId(), i5);
                    log.info("文件: {}, zbfile_qdy_budget_resource_component 共执行 {} 条", budgetProjectDetail.getThsfileId(), i6);
                    log.info("文件: {}, zbfile_qdy_budget_feature 共执行 {} 条", budgetProjectDetail.getThsfileId(), i7);
                    log.info("文件: {}, zbfile_qdy_budget_contont 共执行 {} 条", budgetProjectDetail.getThsfileId(), i8);
                    log.info("文件: {}, zbfile_qdy_measure 共执行 {} 条", budgetProjectDetail.getThsfileId(), i9);
                    log.info("文件: {}, zbfile_qdy_measure_resource_component 共执行 {} 条", budgetProjectDetail.getThsfileId(), i10);
                    log.info("文件: {}, zbfile_qdy_measure_feature 共执行 {} 条", budgetProjectDetail.getThsfileId(), i11);
                    log.info("文件: {}, zbfile_qdy_measure_contont 共执行 {} 条", budgetProjectDetail.getThsfileId(), i12);
                }
            });
        } catch (Exception e) {
            // 处理其他异常
            log.error("Exception:", e);
            log.error("Exception: {}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> testDS() throws SQLException {

        // 动态切换当前数据库， 后⾯代码的数据库操作将在第三⽅数据库进⾏
//        DynamicDataSourceContextHolder.push("MYSQL_QUOTE");
        String username = "root";
        String password = "123456";
        String jdbcUrl = "******************************************************************************************************************************************";
        String dbType = "MySQL";

        DynamicDataSourceUtil.switchToDataSource(username, password, jdbcUrl, dbType);

        List<Map<String, Object>> resultList2 = executeSqlUtils.executeQuerySql("SELECT * FROM `zbcalc_case_project`");
        // 遍历结果集
        for (Map<String, Object> row : resultList2) {
            // 遍历每一行的数据
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String columnName = entry.getKey();
                Object columnValue = entry.getValue();

                // 处理数据，例如输出到日志
                System.out.println("Column: " + columnName + ", Value: " + columnValue);
            }
            row.put("Datasource", "指标库");
            System.out.println("Row processed.");
        }

        // 获取当前数据源的连接， 可获取切换后的连接, 连接⼀定要关闭
        @Cleanup Connection conn = DynamicDataSourceUtil.getCurrentConnection();
        // 清除当前动态库， 后⾯的数据库操作将在主库进⾏
        DynamicDataSourceContextHolder.poll();

        System.out.println("########################################################################");

        List<Map<String, Object>> resultList = executeSqlUtils.executeQuerySql("SELECT * FROM `zbcalc_case_project`");

        // 遍历结果集
        for (Map<String, Object> row : resultList) {
            // 遍历每一行的数据
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String columnName = entry.getKey();
                Object columnValue = entry.getValue();
                // 处理数据，例如输出到日志
                System.out.println("Column: " + columnName + ", Value: " + columnValue);
            }
            row.put("Datasource", "主库");
            System.out.println("Row processed.");
        }
        // 合并两个结果集
        List<Map<String, Object>> combinedResult = new ArrayList<>();
        combinedResult.addAll(resultList);
        Map<String, Object> separatorMap = new HashMap<>();
        separatorMap.put("separator", "*************************");
        combinedResult.addAll(resultList2);
        return combinedResult;
    }

    /**
     * 切换数据源查询测试接口
     *
     * @return
     * @throws SQLException
     */
    @Override
    @Async
    public CompletableFuture<List<Map<String, Object>>> asyncTestDS() throws SQLException {
        CompletableFuture<List<Map<String, Object>>> future = new CompletableFuture<>();
        try {
            // 动态切换当前数据库， 后⾯代码的数据库操作将在第三⽅数据库进⾏
//        DynamicDataSourceContextHolder.push("MYSQL_QUOTE");
            String username = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.username");
            String password = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.password");
            String jdbcUrl = environment.getProperty("spring.datasource.dynamic.datasource.MYSQL_QUOTE.url");
            String dbType = "MySQL";
            DynamicDataSourceUtil.switchToDataSource(username, password, jdbcUrl, dbType);

            List<Map<String, Object>> resultList2 = executeSqlUtils.executeQuerySql("SELECT * FROM `zbcalc_case_project`");

            DynamicDataSourceUtil.switchToDataSource(username, password, jdbcUrl, dbType);
//        String insertSQL="INSERT INTO zbfile_qdg_structure (id, pid, iid, ipid, file_id, code, name, item_type, item_unit, total_money, dek_id, dek_name, qdk_id, qdk_name, fee_name, xxj_name, xxj_datetime, is_added_tax, sequence, budget_money, measure_money, other_money, labor_money, material_money, machine_money, regulation_money, tax_money, overhead_money, profit_money, prj_other_money, equipment_money, material_estimation_money, safety_civilization_money, have_prj_data) VALUES('481386033536140741', null, null, null, '481385995833542085', null, 'XXXX-邹波-测试-咨询项目', '1', null, '13765104.6200', null, null, null, null, null, null, null, null, null, '0.0000', '0.0000', '0.0000', '0.000000', '0.000000', '0.000000', '0.000000', '0.0000', '0.000000', '0.000000', '350.000000', '0.000000', '0.000000', null, null),('481389589861336517', '481386033536140741', null, null, '481385995833542085', null, '测试20231010', '2', null, '13765104.6200', null, null, null, null, null, null, null, null, '14', '0.0000', '0.0000', '0.0000', '0.000000', '0.000000', '0.000000', '0.000000', '0.0000', '0.000000', '0.000000', null, '0.000000', '0.000000', null, null),('762c7975048842c7842548725e61fed9', '481389589861336517', '0', '0', '481385995833542085', null, '大康河碧道工程-给排水工程', '3', 'm2', '2582442.1100', '1020403', '深圳市市政工程消耗量定额(2017)', '1020013', '国标清单(2013)', '市政工程(深建价[2018]25号、建办标函[2019]193号)(安全文明施工费按各专业取费)', '建安材料价格(2020年第 1 月);人工工日价格(2020年第 1 月);园林苗木价格(2020年第一季度)', 'Wed Jan 01 00:00:00 CST 2020', '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('eb40d46cb49f48899f7657dc416e6250', '481389589861336517', '0', '0', '481385995833542085', null, '测试20231024创建单位工程', '3', 'm2', '0.0000', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('df236039fc8246aeb811e107c371371e', '481389589861336517', '0', '0', '481385995833542085', null, '测试单位工程新建20231025', '3', 'm2', '11182630.1700', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('768f00e9993a47688473974329ffd094', '481389589861336517', '0', '0', '481385995833542085', null, '测试新建单位工程1025001', '3', 'm2', '32.3400', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1');\n";

            String insertSQL = "INSERT INTO zbfile_qdg_structure (id, pid, iid, ipid, file_id, code, name, item_type, item_unit, total_money, dek_id, dek_name, qdk_id, qdk_name, fee_name, xxj_name, xxj_datetime, is_added_tax, sequence, budget_money, measure_money, other_money, labor_money, material_money, machine_money, regulation_money, tax_money, overhead_money, profit_money, prj_other_money, equipment_money, material_estimation_money, safety_civilization_money, have_prj_data) VALUES('481386033536140741', null, null, null, '481385995833542085', null, 'XXXX-邹波-测试-咨询项目', '1', null, '13765104.6200', null, null, null, null, null, null, null, null, null, '0.0000', '0.0000', '0.0000', '0.000000', '0.000000', '0.000000', '0.000000', '0.0000', '0.000000', '0.000000', '350.000000', '0.000000', '0.000000', null, null),('481389589861336517', '481386033536140741', null, null, '481385995833542085', null, '测试20231010', '2', null, '13765104.6200', null, null, null, null, null, null, null, null, '14', '0.0000', '0.0000', '0.0000', '0.000000', '0.000000', '0.000000', '0.000000', '0.0000', '0.000000', '0.000000', null, '0.000000', '0.000000', null, null),('762c7975048842c7842548725e61fed9', '481389589861336517', '0', '0', '481385995833542085', null, '大康河碧道工程-给排水工程', '3', 'm2', '2582442.1100', '1020403', '深圳市市政工程消耗量定额(2017)', '1020013', '国标清单(2013)', '市政工程(深建价[2018]25号、建办标函[2019]193号)(安全文明施工费按各专业取费)', '建安材料价格(2020年第 1 月);人工工日价格(2020年第 1 月);园林苗木价格(2020年第一季度)', '2020-01-01 00:00:00', '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('eb40d46cb49f48899f7657dc416e6250', '481389589861336517', '0', '0', '481385995833542085', null, '测试20231024创建单位工程', '3', 'm2', '0.0000', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('df236039fc8246aeb811e107c371371e', '481389589861336517', '0', '0', '481385995833542085', null, '测试单位工程新建20231025', '3', 'm2', '11182630.1700', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1'),('768f00e9993a47688473974329ffd094', '481389589861336517', '0', '0', '481385995833542085', null, '测试新建单位工程1025001', '3', 'm2', '32.3400', '1020105', '深圳市建筑工程消耗量定额(2016)', '1020013', '国标清单(2013)', '建筑工程(深建价[2018]25号、深建市场[2021]20号) (安全文明施工费按各专业取费)', null, null, '0', '1', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '0.0000', '1');";
            executeSqlUtils.executeUpdateSingleSql(insertSQL);
            // 遍历结果集
            for (Map<String, Object> row : resultList2) {
                // 遍历每一行的数据
                for (Map.Entry<String, Object> entry : row.entrySet()) {
                    String columnName = entry.getKey();
                    Object columnValue = entry.getValue();

                    // 处理数据，例如输出到日志
//                System.out.println("Column: " + columnName + ", Value: " + columnValue);
                }
                row.put("Datasource", "指标库");
//            System.out.println("Row processed.");
            }

            // 获取当前数据源的连接， 可获取切换后的连接, 连接⼀定要关闭
            @Cleanup Connection conn = DynamicDataSourceUtil.getCurrentConnection();
            // 清除当前动态库， 后⾯的数据库操作将在主库进⾏
            DynamicDataSourceContextHolder.poll();

            System.out.println("########################################################################");

            List<Map<String, Object>> resultList = executeSqlUtils.executeQuerySql("SELECT * FROM `zbcalc_case_project`");

            // 遍历结果集
            for (Map<String, Object> row : resultList) {
                // 遍历每一行的数据
                for (Map.Entry<String, Object> entry : row.entrySet()) {
                    String columnName = entry.getKey();
                    Object columnValue = entry.getValue();
                    // 处理数据，例如输出到日志
//                System.out.println("Column: " + columnName + ", Value: " + columnValue);
                }
                row.put("Datasource", "主库");
//            System.out.println("Row processed.");
            }
            DynamicDataSourceContextHolder.poll();
            // 合并两个结果集

            List<Map<String, Object>> combinedResult = new ArrayList<>();
//        combinedResult.addAll(resultList);
            Map<String, Object> separatorMap = new HashMap<>();
            separatorMap.put("separator", "*************************");
            combinedResult.addAll(resultList2);
            future.complete(combinedResult);

        } finally {
            @Cleanup Connection conn = DynamicDataSourceUtil.getCurrentConnection();
            // 清除当前动态库， 后⾯的数据库操作将在主库进⾏
            DynamicDataSourceContextHolder.poll();
            return future;
        }
    }

    /**
     * operType:0 审批 ， 1：撤回
     * 根据项目ID更新文件状态
     *
     * @param Id
     * @param flowDetailId
     * @param operType
     */
    public void updateBudgetProjectVersionStatusByCurFlowDetailIdAndId(String Id, String flowDetailId, Integer operType) {
        Optional<ThsBusinessBudgetProjectVersionEntity> businessBudgetProjectVersionEntityOptional = Optional.ofNullable(this.getInfo(Id));
        if (businessBudgetProjectVersionEntityOptional.isPresent()) {
            if (operType == BusinessConstantValues.BUDGET_PROJECT_FLOW_OPERTYPE_APPROVE) {

                if (ObjectUtil.isNotEmpty(flowDetailId)) {
                    businessBudgetProjectVersionEntityOptional.get().setStatus(2);
                } else {
                    //  审批完成
                    businessBudgetProjectVersionEntityOptional.get().setStatus(3);
                    // 单位工程造价编制完成时间
                    businessBudgetProjectVersionEntityOptional.get().setLastModifyTime(Date.from(Instant.now()));
                }
            } else if (operType == BusinessConstantValues.BUDGET_PROJECT_FLOW_OPERTYPE_RETRACT) {
                // 建设项目撤回 ，主记录状态变成待审批
                businessBudgetProjectVersionEntityOptional.get().setStatus(2);
                businessBudgetProjectVersionEntityOptional.get().setLastModifyTime(Date.from(Instant.now()));
            } else if (operType == BusinessConstantValues.BUDGET_PROJECT_FLOW_OPERTYPE_REJECT) {
                // 建设项目驳回 ，主记录状态变成编制中 ，跟建设项目层
                businessBudgetProjectVersionEntityOptional.get().setStatus(0);
                businessBudgetProjectVersionEntityOptional.get().setLastModifyTime(Date.from(Instant.now()));
            }
            ThsBusinessBudgetProjectVersionEntity businessBudgetProjectVersionEntity = new ThsBusinessBudgetProjectVersionEntity();
            BeanUtil.copyProperties(businessBudgetProjectVersionEntityOptional.get(), businessBudgetProjectVersionEntity);
            this.updateById(businessBudgetProjectVersionEntity);
        } else {
            throw new RuntimeException("协同项目不存在");
        }
    }

    /**
     * @param status
     * @description: 根据项目类型和状态获取列表
     * @param: @param templateCase
     * @return: java.util.List<jnpf.entity.ThsBusinessBudgetProjectVersionEntity>
     * @author: wxy
     * @date: 2024/1/25 19:19
     */
    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getListByTemplateCaseAndStatus(Integer templateCase, Integer status) {
        if (ObjectUtil.isNotEmpty(templateCase) && ObjectUtil.isNotEmpty(status)) {
            QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .in(ThsBusinessBudgetProjectVersionEntity::getTemplateCase, templateCase)
                    .in(ThsBusinessBudgetProjectVersionEntity::getStatus, status);
            return this.list(queryWrapper);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * @description: 删除权限判断
     * @param: @param Id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/1/29 16:25
     */
    @Override
    public ActionResult dataDeletePermissionVerify(List<String> ids) {
        ActionResult result = new ActionResult();
        //  标识
        AtomicBoolean flag = new AtomicBoolean(false);
        String strMsg = "";
        UserInfo userInfo = userProvider.get();
        List<ThsBusinessBudgetProjectVersionEntity> budgetProjectVersionEntityList = this.getListByIds(ids);

        for (ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity : budgetProjectVersionEntityList) {
            // 不是责任人不能删除
            if (!budgetProjectVersionEntity.getObligUserId().equals(userInfo.getUserId()) && !budgetProjectVersionEntity.getCreatorId().equals(userInfo.getUserId())) {
                flag.set(true);
                strMsg = "非责任人或非创建人";
                break;
            }

            // 已发布的模板和编制完成的不能修改
            // 编制完成及其以上状态的项目不能删除
            if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getStatus()) && ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getTemplateCase())) {
                if (((budgetProjectVersionEntity.getTemplateCase() == 1) && (budgetProjectVersionEntity.getStatus() == 1)) || (
                        (budgetProjectVersionEntity.getTemplateCase() == 0) && (budgetProjectVersionEntity.getStatus() >= 1))) {
                    flag.set(true);
                    strMsg = "非可删除状态";
                    break;
                }
            }
            // 已归档的数据不能删除
            if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getCaseArchiveStatus()) && (budgetProjectVersionEntity.getCaseArchiveStatus() == 1)) {
                flag.set(true);
                strMsg = "已案例归档不可删除";
                break;
            }
            if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getIndicatorsArchiveStatus()) && (budgetProjectVersionEntity.getIndicatorsArchiveStatus() == 1)) {
                flag.set(true);
                strMsg = "已指标归档不可删除";
                break;
            }

        }

        //  返回结果
        if (flag.get()) {
            result.setMsg(String.format("无权限:%s", strMsg));
            result.setData(false);
            result.setCode(403);
        } else {
            result.setCode(200);
            result.setData(true);
        }
        return result;
    }

    /**
     * @description: 获取列表根据Ids
     * @param: @param ids
     * @return: java.util.List<jnpf.entity.ThsBusinessBudgetProjectVersionEntity>
     * @author: wxy
     * @date: 2024/1/29 16:49
     */
    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getListByIds(List<String> ids) {
        if (ObjectUtil.isNotEmpty(ids) && ids.size() > 0) {
            QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ThsBusinessBudgetProjectVersionEntity::getId, ids);
            return this.list(queryWrapper);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * @Description: 删除计价相关的一套数据
     * @Param: * @param consultId
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 10:49 2024/3/8
     */
    @Override
    public ActionResult deleteBudgetProjectRelatedTables(String consultId) {
        //   查询是否存在建设项目
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, consultId);
        ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = this.getBaseMapper().selectOne(queryWrapper);

        if (thsBusinessBudgetProjectVersionEntity != null) {
            //  查询是否存在已经编辑完成的数据，如果存在，不能删除
            List<ThsBusinessBudgetProjectDetailEntity> listByBudgetProjectId = thsBusinessBudgetProjectDetailService.getListByBudgetProjectId(thsBusinessBudgetProjectVersionEntity.getId());
            List<ThsBusinessBudgetProjectDetailEntity> thsBusinessBudgetProjectDetailEntities = listByBudgetProjectId.stream().filter(thsBusinessBudgetProjectDetailEntity -> thsBusinessBudgetProjectVersionEntity.getStatus() == 1).collect(Collectors.toList());

            if (!thsBusinessBudgetProjectDetailEntities.isEmpty()) {
                return ActionResult.fail("存在编制完成的项目");
            }
            //  造价编制项目结构
            QueryWrapper<ThsBusinessBudgetProjectDetailEntity> thsBusinessBudgetProjectDetailEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectDetailEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectDetailService.remove(thsBusinessBudgetProjectDetailEntityQueryWrapper);

            //  编制说明
            QueryWrapper<ThsBusinessBudgetProjectInstructionsEntity> thsBusinessBudgetProjectInstructionsEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectInstructionsEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectInstructionsEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectInstructionsService.remove(thsBusinessBudgetProjectInstructionsEntityQueryWrapper);

            //  文件历史管理版本表
            QueryWrapper<ThsBusinessBudgetProjectDetailFileHistoryEntity> thsBusinessBudgetProjectDetailFileHistoryEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectDetailFileHistoryEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailFileHistoryEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectDetailFileHistoryService.remove(thsBusinessBudgetProjectDetailFileHistoryEntityQueryWrapper);

            //  单位工程编辑人员表
            QueryWrapper<ThsBusinessBudgetProjectDetailUserEntity> thsBusinessBudgetProjectDetailUserEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectDetailUserEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailUserEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectDetailUserService.remove(thsBusinessBudgetProjectDetailUserEntityQueryWrapper);

            //  项目流程详情表
            QueryWrapper<ThsBusinessBudgetProjectFlowDetailsEntity> thsBusinessBudgetProjectFlowDetailsEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectFlowDetailsEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectFlowDetailsEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectFlowDetailsService.remove(thsBusinessBudgetProjectFlowDetailsEntityQueryWrapper);

            //  流程传递记录表
            QueryWrapper<ThsBusinessBudgetProjectFlowHistoryEntity> thsBusinessBudgetProjectFlowHistoryEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectFlowHistoryEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectFlowHistoryEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectFlowHistoryService.remove(thsBusinessBudgetProjectFlowHistoryEntityQueryWrapper);

            //  流程节点对应的人员表
            QueryWrapper<ThsBusinessBudgetProjectFlowDetailsUserEntity> thsBusinessBudgetProjectFlowDetailsUserEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectFlowDetailsUserEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectFlowDetailsUserEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectFlowDetailsUserService.remove(thsBusinessBudgetProjectFlowDetailsUserEntityQueryWrapper);

            //  费用项目明细/实体清单 审核意见表
            QueryWrapper<ThsBusinessBudgetProjectAuditRemarkEntity> thsBusinessBudgetProjectAuditRemarkEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectAuditRemarkEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectAuditRemarkEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectAuditRemarkService.remove(thsBusinessBudgetProjectAuditRemarkEntityQueryWrapper);

            //  工程造价编制-客户端机器表
            QueryWrapper<ThsBusinessBudgetProjectMachineEntity> thsBusinessBudgetProjectMachineEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectMachineEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectMachineEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectMachineService.remove(thsBusinessBudgetProjectMachineEntityQueryWrapper);

            //  项目的建设工程其他费
            QueryWrapper<ThsBusinessBudgetProjectOtherFeeEntity> thsBusinessBudgetProjectOtherFeeEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectOtherFeeEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectOtherFeeEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectOtherFeeService.remove(thsBusinessBudgetProjectOtherFeeEntityQueryWrapper);

            //  协同文件表
            QueryWrapper<ThsBusinessBudgetProjectDetailFileEntity> thsBusinessBudgetProjectDetailFileEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectDetailFileEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailFileEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectDetailFileService.remove(thsBusinessBudgetProjectDetailFileEntityQueryWrapper);

            // 删除项目组成明细合同文件
            QueryWrapper<ThsBusinessBudgetProjectDetailContractFileEntity> thsBusinessBudgetProjectDetailContractFileEntityQueryWrapper = new QueryWrapper<>();
            thsBusinessBudgetProjectDetailContractFileEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailContractFileEntity::getBudgetProjectId, thsBusinessBudgetProjectVersionEntity.getId());
            thsBusinessBudgetProjectDetailContractFileService.remove(thsBusinessBudgetProjectDetailContractFileEntityQueryWrapper);


            return ActionResult.success("协同几家版本管理,计价模板管理，删除完成");

        }

        return ActionResult.success("没有建设项目,不进行删除");
    }

    /**
     * @Description: Todo 编审模式为审核状态时--上传qdg文件，拆分成qdy
     * @Param: * @param fileStream
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:49 2024/3/15
     */
    @Override
    public ActionResult uploadQdgFile(MultipartFile fileStream) throws DataException {

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String dateStr = format.format(new Date());

        String fileName = XSSEscape.escape(RandomUtil.uuId() + "." + UpUtil.getFileType(fileStream));
        String filePath = BusinessConstantValues.BUDGET_FILES_ROOT_PATH + "/" + dateStr + "/";
        // 上传文件
        FileInfo fileInfo = FileUploadUtils.uploadFile(fileStream, filePath, fileName);
        log.debug("上传文件信息 : {}", fileInfo);
        //  创建解析服务记录
        return createThsFileFiles(fileInfo.getFilename(), filePath);

    }

    /**
     * @Description: Todo 创建一个解析服务记录
     * @Param: * @param
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 10:27 2024/3/15
     */
    public ActionResult createThsFileFiles(String fileName, String filePath) {
        ThsfileFilesEntity thsfileFilesEntity = new ThsfileFilesEntity();
        String id = RandomUtil.uuId();
        thsfileFilesEntity.setId(id);
        thsfileFilesEntity.setName(fileName);
        thsfileFilesEntity.setFileSize(BigDecimal.valueOf(0));
        thsfileFilesEntity.setFilePath(filePath + fileName);
        thsfileFilesEntity.setAnalysisType(0);
        thsfileFilesEntity.setItemType(0);
        thsfileFilesEntity.setSplitFile(1);
        thsfileFilesEntity.setAnalysisMode(1);
        thsfileFilesEntity.setAnalysisLev(0);
        thsfileFilesEntity.setSourceId(1);
        thsfileFilesEntity.setSequence(thsfileFilesService.getMaxSequence() != null ? thsfileFilesService.getMaxSequence() + 1 : 1);
        thsfileFilesEntity.setCreateTime(new Date());

        thsfileFilesService.create(thsfileFilesEntity);

        return ActionResult.success(thsfileFilesEntity);
    }

    /**
     * @Description: Todo 编审模式时审核状态的情况下，已经上传完成文件，解析完成，点击确认按钮
     * @Param: * @param
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:31 2024/3/15
     */
    @Override
    public ActionResult createBudgetProjectVersionByBsType(ThsBusinessBudgetProjectVersionForm thsBusinessBudgetProjectVersionForm) {

        ActionResult actionResult = new ActionResult<>();
        if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectVersionForm.isImportSendFileFlag()) && thsBusinessBudgetProjectVersionForm.isImportSendFileFlag()) {

            String id = "";
            //  解析Id为空
            if (ObjectUtil.isEmpty(thsBusinessBudgetProjectVersionForm.getThsfileFileId())) {
                return ActionResult.fail("thsfileFileId为空");
            }
            //  编审类型为空
            if (ObjectUtil.isEmpty(thsBusinessBudgetProjectVersionForm.getEditorialTypeId())) {
                return ActionResult.fail("editorialTypeId");
            }

            //  id为空,新建
            if (ObjectUtil.isEmpty(thsBusinessBudgetProjectVersionForm.getId())) {
                UserInfo userInfo = userProvider.get();
                id = UUID.randomUUID().toString();
                ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = BeanUtil.copyProperties(thsBusinessBudgetProjectVersionForm, ThsBusinessBudgetProjectVersionEntity.class);
                thsBusinessBudgetProjectVersionEntity.setId(id);

                // 项目分类
                ThsBusinessConsultProjectEntity businessConsultProjectEntity = thsBusinessConsultProjectService.getInfo(thsBusinessBudgetProjectVersionForm.getConsultProjectId());
                thsBusinessBudgetProjectVersionEntity.setCategoryDictId(businessConsultProjectEntity.getProjectTypeId());
                //
//            thsBusinessBudgetProjectVersionEntity.setName(businessConsultProjectEntity.getName()==null? thsBusinessBudgetProjectVersionEntity.getName():businessConsultProjectEntity.getName());

                // 审批流程
                thsBusinessBudgetProjectVersionEntity.setSysFlowId(businessConsultProjectEntity.getSysFlowId());
                // 创建人创建时间
                thsBusinessBudgetProjectVersionEntity.setCreatorId(userInfo.getUserId());
                thsBusinessBudgetProjectVersionEntity.setObligUserId(userInfo.getUserId());
                thsBusinessBudgetProjectVersionEntity.setCreateTime(Date.from(Instant.now()));
                // 新建编制中
                thsBusinessBudgetProjectVersionEntity.setStatus(0);
                thsBusinessBudgetProjectVersionEntity.setTemplateCase(ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT);

                if (this.getMaxSequence() != null) {
                    thsBusinessBudgetProjectVersionEntity.setSequence(this.getMaxSequence() + 1);
                } else {
                    thsBusinessBudgetProjectVersionEntity.setSequence(1);
                }
                // 版本号
                if (this.GetMaxVerByConsultId(thsBusinessBudgetProjectVersionForm.getConsultProjectId()) != null) {
                    thsBusinessBudgetProjectVersionEntity.setVer(this.GetMaxVerByConsultId(thsBusinessBudgetProjectVersionForm.getConsultProjectId()) + 1);
                } else {
                    thsBusinessBudgetProjectVersionEntity.setVer(1);
                }
                //  更新主记录信息
                this.save(thsBusinessBudgetProjectVersionEntity);

                // 新建的协同文件设置为终版
                this.setFinalVersion(thsBusinessBudgetProjectVersionEntity.getId());
            } else {

                ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = this.getInfo(thsBusinessBudgetProjectVersionForm.getId());
                thsBusinessBudgetProjectVersionEntity.setThsfileFileId(thsBusinessBudgetProjectVersionForm.getThsfileFileId());
                thsBusinessBudgetProjectVersionEntity.setEditorialTypeId(thsBusinessBudgetProjectVersionForm.getEditorialTypeId());

                id = thsBusinessBudgetProjectVersionEntity.getId();
                //  更新主记录信息
                this.updateById(thsBusinessBudgetProjectVersionEntity);
            }
            actionResult = thsBusinessBudgetProjectDetailService.syncThsFileQdgStructure(thsBusinessBudgetProjectVersionForm.getSysFlowId(), id, thsBusinessBudgetProjectVersionForm.getThsfileFileId(), thsBusinessBudgetProjectVersionForm.getName(), thsBusinessBudgetProjectVersionForm.isCreateCompilationFlag());
        } else {
            actionResult = this.createProjectByNotImportOriginalSendFile(thsBusinessBudgetProjectVersionForm);
        }
        thsfileFilesService.deleteThsFileSplitFileAndParseData(thsBusinessBudgetProjectVersionForm.getThsfileFileId());
        return actionResult;
    }

    /**
     * @Description: Todo 删除文件时，重写解析服务记录
     * @Param: * @param fileId
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:50 2024/3/22
     */
    @Override
    public ActionResult createProjectDeleteSysFileAndThsFileFiles(String fileId) {
        if (ObjectUtil.isEmpty(fileId)) {
            return ActionResult.fail("文件id为空");
        }
        ThsfileFilesEntity thsfileFilesEntity = thsfileFilesService.getInfo(fileId);
        if (ObjectUtil.isEmpty(thsfileFilesEntity)) {
            return ActionResult.fail("解析记录不存在");
        }
        String filePath = thsfileFilesEntity.getFilePath();
        // 删除文件
        if (filePath.contains(String.valueOf(CharPool.SLASH)) || filePath.contains(String.valueOf(CharPool.BACKSLASH))) {

            String fileRootPath = environment.getProperty("config.file-storage.local-plus[0].base-path");
            // 确保 fileRootPath 以斜杠结尾
            if (!fileRootPath.endsWith("/")) {
                fileRootPath += "/";
            }
            String sourceFilePath = fileRootPath + filePath;

            FileUtil.del(sourceFilePath);
        }
        // 修改ThsfileFiles文件记录
        thsfileFilesEntity.setAnalysisType(4);
        this.thsfileFilesService.updateById(thsfileFilesEntity);

        return ActionResult.success("删除文件成功");
    }

    /**
     * @param isFinalize
     * @Description: Todo 获取终版数据
     * @Param: * @param consultId
     * @return: java.util.List<jnpf.entity.ThsBusinessBudgetProjectVersionEntity>
     * @Author: hrj
     * @Date: 9:24 2024/6/11
     */
    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getListByConsultIdAndIsFinalize(String consultId, Integer isFinalize) {
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, consultId).eq(ThsBusinessBudgetProjectVersionEntity::getIsFinalize, isFinalize);
        return this.list(queryWrapper);
    }

    /**
     * @param userId
     * @description: 项目交接
     * @param: @param id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/8/27 10:38
     */
    @Override
    public ActionResult turnOverProject(String id, String userId) {
        ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
        if (budgetProjectVersionEntity == null) {
            return ActionResult.fail("项目不存在");
        }
        UserInfo userInfo = userProvider.get();

        UserEntity userEntity = userApi.getInfo(userId);
        if (ObjectUtil.isEmpty(userEntity)) {
            return ActionResult.fail("交接用户不存在");
        }

        if (ObjectUtil.isEmpty(userInfo)) {
            return ActionResult.fail("用户未登录");
        }
        if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getCreatorId())
                && (!userInfo.getUserId().equals(budgetProjectVersionEntity.getCreatorId()))) {
            return ActionResult.fail("非当前项目创建人，不能交接");
        }
        budgetProjectVersionEntity.setCreatorId(userId);
        return ActionResult.success(this.updateById(budgetProjectVersionEntity));
    }

    /**
     * @description: 不导入原始送审文件创建协同项目
     * @param: @param id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/10/21 10:38
     */
    @Override
    public ActionResult createProjectByNotImportOriginalSendFile(ThsBusinessBudgetProjectVersionForm thsBusinessBudgetProjectVersionForm) {
        String b = this.checkForm(thsBusinessBudgetProjectVersionForm, 0);
        if (StringUtil.isNotEmpty(b)) {
            return ActionResult.fail(b);
        }
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String mainId = RandomUtil.uuId();
        UserInfo userInfo = userProvider.get();
        // UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        GeneraterSwapUtil.swapDatetime(thsBusinessBudgetProjectVersionForm);
        ThsBusinessBudgetProjectVersionEntity entity = JsonUtil.getJsonToBean(thsBusinessBudgetProjectVersionForm, ThsBusinessBudgetProjectVersionEntity.class);

        // 项目分类
        ThsBusinessConsultProjectEntity businessConsultProjectEntity = thsBusinessConsultProjectService.getInfo(thsBusinessBudgetProjectVersionForm.getConsultProjectId());
        entity.setCategoryDictId(businessConsultProjectEntity.getProjectTypeId());

        // 审批流程
        entity.setSysFlowId(businessConsultProjectEntity.getSysFlowId());
        // 创建人创建时间
        entity.setCreatorId(userInfo.getUserId());
        entity.setObligUserId(userInfo.getUserId());
        entity.setId(mainId);
        entity.setCreateTime(Date.from(Instant.now()));
        // 新建编制中
        entity.setStatus(0);
        entity.setTemplateCase(ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT);
        //        entity.setName(businessConsultProjectEntity.getName()==null ? thsBusinessBudgetProjectVersionForm.getName():businessConsultProjectEntity.getName());

        if (this.getMaxSequence() != null) {
            entity.setSequence(this.getMaxSequence() + 1);
        } else {
            entity.setSequence(1);
        }
        // 版本号
        if (this.GetMaxVerByConsultId(thsBusinessBudgetProjectVersionForm.getConsultProjectId()) != null) {
            entity.setVer(this.GetMaxVerByConsultId(thsBusinessBudgetProjectVersionForm.getConsultProjectId()) + 1);
        } else {
            entity.setVer(1);
        }
        try {
            this.save(entity);
            // 创建计价文件结构建设项目层
            if (this.SaveBusinessBudgetProjectDetailJSXM(entity, ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_PROJECT)) {
                // 设置终版
                this.setFinalVersion(entity.getId());
                return ActionResult.success("创建成功");
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ActionResult.fail("创建失败");
            }

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ActionResult.fail("创建失敗" + e.getMessage());
        }
    }

    /**
     * @description: 当前待办项目IDs
     * @param: @param id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/10/30 10:38
     */
    @Override
    public List<String> getCurrentToDoBudgetProjectIds(String userId) {
        return this.getBaseMapper().selectCurrentToDoBudgetProjectIds(userId);
    }

    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> selectByContractId(String contractId) {
        return this.getBaseMapper().selectByContractId(contractId);
    }

    @Override
    public List<ThsBusinessBudgetProjectVersionEntity> getListInConsultIdAndIsFinalize(List<String> consultId, Integer isFinalize) {
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, consultId).eq(ThsBusinessBudgetProjectVersionEntity::getIsFinalize, isFinalize);
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * @description: 历史待办项目IDs
     * @param: @param id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/10/30 10:38
     */
    @Override
    public List<String> getHistoryBudgetProjectIds(String userId) {
        return this.getBaseMapper().selectHistoryBudgetProjectIds(userId);
    }

    /**
     * @description: 排期待办项目IDs
     * @param: @param id
     * @return: jnpf.base.ActionResult
     * @author: wxy
     * @date: 2024/10/30 10:38
     */
    @Override
    public List<String> getFlowNextBudgetProjectIds(String userId) {
        return this.getBaseMapper().selectFlowNextBudgetProjectIds(userId);
    }

    /**
     * @description: 协同编审类型 1：是编审类型（编制） 2是评审复核类型 （审核）
     * @param: @param budgetProjectId
     * @return: java.lang.Integer
     * @author: wxy
     * @date: 2024/3/25 15:33
     */
    @Override
    public Integer getProjecteditType(String id) {
        Integer editType = -1;
        if (StringUtil.isNotEmpty(id)) {
            //
            ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity = this.getInfo(id);
            String editorialTypeId = "";
            if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity) && ObjectUtil.isNotEmpty(budgetProjectVersionEntity.getEditorialTypeId()) && !budgetProjectVersionEntity.getEditorialTypeId().equals("")) {
                editorialTypeId = budgetProjectVersionEntity.getEditorialTypeId();
            }
            // 从咨询项目取值
            if (editorialTypeId.equals("")) {
                if (ObjectUtil.isNotEmpty(budgetProjectVersionEntity)) {
                    Optional<ThsBusinessConsultProjectEntity> optionalThsBusinessConsultProject = Optional.ofNullable(thsBusinessConsultProjectService.getInfo(budgetProjectVersionEntity.getConsultProjectId()));
                    editorialTypeId = optionalThsBusinessConsultProject.get().getEditorialTypeId();
                }
            }
            boolean isAuditProject = false;
            if (!editorialTypeId.equals("")) {
                // 编审类型
                List<Map<String, Object>> mapList = thsSysDictDetailsService.getListTree("QJ-BSLX", false);
                for (Map<String, Object> map : mapList) {
                    String categoryId = map.get("id").toString();
                    String itemText = map.get("itemText").toString();
                    String itemValue = map.get("itemValue").toString();
                    if (categoryId != null && categoryId.equals(editorialTypeId)) {
                        if (StringUtil.isNotEmpty(itemText) && itemText.equals("审核") || (StringUtil.isNotEmpty(itemValue) && itemValue.equals("AUDIT_PROJECT"))) {
                            isAuditProject = true;
                            break;
                        }
                    }
                }
                if (!isAuditProject) {
                    editType = 1;
                } else {
                    editType = 2;
                }
            }
        }
        return editType;
    }

    /**
     * @param consultProjectId
     * @return
     * @description: 根据咨询项目ID获取终版协同项目
     * <AUTHOR>
     * @date 2024-11-20
     */

    @Override
    public ThsBusinessBudgetProjectVersionEntity findFinalProjectVersion(String consultProjectId) {
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, consultProjectId)
                .eq(ThsBusinessBudgetProjectVersionEntity::getIsFinalize, true);
        return this.getOne(queryWrapper, false);
    }

    @Override
    public String getEditorialTypeIdByConsultId(String consultProjectId) {
        QueryWrapper<ThsBusinessBudgetProjectVersionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId, consultProjectId)
                .orderByAsc(ThsBusinessBudgetProjectVersionEntity::getSequence);

        List<ThsBusinessBudgetProjectVersionEntity> list = this.list(queryWrapper);
        if (list.isEmpty()) {
            return null;
        }

        ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = list.get(0);
        return thsBusinessBudgetProjectVersionEntity.getEditorialTypeId();
    }


    /**
     * 我的当前待办数量
     *
     * @param
     * @return {@link int}
     * <AUTHOR>
     * @date 2025/7/28 11:11
     */
    @Override
    public int getCurrentToDoBudgetProjectCount() {
        // 获取当前待办项目ID列表
        List<String> viewTypeBudgetProjectId = this.getCurrentToDoBudgetProjectIds(userProvider.get().getUserId());

        if (viewTypeBudgetProjectId == null || viewTypeBudgetProjectId.isEmpty()) {
            return 0;
        }

        // 创建查询条件，与list方法保持一致
        ThsBusinessBudgetProjectVersionPagination pagination = new ThsBusinessBudgetProjectVersionPagination();
        pagination.setTemplateCase(0);
        pagination.setStatus(Arrays.asList(0, 1, 2));
        pagination.setDataType("1"); // 不分页

        // 获取所有符合条件的项目版本
        List<ThsBusinessBudgetProjectVersionEntity> allVersions = this.getList(pagination);

        // 过滤出在当前待办列表中的项目
        return (int) allVersions.stream()
                .filter(version -> viewTypeBudgetProjectId.contains(version.getId()))
                .count();
    }

}
