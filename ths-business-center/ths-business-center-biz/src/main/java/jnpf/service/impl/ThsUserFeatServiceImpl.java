package jnpf.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jnpf.constant.GlobalConst;
import jnpf.entity.*;
import jnpf.model.performance.ThsProjectCostDetailModel;
import jnpf.model.performance.ThsUserFeatModel;
import jnpf.model.performance.ThsUserFeatPagination;
import jnpf.model.thsbusinessbudgetprojectdetailuser.ThsBusinessBudgetProjectDetailUserForm;
import jnpf.model.thsbusinessbudgetprojectflowdetails.ThsBusinessBudgetProjectFlowDetailsForm;
import jnpf.model.thsbusinessbudgetprojectflowdetailsuser.ThsBusinessBudgetProjectFlowDetailsUserForm;
import jnpf.model.thsperformanceallocation.ThsPerformanceAllocationForm;
import jnpf.model.thsperformanceconfig.ThsCalcPerformanceModel;
import jnpf.model.thsperformancerule.ThsPerformanceRuleForm;
import jnpf.permission.entity.OrganizeEntity;
import jnpf.permission.entity.PositionEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.OrganizeService;
import jnpf.permission.service.PositionService;
import jnpf.permission.service.UserService;
import jnpf.service.*;
import jnpf.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @auther wxy
 * @date 2023-10-23 15:35
 */
@Service
public class ThsUserFeatServiceImpl implements ThsUserFeatService {

    private final Logger log = LoggerFactory.getLogger(ThsUserFeatServiceImpl.class);

    @Autowired
    private ThsBusinessBudgetProjectDetailService thsBusinessBudgetProjectDetailService;
    @Autowired
    private ThsBusinessBudgetProjectVersionService thsBusinessBudgetProjectVersionService;

    @Autowired
    private ThsBusinessConsultProjectService thsBusinessConsultProjectService;

    @Autowired
    private ThsManageContractService thsManageContractService;

    @Resource
    private ThsPerformanceConfigService thsPerformanceConfigApi;

    @Autowired
    private ThsBusinessBudgetProjectFlowDetailsUserService thsBusinessBudgetProjectFlowDetailsUserService;

    @Autowired
    private ThsBusinessBudgetProjectFlowDetailsService thsBusinessBudgetProjectFlowDetailsService;

    @Autowired
    private ThsBusinessBudgetProjectDetailUserService thsBusinessBudgetProjectDetailUserService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizeService organizeApi;

    @Autowired
    private PositionService positionService;

    @Autowired
    private ThsSysDictDetailsService thsSysDictDetailsService;

    /**
     * @param target
     * @description:
     * @param: @param source
     * @return: void
     * @author: wxy
     * @date: 2024/7/16 17:13
     */
    public void CopyDetailMapToProjectCostDetail(Map<String, Object> source, ThsProjectCostDetailModel target) {
        try {
            BeanUtil.copyProperties(source, target);
            // 根据具体属性进行赋值和处理
            target.setId(MapUtils.toStringSafe(source.get("id")));
            target.setPid(MapUtils.toStringSafe(source.get("pid")));
            target.setBudgetProjectId(MapUtils.toStringSafe(source.get("budgetProjectId")));
            target.setCode((String) source.get("code")); // 确保source.get("code")是String类型
            target.setName(MapUtils.toStringSafe(source.get("GCName")));
            target.setTotalPrice(MapUtils.toBigDecimalSafe(source.get("totalMoney")));
            target.setPrjOtherMoney(MapUtils.toBigDecimalSafe(source.get("prjOtherMoney")));
            target.setItemType((Integer) source.get("item_type")); // 确保source.get("itemType")是Integer类型
            target.setStatus((Integer) source.get("status")); // 确保source.get("status")是Integer类型

            //  页面个人业务业绩统计 单项、单位不显示相关金额逻辑
            // if (source.get("item_type") != null && (Integer) source.get("item_type") == ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_JSXM) {
            // target.setPrjTotalMoney(MapUtils.toBigDecimalSafe(source.get("totalMoney")).add(MapUtils.toBigDecimalSafe(source.get("prjOtherMoney"))));
            // }
            // if (source.get("item_type") != null && (Integer) source.get("item_type") != ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_DWGC) {
            //     target.setFeatMoney(BigDecimal.ZERO);
            // } else {
            //
            // }
            target.setPrjTotalMoney(MapUtils.toBigDecimalSafe(source.get("totalMoney")).add(MapUtils.toBigDecimalSafe(source.get("prjOtherMoney"))));
            target.setFeatMoney(MapUtils.toBigDecimalSafe(source.get("FeatMoney")));

            // 调整系统是单位工程的调整系数，如果不设置为1
            if (source.get("adjustRate") == null || source.get("adjustRate").equals(BigDecimal.ZERO)) {
                target.setAdjustRate(new BigDecimal(1));
            } else {
                target.setAdjustRate(MapUtils.toBigDecimalSafe(source.get("adjustRate")));
            }
            // WXY TODO 2023/11/17 扣减业务量和增加业务量的规则看了存储过程都是0，不知道这个具体规则，暂时先置为0，
            target.setIncFeatMoney(new BigDecimal("0.0"));
            target.setDecFeatMoney(new BigDecimal("0.0"));

            //  计量标准
            if (target.getFeatMoney() == null || target.getFeatTotalMoney() == null || target.getFeatTotalMoney().compareTo(BigDecimal.ZERO) == 0) {
                target.setRate(BigDecimal.ZERO);
            } else {
                target.setRate(target.getFeatMoney().divide(target.getFeatTotalMoney(), 2, RoundingMode.HALF_UP));
                log.error("Rate:{}", target.getFeat());
            }
            //  考核业绩
            if (target.getFeatMoney() == null || target.getRate() == null) {
                target.setFeat(BigDecimal.ZERO);
            } else {
                target.setFeat(target.getFeatMoney().multiply(target.getRate()).setScale(2, RoundingMode.HALF_UP));
                log.error("Feat:{}", target.getFeat());
            }
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }

    }

    /**
     * 将协同造价详情
     *
     * @param source
     * @param target
     * @return
     */

    public void CopyBudgetProjectDetailToProjectCostDetail(ThsBusinessBudgetProjectDetailEntity source, ThsProjectCostDetailModel target) {
//        target=JsonUtil.getJsonToBean(source,ThsProjectCostDetailModel.class);
        try {
//            CommonUtil.copyProperties(source, target);
            BeanUtil.copyProperties(source, target);
            target.setId(source.getId());
            target.setPid(source.getPid());
            target.setBudgetProjectId(target.getBudgetProjectId());
            target.setCode(source.getCode());
            target.setName(source.getName());
            target.setTotalPrice(source.getTotalPrice());
            target.setPrjOtherMoney(source.getPrjOtherMoney());
            target.setItemType(source.getItemType());
            target.setFeatMoney(source.getTotalPrice());
            target.setStatus(source.getStatus());

            if (source.getItemType() == ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_JSXM) {
                target.setPrjTotalMoney(source.getTotalPrice().add(source.getPrjOtherMoney()));
            }
            if (source.getItemType() != ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_DWGC) {
                target.setFeatMoney(new BigDecimal("0.0"));
            } else {
                target.setFeatMoney(source.getTotalPrice());
            }

            // 调整系统是单位工程的调整系数，如果不设置为1
            if (source.getAdjustRate() == null || source.getAdjustRate().compareTo(BigDecimal.ZERO) == 0) {
                target.setAdjustRate(new BigDecimal(1));
            }
            // WXY TODO 2023/11/17 扣减业务量和增加业务量的规则看了存储过程都是0，不知道这个具体规则，暂时先置为0，
            target.setIncFeatMoney(new BigDecimal("0.0"));
            target.setDecFeatMoney(new BigDecimal("0.0"));
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }

    }


    /**
     * 获取编制完成的单位工程列表数据
     *
     * @param thsUserFeatPagination
     * @return
     */

    @Override
    public List<ThsBusinessBudgetProjectDetailEntity> getUnitProjectList(ThsUserFeatPagination thsUserFeatPagination) {
//        thsUserFeatPagination.setItemType(ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_DWGC);
//        return thsBusinessBudgetProjectDetailService.getTypeList(thsUserFeatPagination, thsUserFeatPagination.getDataType());
        return Collections.emptyList();
    }


    /**
     * 根据List获取父节点List
     *
     * @param list
     * @return
     */
    @Override
    public List<ThsBusinessBudgetProjectDetailEntity> getParentProjectList(List<ThsBusinessBudgetProjectDetailEntity> list) {
        List<String> Ids = list.stream().map(ThsBusinessBudgetProjectDetailEntity::getPid).collect(Collectors.toList());
        if (Ids != null && !ObjectUtil.isEmpty(Ids)) {
            return thsBusinessBudgetProjectDetailService.listByIds(Ids);
        } else {
            return null;
        }
    }


    /**
     * 获取工程造价数据
     *
     * @param thsUserFeatPagination
     * @return
     */
    @Override
    public List<ThsProjectCostDetailModel> getProjectCostDetailList(ThsUserFeatPagination thsUserFeatPagination) {
        List<ThsProjectCostDetailModel> projectCostDetailModelList = new ArrayList<>();
        thsUserFeatPagination.setItemType(ThsBusinessBudgetProjectDetailEntity.ITEM_TYPE_DWGC);

        String contractCode = thsUserFeatPagination.getContractCode() != null ? thsUserFeatPagination.getContractCode().toString() : null;
        String finishBeginTime = thsUserFeatPagination.getFinishBeginTime() != null ? thsUserFeatPagination.getFinishBeginTime().toString() : null;
        String finishEndTime = thsUserFeatPagination.getFinishEndTime() != null ? thsUserFeatPagination.getFinishEndTime().toString() : null;
        // 打回申请
        Integer forceReturnFlag = thsUserFeatPagination.getForceReturnFlag() != null ? thsUserFeatPagination.getForceReturnFlag() : 0;
        String reqRetBeginDate = thsUserFeatPagination.getReqRetBeginDate() != null ? thsUserFeatPagination.getReqRetBeginDate().toString() : null;
        String reqRetEndDate = thsUserFeatPagination.getReqRetEndDate() != null ? thsUserFeatPagination.getReqRetEndDate().toString() : null;

        // 常规造价类型的绩效，
        List<Map<String, Object>> projectList = thsBusinessBudgetProjectDetailService.getFeatZJProjectList(contractCode, forceReturnFlag, finishBeginTime, finishEndTime, reqRetBeginDate, reqRetEndDate);
        for (Map<String, Object> objectMap : projectList) {
            ThsProjectCostDetailModel projectCostDetailModel = new ThsProjectCostDetailModel();
            this.CopyDetailMapToProjectCostDetail(objectMap, projectCostDetailModel);
            projectCostDetailModelList.add(projectCostDetailModel);
        }


        // 协同版本Ids
        List<String> budgetProjectIds = null;
        if (projectList != null && !projectList.isEmpty()) {
            budgetProjectIds = projectList.stream()
                    .map(map -> (String) map.get("budgetProjectId"))
                    .filter(id -> id != null && !id.isEmpty())
                    .collect(Collectors.toList());
        }

        List<ThsBusinessBudgetProjectVersionEntity> budgetProjectVersionEntityList = null;
        if (budgetProjectIds != null && ObjectUtil.isNotEmpty(budgetProjectIds)) {
            budgetProjectVersionEntityList = thsBusinessBudgetProjectVersionService.listByIds(budgetProjectIds);
        }
        // 咨询项目列表
        List<String> consultIds = null;
        List<ThsBusinessConsultProjectEntity> consultProjectEntityList = null;
        if (budgetProjectVersionEntityList != null && ObjectUtil.isNotEmpty(budgetProjectVersionEntityList)) {
            consultIds = budgetProjectVersionEntityList.stream().map(ThsBusinessBudgetProjectVersionEntity::getConsultProjectId).collect(Collectors.toList());
        }
        if (consultIds != null && ObjectUtil.isNotEmpty(consultIds)) {
            consultProjectEntityList = thsBusinessConsultProjectService.listByIds(consultIds);
        }
        // 合同列表
        List<String> contractIds = null;
        if (consultProjectEntityList != null && ObjectUtil.isNotEmpty(consultProjectEntityList)) {
            contractIds = consultProjectEntityList.stream().map(ThsBusinessConsultProjectEntity::getContractId).collect(Collectors.toList());
        }
        if (ObjectUtil.isEmpty(contractIds) || contractIds == null) {
            return null;
        }

        // 根据businessTypeId 查询对应的项目类型名称
        List<Map<String, Object>> businessTypeList = thsSysDictDetailsService.getListTree(ManagementConstantValues.CONTRACT_BUSINESS_TYPE_CODE, false);

        List<ThsManageContractEntity> contractEntityList = thsManageContractService.listByIds(contractIds);

        // 填充协同的数据
        for (ThsBusinessBudgetProjectVersionEntity budgetProjectVersionEntity : budgetProjectVersionEntityList) {
            // 编制人和流程数据
            List<ThsBusinessBudgetProjectDetailUserEntity> budgetProjectDetailsUserList = thsBusinessBudgetProjectDetailUserService.findByProjectIdEqualsOrderBySequenceAsc(budgetProjectVersionEntity.getId());
            List<ThsBusinessBudgetProjectFlowDetailsEntity> budgetProjectFlowDetailsList = thsBusinessBudgetProjectFlowDetailsService.findByBudgetProjectIdEqualsOrderBySequenceAsc(budgetProjectVersionEntity.getId());
            List<ThsBusinessBudgetProjectFlowDetailsUserEntity> budgetProjectFlowDetailsUserList = thsBusinessBudgetProjectFlowDetailsUserService.findByBudgetProjectIdEqualsOrderBySequenceAsc(budgetProjectVersionEntity.getId());

            projectCostDetailModelList.stream()
                    .filter(projectCostDetailModel -> projectCostDetailModel.getBudgetProjectId().equals(budgetProjectVersionEntity.getId()))
                    .forEach(filteredEntity -> {

                        // 编制人
                        List<ThsBusinessBudgetProjectDetailUserForm> newBudgetProjectDetailUserFormList = new ArrayList<>();
                        // 审核流程
                        List<ThsBusinessBudgetProjectFlowDetailsForm> newBudgetProjectFlowDetailsFormList = new ArrayList<>();


                        List<ThsBusinessBudgetProjectDetailUserEntity> budgetProjectDetailsUsers = budgetProjectDetailsUserList.stream().filter(
                                budgetProjectDetailsUser -> budgetProjectDetailsUser.getDetailId() != null && budgetProjectDetailsUser.getDetailId().equals(filteredEntity.getId())).collect(Collectors.toList()
                        );
                        budgetProjectDetailsUsers.forEach(budgetProjectDetailsUser -> {
                            // 复制对象
                            ThsBusinessBudgetProjectDetailUserForm businessBudgetProjectDetailUserFormNew = new ThsBusinessBudgetProjectDetailUserForm();

                            BeanUtil.copyProperties(budgetProjectDetailsUser, businessBudgetProjectDetailUserFormNew);
                            newBudgetProjectDetailUserFormList.add(businessBudgetProjectDetailUserFormNew);
                        });

                        // 审核流程一套
                        List<ThsBusinessBudgetProjectFlowDetailsEntity> BudgetProjectFlowDetails = budgetProjectFlowDetailsList.stream().filter(budgetProjectDetail -> budgetProjectDetail.getDetailId().equals(filteredEntity.getId())).collect(Collectors.toList());
                        BudgetProjectFlowDetails.forEach(BudgetProjectFlowDetailEntity -> {
                            // 复制对象审核流程
                            ThsBusinessBudgetProjectFlowDetailsForm budgetProjectFlowDetailsFormNew = new ThsBusinessBudgetProjectFlowDetailsForm();

                            BeanUtil.copyProperties(BudgetProjectFlowDetailEntity, budgetProjectFlowDetailsFormNew);

                            // 过滤当前流程的审核人
                            List<ThsBusinessBudgetProjectFlowDetailsUserEntity> budgetProjectFlowDetailsUsers = budgetProjectFlowDetailsUserList.stream()
                                    .filter(budgetProjectFlowDetailsUser -> budgetProjectFlowDetailsUser.getDetailId().equals(filteredEntity.getId()))
                                    .filter(budgetProjectFlowDetailsUser -> budgetProjectFlowDetailsUser.getFlowDetailId().equals(BudgetProjectFlowDetailEntity.getId()))
                                    .collect(Collectors.toList());

                            // 当前流程审核人
                            List<ThsBusinessBudgetProjectFlowDetailsUserForm> newBudgetProjectFlowDetailsUserForms = new ArrayList<>();
                            budgetProjectFlowDetailsUsers.forEach(budgetProjectFlowDetailsUserEntity -> {
                                // 复制对象
                                ThsBusinessBudgetProjectFlowDetailsUserForm budgetProjectFlowDetailsUserFormNew = new ThsBusinessBudgetProjectFlowDetailsUserForm();
                                BeanUtil.copyProperties(budgetProjectFlowDetailsUserEntity, budgetProjectFlowDetailsUserFormNew);
                                newBudgetProjectFlowDetailsUserForms.add(budgetProjectFlowDetailsUserFormNew);
                            });
                            budgetProjectFlowDetailsFormNew.setBudgetProjectFlowDetailsUserList(newBudgetProjectFlowDetailsUserForms);

                            newBudgetProjectFlowDetailsFormList.add(budgetProjectFlowDetailsFormNew);
                        });

                        // 在这里对每个过滤后的实体进行属性赋值操作
                        filteredEntity.setBudgetProjectDetailUserList(newBudgetProjectDetailUserFormList);
                        filteredEntity.setBudgetProjectFlowDetailsList(newBudgetProjectFlowDetailsFormList);

                        filteredEntity.setBudgetProjectName(budgetProjectVersionEntity.getName());
                        filteredEntity.setConsultProjectId(budgetProjectVersionEntity.getConsultProjectId());
                    });
        }

        // 填充咨询项目数据
        for (ThsBusinessConsultProjectEntity consultProjectEntity : consultProjectEntityList) {
            // 检查 consultProjectEntity 是否为 null
            if (consultProjectEntity != null) {
                projectCostDetailModelList.stream()
                        .filter(projectCostDetailModel -> {
                            return projectCostDetailModel != null && projectCostDetailModel.getConsultProjectId() != null &&
                                    projectCostDetailModel.getConsultProjectId().equals(consultProjectEntity.getId());
                        })
                        .forEach(filteredEntity -> {
                            // 在这里对每个过滤后的实体进行属性赋值操作
                            filteredEntity.setConsultProjectName(consultProjectEntity.getName());
                            filteredEntity.setConsultProjectCode(consultProjectEntity.getCode());
                            filteredEntity.setConsultingTypeID(consultProjectEntity.getConsultingTypeId());
                            filteredEntity.setEditorialTypeId(consultProjectEntity.getEditorialTypeId());
                            // 项目经理负责人
                            filteredEntity.setManagerUserId(consultProjectEntity.getManagerUserId());
                            filteredEntity.setContractId(consultProjectEntity.getContractId());
                            //  赋值项目类型
                            filteredEntity.setBusinessTypeId(consultProjectEntity.getBusinessTypeId());
                            // 赋值编审类型
                            for (Map<String, Object> businessTypeMap : businessTypeList) {
                                if (MapUtil.getStr(businessTypeMap, "id").equals(consultProjectEntity.getBusinessTypeId())) {
                                    filteredEntity.setBusinessTypeName((String) businessTypeMap.get("itemText"));
                                }
                            }
                        });
            }
        }

        // 合同数据
        for (ThsManageContractEntity contractEntity : contractEntityList) {
            if (contractEntity != null) {
                projectCostDetailModelList.stream()
                        .filter(projectCostDetailModel -> {
                            return projectCostDetailModel != null && projectCostDetailModel.getContractId() != null &&
                                    projectCostDetailModel.getContractId().equals(contractEntity.getId());
                        })
                        .forEach(filteredEntity -> {
                            // 在这里对每个过滤后的实体进行属性赋值操作
                            filteredEntity.setContractName(contractEntity.getName());
                            filteredEntity.setContractCode(contractEntity.getCode());
                        });
            }
        }

        return projectCostDetailModelList;
    }


    /**
     * 初始化计算表达式
     *
     * @param thsCalcPerformanceModelList
     */
    public void initializeFormula(List<ThsCalcPerformanceModel> thsCalcPerformanceModelList) {

        if (ObjectUtil.isNotEmpty(thsCalcPerformanceModelList)) {
            for (ThsCalcPerformanceModel calcPerformanceModel : thsCalcPerformanceModelList) {

                List<ThsPerformanceRuleForm> performanceRuleFormList = calcPerformanceModel.getPerformanceRuleList();
                // 梯度计算
                if (calcPerformanceModel.getGradientCalculation() != null && calcPerformanceModel.getGradientCalculation() == 1) {

                } else {
                    // 非梯度计算
                    List<String> conditions = new ArrayList<>();
                    List<String> expressions = new ArrayList<>();
                    String defaultExpression = "";
                    String calculateExpression = "";


                    int size = performanceRuleFormList.size();
                    for (int i = 0; i < size; i++) {
                        ThsPerformanceRuleForm performanceRuleForm = performanceRuleFormList.get(i);
                        // 判断是否处理了最后一个元素
                        if (i == size - 1) {
                            String exprBuilder = performanceRuleForm.getCalculationCondition() +
                                    " ? " +
                                    performanceRuleForm.getCalculationFormula() +
                                    " : " +
                                    "0";
                            defaultExpression = exprBuilder;
                        } else {
                            conditions.add(performanceRuleForm.getCalculationCondition());
                            expressions.add(performanceRuleForm.getCalculationFormula());
                        }
                    }
                    calculateExpression = CommonUtil.buildExpression(conditions, expressions, defaultExpression);
                    calcPerformanceModel.setCalculateExpression(calculateExpression);
                }
            }
        } else {
            Objects.requireNonNull(thsCalcPerformanceModelList, "计算规则未配置");
        }
    }

    /**
     * 根据计算表达式计算计量总业绩
     *
     * @param optionalThsCalcPerformanceModel
     * @param thsProjectCostDetailModel
     */
    public void calcTotalFeat(Optional<ThsCalcPerformanceModel> optionalThsCalcPerformanceModel, ThsProjectCostDetailModel thsProjectCostDetailModel) {
        try {
            List<Double> conditions = new ArrayList<>();
            List<Function<Double, Double>> algorithms = new ArrayList<>();
            // 调用函数来构建条件和算法列表
            this.buildConditionsAndAlgorithms(optionalThsCalcPerformanceModel,
                    thsProjectCostDetailModel,
                    conditions,
                    algorithms);
            double[] gradientConditions = conditions.stream().mapToDouble(Double::doubleValue).toArray();
            Function<Double, Double>[] gradientExpressions = algorithms.toArray(new Function[0]);

            double featTotalMoney = 0.0;

            if (ObjectUtil.isNotEmpty(thsProjectCostDetailModel.getFeatTotalMoney())) {
                featTotalMoney = thsProjectCostDetailModel.getFeatTotalMoney().doubleValue();
            }
            // 梯度累加
            try {
                if (optionalThsCalcPerformanceModel.get().getGradientCalculation() == 1) {
                    Object result = CalculateExpressionUtil.gradientRangeCalculator(gradientConditions,
                            gradientExpressions, featTotalMoney);

                    thsProjectCostDetailModel.setTotalFeat(BigDecimal.valueOf(Double.parseDouble(result.toString())));

                } else {
                    Object result = CalculateExpressionUtil.gradientSingleCalculator(gradientConditions,
                            gradientExpressions, featTotalMoney);

                    thsProjectCostDetailModel.setTotalFeat(BigDecimal.valueOf(Double.parseDouble(result.toString())));
                }
            } catch (RuntimeException e) {
                log.error("计算表达式计算计量总业绩" + e.getMessage(), e);
                throw new RuntimeException("计算表达式计算计量总业绩：" + e.getMessage());
            }
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(optionalThsCalcPerformanceModel.get().getName() + e.getMessage());
        }
    }

    /**
     * 项目经理摊分规则计算
     *
     * @param totalFeat
     * @param thsProjectCostDetailModel
     * @param thsUserFeatModelList
     */
    public void managerAllocateCalc(BigDecimal totalFeat, ThsProjectCostDetailModel thsProjectCostDetailModel, List<ThsUserFeatModel> thsUserFeatModelList) {
        Optional<ThsUserFeatModel> findUserFeatModel = thsUserFeatModelList.stream().filter(user -> thsProjectCostDetailModel.getManagerUserId().equals(user.getUserID())).findFirst();
        // 項目經理分攤數據
        ThsProjectCostDetailModel newProjectCostDetailModel = new ThsProjectCostDetailModel();
        BeanUtil.copyProperties(thsProjectCostDetailModel, newProjectCostDetailModel);
        newProjectCostDetailModel.setTotalFeat(totalFeat);
        newProjectCostDetailModel.setUserId(thsProjectCostDetailModel.getManagerUserId());
        newProjectCostDetailModel.setFlowType("项目经理");
        if (findUserFeatModel.isPresent()) {
            boolean exists = findUserFeatModel.get().getProjectCostDetailModelList().stream()
                    .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
            if (!exists) {
                findUserFeatModel.get().getProjectCostDetailModelList().add(newProjectCostDetailModel);
            }
        } else {
            ThsUserFeatModel newThsUserFeatModel = new ThsUserFeatModel();
            newThsUserFeatModel.setUserID(thsProjectCostDetailModel.getManagerUserId());
            if (ObjectUtil.isEmpty(newThsUserFeatModel.getProjectCostDetailModelList())) {
                List<ThsProjectCostDetailModel> newProjectCostDetailModelList = new ArrayList<>();
                newProjectCostDetailModelList.add(newProjectCostDetailModel);
                newThsUserFeatModel.setProjectCostDetailModelList(newProjectCostDetailModelList);
            } else {
                boolean exists = newThsUserFeatModel.getProjectCostDetailModelList().stream()
                        .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
                if (!exists) {
                    newThsUserFeatModel.getProjectCostDetailModelList().add(newProjectCostDetailModel);
                }
            }
            thsUserFeatModelList.add(newThsUserFeatModel);
        }

    }

    /**
     * 编制人摊分规则计算
     *
     * @param totalFeat
     * @param thsProjectCostDetailModel
     * @param thsUserFeatModelList
     */

    public void editorAllocateCalc(BigDecimal totalFeat, ThsProjectCostDetailModel thsProjectCostDetailModel, List<ThsUserFeatModel> thsUserFeatModelList) {
        // 编制人
        int userCount = thsProjectCostDetailModel.getBudgetProjectDetailUserList().size();

        List<ThsBusinessBudgetProjectDetailUserForm> budgetProjectDetailUser = thsProjectCostDetailModel.getBudgetProjectDetailUserList();

        for (ThsBusinessBudgetProjectDetailUserForm businessBudgetProjectDetailUserForm : budgetProjectDetailUser) {
            Optional<ThsUserFeatModel> findEditorUserFeatModel = thsUserFeatModelList.stream().filter(user -> businessBudgetProjectDetailUserForm.getUserId().equals(user.getUserID())).findFirst();

            ThsProjectCostDetailModel newProjectCostDetailModel = new ThsProjectCostDetailModel();
            BeanUtil.copyProperties(thsProjectCostDetailModel, newProjectCostDetailModel);
            newProjectCostDetailModel.setUserId(businessBudgetProjectDetailUserForm.getUserId());
            newProjectCostDetailModel.setFlowType("编制");
            // 编制人分摊金额，按编制人数量平均分摊
            if (userCount > 1) {
                newProjectCostDetailModel.setTotalFeat(totalFeat.divide(new BigDecimal(userCount)));
            } else {
                newProjectCostDetailModel.setTotalFeat(totalFeat);
            }

            if (findEditorUserFeatModel.isPresent()) {
                boolean exists = findEditorUserFeatModel.get().getProjectCostDetailModelList().stream()
                        .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
                if (!exists) {
                    findEditorUserFeatModel.get().getProjectCostDetailModelList().add(newProjectCostDetailModel);
                }
            } else {
                ThsUserFeatModel newThsUserFeatModel = new ThsUserFeatModel();
                newThsUserFeatModel.setUserID(businessBudgetProjectDetailUserForm.getUserId());
                if (ObjectUtil.isEmpty(newThsUserFeatModel.getProjectCostDetailModelList())) {
                    List<ThsProjectCostDetailModel> newProjectCostDetailModelList = new ArrayList<>();
                    newProjectCostDetailModelList.add(newProjectCostDetailModel);
                    newThsUserFeatModel.setProjectCostDetailModelList(newProjectCostDetailModelList);
                } else {
                    boolean exists = newThsUserFeatModel.getProjectCostDetailModelList().stream()
                            .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
                    if (!exists) {
                        newThsUserFeatModel.getProjectCostDetailModelList().add(newProjectCostDetailModel);
                    }
                }
                thsUserFeatModelList.add(newThsUserFeatModel);
            }
        }

    }

    /**
     * 审核人摊分计算
     *
     * @param totalFeat
     * @param thsPerformanceAllocationForm
     * @param thsProjectCostDetailModel
     * @param thsUserFeatModelList
     */
    public void auditorAllocateCalc(BigDecimal totalFeat, ThsPerformanceAllocationForm thsPerformanceAllocationForm, ThsProjectCostDetailModel thsProjectCostDetailModel, List<ThsUserFeatModel> thsUserFeatModelList) {
        if (ObjectUtil.isEmpty(thsProjectCostDetailModel.getBudgetProjectFlowDetailsList())) return;

        Optional<ThsBusinessBudgetProjectFlowDetailsForm> businessBudgetProjectFlowDetailsForm = thsProjectCostDetailModel.getBudgetProjectFlowDetailsList().stream()
                .filter(thsBusinessBudgetProjectFlowDetailsForm -> thsPerformanceAllocationForm.getRoleType() == thsBusinessBudgetProjectFlowDetailsForm.getRoleType()).findFirst();

        if (!businessBudgetProjectFlowDetailsForm.isPresent()) return;

        int userCount = thsProjectCostDetailModel.getBudgetProjectDetailUserList().size();

        List<ThsBusinessBudgetProjectFlowDetailsUserForm> ProjectFlowDetailsUser = businessBudgetProjectFlowDetailsForm.get().getBudgetProjectFlowDetailsUserList();

        for (ThsBusinessBudgetProjectFlowDetailsUserForm thsBusinessBudgetProjectFlowDetailsUserForm : ProjectFlowDetailsUser) {
            Optional<ThsUserFeatModel> findEditorUserFeatModel = thsUserFeatModelList.stream().filter(user -> thsBusinessBudgetProjectFlowDetailsUserForm.getUserId().equals(user.getUserID())).findFirst();

            ThsProjectCostDetailModel newProjectCostDetailModel = new ThsProjectCostDetailModel();
            BeanUtil.copyProperties(thsProjectCostDetailModel, newProjectCostDetailModel);
            newProjectCostDetailModel.setUserId(thsBusinessBudgetProjectFlowDetailsUserForm.getUserId());
            newProjectCostDetailModel.setFlowType(businessBudgetProjectFlowDetailsForm.get().getName());
            // 审核人分摊金额，按审核人数量平均分摊
            if (userCount > 1) {
                newProjectCostDetailModel.setTotalFeat(totalFeat.divide(new BigDecimal(userCount)));
            } else {
                newProjectCostDetailModel.setTotalFeat(totalFeat);
            }

            if (findEditorUserFeatModel.isPresent()) {
                boolean exists = findEditorUserFeatModel.get().getProjectCostDetailModelList().stream()
                        .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
                if (!exists) {
                    findEditorUserFeatModel.get().getProjectCostDetailModelList().add(newProjectCostDetailModel);
                }

            } else {
                ThsUserFeatModel newThsUserFeatModel = new ThsUserFeatModel();
                newThsUserFeatModel.setUserID(thsBusinessBudgetProjectFlowDetailsUserForm.getUserId());

                if (ObjectUtil.isEmpty(newThsUserFeatModel.getProjectCostDetailModelList())) {
                    List<ThsProjectCostDetailModel> newProjectCostDetailModelList = new ArrayList<>();
                    newProjectCostDetailModelList.add(newProjectCostDetailModel);
                    newThsUserFeatModel.setProjectCostDetailModelList(newProjectCostDetailModelList);
                } else {
                    boolean exists = newThsUserFeatModel.getProjectCostDetailModelList().stream()
                            .anyMatch(detail -> detail.getId().equals(newProjectCostDetailModel.getId()));
                    if (!exists) {
                        newThsUserFeatModel.getProjectCostDetailModelList().add(newProjectCostDetailModel);
                    }
                }
                thsUserFeatModelList.add(newThsUserFeatModel);
            }
        }

    }

    /**
     * 绩效计算
     *
     * @param list
     * @return
     */
    public List<ThsUserFeatModel> calcProjectCostFeat(List<ThsProjectCostDetailModel> list) {
        List<ThsUserFeatModel> userFeatModelList = new ArrayList<>();
        List<UserEntity> userList = userService.getList(false);

        List<ThsCalcPerformanceModel> performanceModelList = thsPerformanceConfigApi.getCalcPerformanceModelList();
        // 表达式初始化
//        this.initializeFormula(performanceModelList);

        for (ThsProjectCostDetailModel projectCostDetailModel : list) {

            Optional<ThsCalcPerformanceModel> optionalThsCalcPerformanceModel;

            if (projectCostDetailModel.getBusinessTypeName() != null && projectCostDetailModel.getBusinessTypeName().equals("研究分析类业务")) {
                // 如果是研究分析类，则使用研究分析类的绩效计算规则
                optionalThsCalcPerformanceModel = performanceModelList.stream()
                        .filter(thsCalcPerformanceModel -> thsCalcPerformanceModel.getName().equals("研究分析类")).findFirst();
            } else {
                // 其他情况按原逻辑，通过consultingTypeId匹配
                optionalThsCalcPerformanceModel = performanceModelList.stream().filter(thsCalcPerformanceModel -> !thsCalcPerformanceModel.getName().equals("研究分析类"))
                        .filter(thsCalcPerformanceModel -> thsCalcPerformanceModel.getConsultingTypeId().equals(projectCostDetailModel.getConsultingTypeID())).findFirst();
            }

            if (!optionalThsCalcPerformanceModel.isPresent()) continue;

            // 计算计量总业绩
            this.calcTotalFeat(optionalThsCalcPerformanceModel, projectCostDetailModel);

            try {
                // 摊分规则
                List<ThsPerformanceAllocationForm> performanceAllocationList = optionalThsCalcPerformanceModel.get().getPerformanceAllocationList();

                if ((ObjectUtil.isNotEmpty(performanceAllocationList) && performanceAllocationList.size() <= 0) || (ObjectUtil.isEmpty(performanceAllocationList))) {
                    throw new RuntimeException(optionalThsCalcPerformanceModel.get().getName() + "未配置绩效计算人员摊分规则");
                }

                BigDecimal totalFeat = projectCostDetailModel.getTotalFeat();

                for (ThsPerformanceAllocationForm performanceAllocationForm : performanceAllocationList) {
                    // 根据人员不同分工，绩效摊分
                    projectCostDetailModel.setAllocationRatio(performanceAllocationForm.getAllocationRatio());
                    // 项目经理
                    if (Objects.equals(performanceAllocationForm.getRoleType(), BusinessConstantValues.THS_PERFORMANCE_ALLOCATION_ROLE_TYPE_MANAGER)) {
                        this.managerAllocateCalc(totalFeat, projectCostDetailModel, userFeatModelList);
                    } else if (Objects.equals(performanceAllocationForm.getRoleType(), BusinessConstantValues.THS_PERFORMANCE_ALLOCATION_ROLE_TYPE_EDITOR)) {
                        // 编制人
                        this.editorAllocateCalc(totalFeat, projectCostDetailModel, userFeatModelList);
                    } else {
                        // 审核人
                        this.auditorAllocateCalc(totalFeat, performanceAllocationForm, projectCostDetailModel, userFeatModelList);
                    }

                }
            } catch (RuntimeException e) {
                log.error("绩效人员分摊计算错误" + e.getMessage(), e);
                throw new RuntimeException("绩效人员分摊计算错误" + e.getMessage());
            }
        }

        // 填充用户基本信息（用户名、部门、岗位等）
        this.fillUserBasicInfo(userFeatModelList, userList);

        //  组装树
        userFeatModelList.forEach(featModel -> {
            List<ThsProjectCostDetailModel> projectCostDetailList = featModel.getProjectCostDetailModelList();
            if (ObjectUtil.isNotEmpty(projectCostDetailList)) {
                // 将ThsProjectCostDetailModel转换为Map格式
                List<Map<String, Object>> mapList = new ArrayList<>();
                //  业绩汇总
                BigDecimal totalAllocationFeat = new BigDecimal(0);
                BigDecimal totalProjectManager = new BigDecimal(0);
                for (ThsProjectCostDetailModel detailModel : projectCostDetailList) {
                    Map<String, Object> detailModelMap = BeanUtil.beanToMap(detailModel);
                    detailModelMap.put("userName", userList.stream()
                            .filter(userEntity -> userEntity != null && userEntity.getId() != null && userEntity.getId().equals(detailModel.getUserId()))
                            .findFirst().map(UserEntity::getRealName).orElse(null));
                    mapList.add(detailModelMap);
                    if (detailModel.getItemType().equals(1) && ObjectUtil.isNotEmpty(detailModel.getFlowType()) && detailModel.getFlowType().equals("编制")) {
                        totalAllocationFeat = totalAllocationFeat.add(detailModel.getAllocationFeat());
                    }
                    if (detailModel.getItemType().equals(1) && ObjectUtil.isNotEmpty(detailModel.getFlowType()) && detailModel.getFlowType().equals("项目经理")) {
                        totalProjectManager = totalProjectManager.add(detailModel.getAllocationFeat());
                    }
                }
                featModel.setCostEditor(totalAllocationFeat.setScale(2, RoundingMode.HALF_UP));
                featModel.setProjectManager(totalProjectManager.setScale(2, RoundingMode.HALF_UP));
                featModel.setTotal(totalAllocationFeat.add(totalProjectManager).setScale(2, RoundingMode.HALF_UP));
                // 使用TreeUtils.getEasyTrees构建树结构
                List treeList = TreeUtils.getEasyTreesMap(mapList, "id", "pid", "name");

                featModel.setProjectCostDetailModelList(treeList);
            }
        });

        return userFeatModelList;
    }

    /**
     * 填充用户基本信息（用户名、部门、岗位等）
     *
     * @param userFeatModelList 用户绩效列表
     * @param userList          用户列表
     * <AUTHOR>
     * @date 2025/7/31 14:35
     */
    private void fillUserBasicInfo(List<ThsUserFeatModel> userFeatModelList, List<UserEntity> userList) {
        if (ObjectUtil.isEmpty(userFeatModelList)) {
            return;
        }

        // 获取部门和岗位数据
        List<OrganizeEntity> organizeEntityList = organizeApi.getList(false);
        List<PositionEntity> positionEntityList = positionService.getList(false);

        // 转换为Map提高查找效率
        Map<String, UserEntity> userMap = userList.stream()
                .collect(Collectors.toMap(UserEntity::getId, Function.identity(), (existing, replacement) -> existing));
        Map<String, OrganizeEntity> organizeMap = organizeEntityList.stream()
                .collect(Collectors.toMap(OrganizeEntity::getId, Function.identity(), (existing, replacement) -> existing));
        Map<String, PositionEntity> positionMap = positionEntityList.stream()
                .collect(Collectors.toMap(PositionEntity::getId, Function.identity(), (existing, replacement) -> existing));

        // 填充用户信息
        for (ThsUserFeatModel userFeatModel : userFeatModelList) {
            // 填充用户基本信息
            UserEntity user = userMap.get(userFeatModel.getUserID());
            if (user != null) {
                userFeatModel.setUserName(user.getRealName());
                userFeatModel.setDeptId(user.getOrganizeId());
                userFeatModel.setPositionId(user.getPositionId());
                userFeatModel.setEntryDate(user.getEntryDate());
            }

            // 填充部门信息
            if (userFeatModel.getDeptId() != null) {
                OrganizeEntity organize = organizeMap.get(userFeatModel.getDeptId());
                if (organize != null) {
                    userFeatModel.setDeptName(organize.getFullName());
                }
            }

            // 填充岗位信息
            if (userFeatModel.getPositionId() != null) {
                PositionEntity position = positionMap.get(userFeatModel.getPositionId());
                if (position != null) {
                    userFeatModel.setPositionName(position.getFullName());
                }
            }
        }
    }

    /**
     * 获取绩效数据用户
     *
     * @param thsUserFeatPagination
     * @return
     */
    public List<ThsUserFeatModel> getUserFeatList(ThsUserFeatPagination thsUserFeatPagination) {
        try {
            // 项目的计量工程量的计算
            List<ThsProjectCostDetailModel> thsProjectCostDetailModels = getProjectCostDetailList(thsUserFeatPagination);
            // 绩效分摊计算
            if (ObjectUtil.isNotEmpty(thsProjectCostDetailModels)) {
                return this.calcProjectCostFeat(thsProjectCostDetailModels);
            } else {
                return Collections.emptyList();
            }
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * @param conditions
     * @param algorithms
     * @description: 构建条件和算法列表的函数
     * @param: @param performanceRuleEntityList
     * @return: void
     * @author: wxy
     * @date: 2024/7/18 15:02
     */
    @Override
    public void buildConditionsAndAlgorithms(Optional<ThsCalcPerformanceModel> optionalThsCalcPerformanceModel,
                                             ThsProjectCostDetailModel thsProjectCostDetailModel,
                                             List<Double> conditions,
                                             List<Function<Double, Double>> algorithms) {
        List<ThsPerformanceRuleForm> performanceRuleFormList = optionalThsCalcPerformanceModel.get().getPerformanceRuleList();
        if ((ObjectUtil.isNotEmpty(performanceRuleFormList) && performanceRuleFormList.size() <= 0) || (ObjectUtil.isEmpty(performanceRuleFormList))) {
            throw new RuntimeException("未配置绩效计算规则");
        }
        int size = performanceRuleFormList.size();
        for (int i = 0; i < size; i++) {
            ThsPerformanceRuleForm performanceRuleForm = performanceRuleFormList.get(i);
            Function<Map<String, Double>, Double> func = CalculateExpressionUtil.createFunction(performanceRuleForm.getCalculationFormula());
            List<String> foundKeywords = CalculateExpressionUtil.extractKeywords(performanceRuleForm.getCalculationFormula());
            // 判断是否处理了最后一个元素
            // 判断是否处理了最后一个元素
            if (size == 1) {
                // 特殊情况：只有一个范围
                conditions.add(Double.MAX_VALUE); // 使用一个非常大的值来表示无限大
                algorithms.add(v -> {
                    Map<String, Double> variables = new HashMap<>();
                    foundKeywords.forEach(s -> variables.put(s, v));
                    return func.apply(variables);
                });
            } else {
                if (i == 0) {
                    // 添加空值判断
                    if (performanceRuleForm.getMaximumValue() != null) {
                        conditions.add(performanceRuleForm.getMaximumValue().doubleValue());
                    } else {
                        conditions.add(Double.MAX_VALUE); // 空则认为是无上限
                    }
                    algorithms.add(value1 -> {
                        Map<String, Double> variables = new HashMap<>();
                        foundKeywords.forEach(s -> {
                            // 使用传入的参数value1作为变量值，而不是从model中获取
                            variables.put(s, value1);
                        });
                        return func.apply(variables);
                    });

                } else {
                    ThsPerformanceRuleForm previousPerformanceRuleForm = performanceRuleFormList.get(i - 1);
                    //  如果前一个最大值不等于当前的最小值，则这段不参与计算
                    if (performanceRuleForm.getMinimumValue() != null && previousPerformanceRuleForm.getMaximumValue() != null
                            && performanceRuleForm.getMinimumValue().compareTo(previousPerformanceRuleForm.getMaximumValue()) != 0) {
                        conditions.add(performanceRuleForm.getMinimumValue().doubleValue());
//                        algorithms.add(value1 -> func.apply(Collections.singletonMap("FeatTotalMoney", 0.0)));
                        algorithms.add(value1 -> {
                            Map<String, Double> variables = new HashMap<>();
                            foundKeywords.forEach(s -> {
                                // 对于间隙部分，使用0值
                                variables.put(s, 0.00);
                            });
                            return func.apply(variables);
                        });
                    }
                    if (i == size - 1) {
                        // 如果是最后一个,只加计算表达式
//                        algorithms.add(value1 -> func.apply(Collections.singletonMap("FeatTotalMoney", value)));
                        algorithms.add(value1 -> {
                            Map<String, Double> variables = new HashMap<>();
                            foundKeywords.forEach(s -> {
                                // 使用传入的参数value1作为变量值
                                variables.put(s, value1);
                            });
                            return func.apply(variables);
                        });
                    } else {

                        if (performanceRuleForm.getMaximumValue() != null) {
                            conditions.add(performanceRuleForm.getMaximumValue().doubleValue());
                        } else {
                            conditions.add(Double.MAX_VALUE); // 空最大值默认无限大
                        }
                        algorithms.add(value1 -> {
                            Map<String, Double> variables = new HashMap<>();
                            foundKeywords.forEach(s -> {
                                // 使用传入的参数value1作为变量值
                                variables.put(s, value1);
                            });
                            return func.apply(variables);
                        });
                    }
                }
            }
        }
    }

    /**
     * 导出Excel
     *
     * @param thsUserFeatPagination
     * @return
     * <AUTHOR>
     * @date 2025/4/2 11:17
     */
    @Override
    public void exportExcel(ThsUserFeatPagination thsUserFeatPagination) throws Exception {
        try {
            List<Map<String, Object>> realList = new ArrayList<>();
            //  获取绩效数据用户
            List<ThsUserFeatModel> userFeatList = getUserFeatList(thsUserFeatPagination);

            if (ObjectUtil.isNotEmpty(userFeatList)) {
                // 人员列表
                List<UserEntity> userEntityList = this.userService.getList(false);
                // 部门列表
                List<OrganizeEntity> organizeEntityList = organizeApi.getList(false);
                // 岗位列表
                List<PositionEntity> positionEntityList = positionService.getList(false);

                int i = 0;
                for (ThsUserFeatModel userFeatModel : userFeatList) {
                    // 人员名称
                    Optional<UserEntity> user = userEntityList.stream().filter(userEntity -> userEntity.getId().equals(userFeatModel.getUserID())).findFirst();
                    if (ObjectUtil.isNotEmpty(user) && user.isPresent()) {
                        userFeatModel.setUserName(user.get().getRealName());
                        userFeatModel.setDeptId(user.get().getOrganizeId());
                        userFeatModel.setPositionId(user.get().getPositionId());
                        // 入职时间
                        userFeatModel.setEntryDate(user.get().getEntryDate());
                    }
                    // 部门名称
                    Optional<OrganizeEntity> organize = organizeEntityList.stream().filter(organizeEntity -> organizeEntity.getId().equals(userFeatModel.getDeptId())).findFirst();
                    if (ObjectUtil.isNotEmpty(organize) && organize.isPresent()) {
                        userFeatModel.setDeptName(organize.get().getFullName());
                    }
                    // 岗位名称
                    Optional<PositionEntity> optionalPosition = positionEntityList.stream().filter(positionEntity -> positionEntity.getId().equals(userFeatModel.getPositionId())).findFirst();
                    if (ObjectUtil.isNotEmpty(optionalPosition) && optionalPosition.isPresent()) {
                        userFeatModel.setPositionName(optionalPosition.get().getFullName());
                    }
                    //  转MAP
                    i++;
                    Map<String, Object> userFeatModelMap = JsonUtil.entityToMap(userFeatModel);
                    userFeatModelMap.put("index", i);
                    realList.add(userFeatModelMap);
                }
            }

            InputStream fis = this.getClass().getClassLoader().getResourceAsStream("templates/excel/个人业务业绩统计导出模板.xlsx");
            assert fis != null;

            HttpServletResponse response = ServletUtil.getResponse();

            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/plain");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncodeUtil.encode("个人业务业绩统计 - " + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx", GlobalConst.DEFAULT_CHARSET));

            // 根据consultingTypeId 查询对应的咨询类型名称
            List<Map<String, Object>> consultingTypeList = thsSysDictDetailsService.getListTree(ManagementConstantValues.CONTRACT_CONSULTING_TYPE_CODE, false);

            // 根据editorialTypeId 查询对应的编审类型名称
            List<Map<String, Object>> editorialTypeList = thsSysDictDetailsService.getListTree(ManagementConstantValues.CONTRACT_EDITORIAL_TYPE_CODE, false);

            //  处理数据下标 - 扁平化树结构数据，循环所有用户的项目数据
            List<Map<String, Object>> resultMap = new ArrayList<>();
            for (Map<String, Object> userFeatMap : realList) {
                Object projectCostDetailModelList = userFeatMap.get("projectCostDetailModelList");
                if (projectCostDetailModelList instanceof List) {
                    List<Map<String, Object>> flattenedData = flattenTreeData((List<Map<String, Object>>) projectCostDetailModelList);
                    resultMap.addAll(flattenedData);
                }
            }

            AtomicInteger i = new AtomicInteger();
            resultMap.forEach(info -> {
                i.getAndIncrement();
                info.put("index", i.get());

                //  赋值咨询类型
                for (Map<String, Object> consultingTypeMap : consultingTypeList) {
                    if (MapUtil.getStr(consultingTypeMap, "id").equals(MapUtil.getStr(info, "consultingTypeID"))) {
                        info.put("consultingTypeName", consultingTypeMap.get("itemText"));
                    }
                }
                // 赋值编审类型
                for (Map<String, Object> editorialTypeMap : editorialTypeList) {
                    if (MapUtil.getStr(editorialTypeMap, "id").equals(MapUtil.getStr(info, "editorialTypeId"))) {
                        info.put("editorialTypeName", editorialTypeMap.get("itemText"));
                    }
                }
                //  工程类型
                switch (MapUtil.getInt(info, "itemType")) {
                    case 1:
                        info.put("itemTypeName", "建设项目");
                        break;
                    case 2:
                        info.put("itemTypeName", "单项工程");
                        break;
                    case 3:
                        info.put("itemTypeName", "单位工程");
                        break;
                }
            });

            //  填充数据
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(fis).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                // 自动向下填充
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                excelWriter.fill(realList, fillConfig, writeSheet);
                excelWriter.fill(new FillWrapper("data1", realList), fillConfig, writeSheet);
                excelWriter.fill(new FillWrapper("data2", resultMap), fillConfig, writeSheet);
                excelWriter.finish();
            }
            fis.close();
        } catch (Exception e) {
            log.error("导出个人业务业绩统计异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 扁平化树结构数据，递归获取所有子级数据
     *
     * @param treeData 树结构数据
     * @return 扁平化后的列表
     */
    private List<Map<String, Object>> flattenTreeData(List<Map<String, Object>> treeData) {
        List<Map<String, Object>> flatList = new ArrayList<>();

        if (treeData == null || treeData.isEmpty()) {
            return flatList;
        }

        for (Map<String, Object> item : treeData) {
            if (item == null) {
                continue;
            }

            // 创建当前节点的副本，移除children字段避免循环引用
            Map<String, Object> flatItem = new HashMap<>(item);
            flatItem.remove("children");
            flatList.add(flatItem);

            // 递归处理子级数据
            Object children = item.get("children");
            if (children instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> childrenList = (List<Map<String, Object>>) children;
                List<Map<String, Object>> flatChildren = flattenTreeData(childrenList);
                flatList.addAll(flatChildren);
            }
        }

        return flatList;
    }
}

