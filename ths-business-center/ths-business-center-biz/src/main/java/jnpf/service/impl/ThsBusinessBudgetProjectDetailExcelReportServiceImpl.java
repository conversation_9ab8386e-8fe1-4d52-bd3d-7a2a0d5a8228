package jnpf.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xuyanwu.spring.file.storage.FileInfo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.base.model.ColumnDataModel;
import jnpf.database.util.DynamicDataSourceUtil;
import jnpf.entity.*;
import jnpf.errors.BadRequestAlertException;
import jnpf.exception.DataException;
import jnpf.mapper.ThsBusinessBudgetProjectDetailExcelReportMapper;
import jnpf.model.QueryAllModel;
import jnpf.model.thsbusinessbudgetprojectdetailexcelreport.ThsBusinessBudgetProjectDetailExcelReportConstant;
import jnpf.model.thsbusinessbudgetprojectdetailexcelreport.ThsBusinessBudgetProjectDetailExcelReportForm;
import jnpf.model.thsbusinessbudgetprojectdetailexcelreport.ThsBusinessBudgetProjectDetailExcelReportPagination;
import jnpf.service.*;
import jnpf.util.*;
import jnpf.util.DateUtil;
import jnpf.util.pdf.ImagesVO;
import jnpf.util.pdf.PdfPageOfEnd;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static jnpf.util.BusinessConstantValues.*;


/**
 * thsBusinessBudgetProjectDetailExcelReport
 * 版本： V3.5
 * 版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * 作者： JNPF开发平台组
 * 日期： 2024-03-26
 */
@Slf4j
@Service
public class ThsBusinessBudgetProjectDetailExcelReportServiceImpl extends ServiceImpl<ThsBusinessBudgetProjectDetailExcelReportMapper, ThsBusinessBudgetProjectDetailExcelReportEntity> implements ThsBusinessBudgetProjectDetailExcelReportService {

    /**
     * 标准签名图片宽度（像素）- 用于拼接前的单个签名
     */
    private static final int STANDARD_SIGNATURE_WIDTH = 200;

    /**
     * 标准签名图片高度（像素）- 用于拼接前的单个签名
     */
    private static final int STANDARD_SIGNATURE_HEIGHT = 100;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private ThsDocumectFileManagementService thsDocumectFileManagementService;

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectDetailService thsBusinessBudgetProjectDetailService;

    @Autowired
    private ThsDataReportDetailsService thsDataReportDetailsService;

    @Autowired
    private ThsBusinessBudgetProjectDetailExcelReportDetailsService thsBusinessBudgetProjectDetailExcelReportDetailsService;


    @Autowired
    private Environment environment;

    @Autowired
    private ThsBusinessBudgetProjectDetailReportSerialNumberService thsBusinessBudgetProjectDetailReportSerialNumberService;

    @Resource
    @Lazy
    private ThsTemplateReportCategoryFileService thsTemplateReportCategoryFileService;

    @Resource
    @Lazy
    private ThsFileUtil thsFileUtil;


    @Resource
    @Lazy
    private ThsTemplateReportCategoryFileSourceService thsTemplateReportCategoryFileSourceService;

    @Resource
    @Lazy
    private ExecuteSqlUtils executeSqlUtils;


    @Resource
    @Lazy
    private ThsReportSysSettingService thsReportSysSettingService;

    @Resource
    @Lazy
    private ThsBusinessProjectDetailUtil thsBusinessProjectDetailUtil;

    @Autowired
    @Lazy
    private ThsBusinessBudgetProjectVersionService thsBusinessBudgetProjectVersionService;

    @Autowired
    @Lazy
    private ThsBusinessConsultProjectHandlingOperatorService thsBusinessConsultProjectHandlingOperatorService;

    public String BUDGET_FILES_ACHIEVEMENT_ROOT_PATH;
    public String LOGO_IMAGE = "data:image/png;base64,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";
    public String WatermarkImagePath_IMAGE = "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";

    @PostConstruct
    public void init() {
        BUDGET_FILES_ACHIEVEMENT_ROOT_PATH = environment.getProperty("config.file-storage.local-plus[0].base-path"); // 归档文件上传路径
//        BUDGET_FILES_ACHIEVEMENT_ROOT_PATH = "E:\\home\\app\\resources\\";
    }

    @Override
    public List<ThsBusinessBudgetProjectDetailExcelReportEntity> getList(ThsBusinessBudgetProjectDetailExcelReportPagination thsBusinessBudgetProjectDetailExcelReportPagination) {
        return getTypeList(thsBusinessBudgetProjectDetailExcelReportPagination, thsBusinessBudgetProjectDetailExcelReportPagination.getDataType());
    }

    /**
     * 列表查询
     */
    @Override
    public List<ThsBusinessBudgetProjectDetailExcelReportEntity> getTypeList(ThsBusinessBudgetProjectDetailExcelReportPagination thsBusinessBudgetProjectDetailExcelReportPagination, String dataType) {
        String userId = userProvider.get().getUserId();
        Map<String, Class> tableClassMap = new HashMap<>();
        tableClassMap.put("ths_business_budget_project_detail_excel_report", ThsBusinessBudgetProjectDetailExcelReportEntity.class);

        MPJLambdaWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> wrapper = JoinWrappers
                .lambda("ths_business_budget_project_detail_excel_report", ThsBusinessBudgetProjectDetailExcelReportEntity.class)
                .selectAll(ThsBusinessBudgetProjectDetailExcelReportEntity.class);
        MPJLambdaWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> wrapper2 = JoinWrappers
                .lambda("ths_business_budget_project_detail_excel_report", ThsBusinessBudgetProjectDetailExcelReportEntity.class)
                .distinct().select(ThsBusinessBudgetProjectDetailExcelReportEntity::getId);

        QueryAllModel queryAllModel = new QueryAllModel();
        queryAllModel.setWrapper(wrapper);
        queryAllModel.setClassMap(tableClassMap);
        queryAllModel.setDbLink(ThsBusinessBudgetProjectDetailExcelReportConstant.DBLINKID);
        // 数据过滤
        boolean isPc = ServletUtil.getHeader("jnpf-origin").equals("pc");
        String columnData = !isPc ? ThsBusinessBudgetProjectDetailExcelReportConstant.getAppColumnData() : ThsBusinessBudgetProjectDetailExcelReportConstant.getColumnData();
        ColumnDataModel columnDataModel = JsonUtil.getJsonToBean(columnData, ColumnDataModel.class);
        String ruleJson = !isPc ? JsonUtil.getObjectToString(columnDataModel.getRuleListApp()) : JsonUtil.getObjectToString(columnDataModel.getRuleList());
        if (ObjectUtil.isNotEmpty(ruleJson) && !ruleJson.equals("null")) {
            queryAllModel.setRuleJson(ruleJson);
        }
        // 高级查询
        boolean hasSuperQuery = true;
        if (hasSuperQuery) {
            queryAllModel.setSuperJson(thsBusinessBudgetProjectDetailExcelReportPagination.getSuperQueryJson());
        }
        // 数据权限
        boolean pcPermission = false;
        boolean appPermission = false;
        if (isPc && pcPermission && !userProvider.get().getIsAdministrator()) {
            queryAllModel.setModuleId(thsBusinessBudgetProjectDetailExcelReportPagination.getMenuId());
            queryAllModel.setModuleId(thsBusinessBudgetProjectDetailExcelReportPagination.getMenuId());
        }
        if (!isPc && appPermission && !userProvider.get().getIsAdministrator()) {
            queryAllModel.setModuleId(thsBusinessBudgetProjectDetailExcelReportPagination.getMenuId());
        }
        // 拼接复杂条件
        wrapper = generaterSwapUtil.getConditionAllTable(queryAllModel);
        queryAllModel.setWrapper(wrapper2);
        wrapper2 = generaterSwapUtil.getConditionAllTable(queryAllModel);
        // 其他条件拼接
        otherConditions(thsBusinessBudgetProjectDetailExcelReportPagination, wrapper, isPc);
        otherConditions(thsBusinessBudgetProjectDetailExcelReportPagination, wrapper2, isPc);

        if ("0".equals(dataType)) {
            com.github.pagehelper.Page<Object> objects = PageHelper.startPage((int) thsBusinessBudgetProjectDetailExcelReportPagination.getCurrentPage(), (int) thsBusinessBudgetProjectDetailExcelReportPagination.getPageSize(), true);
            List<ThsBusinessBudgetProjectDetailExcelReportEntity> userIPage = this.selectJoinList(ThsBusinessBudgetProjectDetailExcelReportEntity.class, wrapper2);
            List<Object> collect = userIPage.stream().map(t -> t.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                wrapper.in(ThsBusinessBudgetProjectDetailExcelReportEntity::getId, collect);
            }
            List<ThsBusinessBudgetProjectDetailExcelReportEntity> result = this.selectJoinList(ThsBusinessBudgetProjectDetailExcelReportEntity.class, wrapper);
            return thsBusinessBudgetProjectDetailExcelReportPagination.setData(result, objects.getTotal());
        } else {
            List<ThsBusinessBudgetProjectDetailExcelReportEntity> list = this.selectJoinList(ThsBusinessBudgetProjectDetailExcelReportEntity.class, wrapper);
            if ("2".equals(dataType)) {
                List<String> selectIds = Arrays.asList(thsBusinessBudgetProjectDetailExcelReportPagination.getSelectIds());
                return list.stream().filter(t -> selectIds.contains(t.getId())).collect(Collectors.toList());
            } else {
                return list;
            }
        }

    }

    /**
     * 其他条件拼接
     */
    private void otherConditions(ThsBusinessBudgetProjectDetailExcelReportPagination thsBusinessBudgetProjectDetailExcelReportPagination, MPJLambdaWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> wrapper, boolean isPc) {
        String databaseName;
        try {
            @Cleanup Connection cnn = DynamicDataSourceUtil.getCurrentConnection();
            databaseName = cnn.getMetaData().getDatabaseProductName().trim();
        } catch (SQLException e) {
            throw new DataException(e.getMessage());
        }
        // 关键词
        if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectDetailExcelReportPagination.getJnpfKeyword())) {
        }
        // 普通查询
        if (isPc) {
            if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectDetailExcelReportPagination.getDetailId())) {
                String value = thsBusinessBudgetProjectDetailExcelReportPagination.getDetailId() instanceof List ?
                        JsonUtil.getObjectToString(thsBusinessBudgetProjectDetailExcelReportPagination.getDetailId()) :
                        String.valueOf(thsBusinessBudgetProjectDetailExcelReportPagination.getDetailId());
                wrapper.like(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, value);
            }

        }
        // 排序
        if (StringUtil.isEmpty(thsBusinessBudgetProjectDetailExcelReportPagination.getSidx())) {
            wrapper.orderByDesc(ThsBusinessBudgetProjectDetailExcelReportEntity::getId);
        } else {
            try {
                String[] split = thsBusinessBudgetProjectDetailExcelReportPagination.getSidx().split(",");
                for (String sidx : split) {
                    ThsBusinessBudgetProjectDetailExcelReportEntity thsBusinessBudgetProjectDetailExcelReportEntity = new ThsBusinessBudgetProjectDetailExcelReportEntity();
                    String oderTableField = thsBusinessBudgetProjectDetailExcelReportEntity.getClass().getAnnotation(TableName.class).value();
                    if (sidx.startsWith("-")) {
                        Field declaredField = thsBusinessBudgetProjectDetailExcelReportEntity.getClass().getDeclaredField(sidx.substring(1));
                        declaredField.setAccessible(true);
                        oderTableField = oderTableField + "." + declaredField.getAnnotation(TableField.class).value();
                        wrapper.select(oderTableField);
                        wrapper.orderByDesc(oderTableField);
                    } else {
                        Field declaredField = thsBusinessBudgetProjectDetailExcelReportEntity.getClass().getDeclaredField(sidx);
                        declaredField.setAccessible(true);
                        oderTableField = oderTableField + "." + declaredField.getAnnotation(TableField.class).value();
                        wrapper.select(oderTableField);
                        wrapper.orderByAsc(oderTableField);
                    }
                }
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public ThsBusinessBudgetProjectDetailExcelReportEntity getInfo(String id) {
        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(ThsBusinessBudgetProjectDetailExcelReportEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, ThsBusinessBudgetProjectDetailExcelReportEntity entity) {
        return this.updateById(entity);
    }

    @Override
    public void delete(ThsBusinessBudgetProjectDetailExcelReportEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }

    /**
     * 验证表单唯一字段，正则，非空 i-0新增-1修改
     */
    @Override
    public String checkForm(ThsBusinessBudgetProjectDetailExcelReportForm form, int i) {
        boolean isUp = StringUtil.isNotEmpty(form.getId()) && !form.getId().equals("0");
        String id = "";
        String countRecover = "";
        if (isUp) {
            id = form.getId();
        }
        // 主表字段验证
        return countRecover;
    }

    @Override
    public Integer getMaxSequence() {
        if (this.getBaseMapper().selectMaxSequence() == null) {
            return 0;
        }
        return this.getBaseMapper().selectMaxSequence();
    }

    @Override
    public String uploadNoEditFieldExcelReport(String fileName, String folderName, String reportId) throws Exception {
        InputStream fis = this.getClass().getClassLoader().getResourceAsStream("templates/excel/一表通/" + fileName + ".xlsx");
        String fileNameFormat = reportId + fileName + ".xlsx";
        String mainId = uploadFile(fileNameFormat, folderName, IoUtil.readBytes(fis), reportId + fileName, "xlsx");
        assert fis != null;
        fis.close();
        return mainId;
    }

    /**
     * @param name
     * @param reportId
     * @param fileType
     * @param isPreview 是否为打印预览。打印预览不生成流水号
     * @param response
     * @Description: 下载文件
     * @return: void
     * @Author: hrj
     * @Date: 13:54 2024/7/9
     */
    @Override
    public void downloadReport(String name, String reportId, String detailId, String budgetProjectId, String fileType, Boolean isPreview, HttpServletResponse response) throws Exception {
        // 获取报表信息
        ThsBusinessBudgetProjectDetailExcelReportEntity info = this.getInfo(reportId);
        if (info == null) {
            throw new Exception("报表不存在");
        }

        // 获取文件管理实体
        ThsDocumectFileManagementEntity documentFileManagementEntity;
        String tempPdfFilePath;
        String tempPdfFilePath2 = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + UUID.randomUUID() + ".pdf";
        if ("pdf".equals(fileType)) {
            // 1、未归档报表预览
            // if (ObjectUtil.isEmpty(info.getIsArchived()) || info.getIsArchived() != 1) {
            // 1.1 加载业务数据 存为临时归档pdf文件
            String pdfPath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + info.getDetailId() + "/" + UUID.randomUUID() + ".pdf";

            String tempExcelFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + info.getDetailId() + "/temp.xlsx";

            tempPdfFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + UUID.randomUUID() + ".pdf";

            // 加载业务数据填充到报表, 存为临时excel文件
            previewBeforeArchiving(reportId, detailId, budgetProjectId, tempExcelFilePath);
            // 将excel转为pdf
            if (info.getName().equals("项目归档统计表")) {
                // 横向分四页打印
                AsposeToPdfUtil.trans(tempExcelFilePath, pdfPath, "excel");
            } else {
                //  汇总表和编制说明采用自动行高
                if (Arrays.asList("造价汇总表", "编制说明").contains(info.getName())) {
                    ExcelToPdfUtil.excelToPdfAutoHeightAndWidth(tempExcelFilePath, pdfPath);
                } else {
                    ExcelToPdfUtil.excelToPdf(tempExcelFilePath, pdfPath);
                }
            }

            // 添加页眉页脚
            tempPdfFilePath = pdfFileAddHeaderFooter(info, pdfPath, tempPdfFilePath, tempPdfFilePath2, reportId, isPreview);

            FileUtil.del(pdfPath);

            // } else {
            //     // 2、归档报表预览
            //     documentFileManagementEntity = thsDocumectFileManagementService.getInfo(ObjectUtil.isEmpty(info.getArchivedFileId()) ? "" : info.getArchivedFileId());
            //     if (documentFileManagementEntity == null) {
            //         throw new Exception("PDF文件不存在");
            //     }
            //
            //     String inputPath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagementEntity.getFileSavePath() + documentFileManagementEntity.getFileName();
            //     tempPdfFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagementEntity.getFileSavePath() + UUID.randomUUID() + ".pdf";
            //
            //     // 归档以后下载文件 添加页眉页脚，logo图片，流水号
            //     tempPdfFilePath = pdfFileAddHeaderFooter(info, inputPath, tempPdfFilePath, tempPdfFilePath2, reportId, isPreview);
            //     // reportTemplatedHeader(inputPath, tempPdfFilePath, reportId, isPreview);
            // }

        } else if ("xlsx".equals(fileType)) {
            documentFileManagementEntity = thsDocumectFileManagementService.getInfo(ObjectUtil.isEmpty(info.getExcelFileId()) ? "" : info.getExcelFileId());
            if (documentFileManagementEntity == null) {
                throw new Exception("Excel文件不存在");
            }
            tempPdfFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagementEntity.getFileSavePath() + documentFileManagementEntity.getFileName();
        } else {
            throw new Exception("不支持的文件类型");
        }

        // 设置响应头
        String fileName = reportId + name + "." + fileType;
        response.setContentType("application/octet-stream");
        response.setHeader("Accept-Ranges", "bytes");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Content-Security-Policy", "script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline';img-src 'self' data:;connect-src 'self' http://127.0.0.1:8080 data:;font-src 'self';object-src 'self';");

        try (BufferedInputStream is = FileUtil.getInputStream(tempPdfFilePath);
             ServletOutputStream out = response.getOutputStream()) {

            if ("pdf".equals(fileType)) {
                response.setContentType("application/pdf");
                IOUtils.copy(is, out);
            } else {
                byte[] bytes = IoUtil.readBytes(is);
                out.write(bytes);
            }
            out.flush();
        } catch (Exception e) {
            log.error(e.getMessage());
            throw e;
        } finally {
            if ("pdf".equals(fileType)) {
                FileUtil.del(tempPdfFilePath);
                FileUtil.del(tempPdfFilePath2);
            }
        }
    }


    /**
     * @param inputPath
     * @param outputPath
     * @Description: Todo ureport报表导出，设置水印，设置页眉页脚
     * @return: java.lang.String
     * @Author: hrj
     * @Date: 16:50 2024/6/20
     */
    private void reportTemplatedHeader(String inputPath, String outputPath, String reportId, Boolean isPreview) throws IOException {
//        String pdfPathTemp = "D:\\合并导出temp2.pdf";
        String pdfPathTemp = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + UUID.randomUUID() + ".pdf";
        String pdfPathImage = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + UUID.randomUUID() + ".pdf";


        ThsBusinessBudgetProjectDetailReportSerialNumberEntity thsBusinessBudgetProjectDetailReportSerialNumberEntity = new ThsBusinessBudgetProjectDetailReportSerialNumberEntity();
        if (!isPreview) {
            //  创建流水号打印
            ActionResult reportSerialNumber = thsBusinessBudgetProjectDetailReportSerialNumberService.createReportSerialNumber(reportId);
            thsBusinessBudgetProjectDetailReportSerialNumberEntity = (ThsBusinessBudgetProjectDetailReportSerialNumberEntity) reportSerialNumber.getData();
        }


        List<ImagesVO> imagesVOList = new ArrayList<>();

        List<PdfPageOfEnd> pageOfEndList = new ArrayList<>();
        //  查询页头页尾数据
        List<ThsBusinessBudgetProjectDetailExcelReportDetailsEntity> thsBusinessBudgetProjectDetailExcelReportDetailsEntities = thsBusinessBudgetProjectDetailExcelReportDetailsService.getThsBusinessBudgetProjectDetailExcelReportDetailsEntityByReportId(reportId);

        //  过滤已经设置值的数据
        List<ThsBusinessBudgetProjectDetailExcelReportDetailsEntity> collect = thsBusinessBudgetProjectDetailExcelReportDetailsEntities.stream().filter(thsBusinessBudgetProjectDetailExcelReportDetailsEntity -> ObjectUtil.isNotEmpty(thsBusinessBudgetProjectDetailExcelReportDetailsEntity.getValueData())).collect(Collectors.toList());

        //  获取logo
        String base64ImageLogo = cn.hutool.core.codec.Base64.encode(ResourceUtil.getResourceObj("image/logo1.png").readBytes());
        // 添加数据URI前缀（可选）
        String base64DataUriLogo = "data:image/png;base64," + base64ImageLogo;

        //  查询页头页尾的位置
        if (collect.isEmpty()) {
            setPosition(imagesVOList, pageOfEndList, "images", base64DataUriLogo, 50, 20);
        } else {

            Optional<ThsBusinessBudgetProjectDetailExcelReportDetailsEntity> topLeft = collect.stream().filter(item -> item.getName().equals("topLeft")).findFirst();
            if (topLeft.isPresent()) {
                //  topLeft 位置
                if (ObjectUtil.isNotEmpty(topLeft.get().getName()) && topLeft.get().getName().equals("topLeft")) {
                    if (ObjectUtil.isNotEmpty(topLeft.get().getValueType())) {
                        setPosition(imagesVOList, pageOfEndList, topLeft.get().getValueType(), topLeft.get().getValueData(), 10, 20);
                    }
                }
            } else {
                setPosition(imagesVOList, pageOfEndList, "images", base64DataUriLogo, 50, 20);
            }
            //  添加页头页尾
            ThsBusinessBudgetProjectDetailReportSerialNumberEntity finalThsBusinessBudgetProjectDetailReportSerialNumberEntity = thsBusinessBudgetProjectDetailReportSerialNumberEntity;
            collect.forEach(item -> {

                if (item.getValueType().equals("number")) {
                    // 打印
                    if (!isPreview) {
                        item.setValueData(finalThsBusinessBudgetProjectDetailReportSerialNumberEntity.getSerialNumber().toString());
                    }
                    // 打印预览
                    else {
                        item.setValueData("");
                    }
                }
                //  页眉中间
                if (ObjectUtil.isNotEmpty(item.getName()) && item.getName().equals("topMid")) {
                    setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 250, 820);
                }

                //  页眉右边
                if (ObjectUtil.isNotEmpty(item.getName()) && item.getName().equals("topRight")) {
                    setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 480, 820);
                }

                //  页脚右边
                if (ObjectUtil.isNotEmpty(item.getName()) && item.getName().equals("downRight")) {
                    setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 440, 20);
                }

                //  页脚中间
                if (ObjectUtil.isNotEmpty(item.getName()) && item.getName().equals("downMid")) {
                    setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 250, 20);
                }

                //  页脚左边
                if (ObjectUtil.isNotEmpty(item.getName()) && item.getName().equals("downLeft")) {
                    if (item.getValueType().equals("images")) {
                        setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 10, 780);
                    } else {
                        setPosition(imagesVOList, pageOfEndList, item.getValueType(), item.getValueData(), 10, 20);
                    }

                }

            });
        }

        // 添加pdf页码
        AsposeToPdfUtil.pdfAddNumber(inputPath, pdfPathTemp, pageOfEndList);

        //  添加页眉
        AsposeToPdfUtil.pdfAddImage(pdfPathTemp, pdfPathImage, imagesVOList);

        //  获取水印
        String base64ImageWatermark = cn.hutool.core.codec.Base64.encode(ResourceUtil.getResourceObj("image/watermark.png").readBytes());
        // 添加数据URI前缀（可选）
        // String base64DataUriWatermark = "data:image/png;base64," + base64ImageWatermark;

        //  添加水印
        AsposeToPdfUtil.addPdfWater(pdfPathImage, outputPath, base64ImageWatermark);


        FileUtil.del(pdfPathTemp);
        FileUtil.del(pdfPathImage);

    }

    /**
     * @param imagesVOList  图片类型
     * @param pageOfEndList 字符串类型
     * @Description: 设置页眉页脚位置
     * @return: void
     * @Author: hrj
     * @Date: 15:20 2024/6/25
     */
    public void setPosition(List<ImagesVO> imagesVOList, List<PdfPageOfEnd> pageOfEndList, String valueType, String valueDate, int x, int y) {
        ImagesVO imagesVO = new ImagesVO();
        PdfPageOfEnd pdfPageOfEnd = new PdfPageOfEnd();
        if (valueType.equals("images")) {
            imagesVO.setUrl(valueDate);
            imagesVO.setX(x);
            imagesVO.setY(y);
            imagesVOList.add(imagesVO);
        }
        if (valueType.equals("page")) {
            pdfPageOfEnd.setX(x);
            pdfPageOfEnd.setY(y);
            pdfPageOfEnd.setType(2);
            pdfPageOfEnd.setValue(valueDate);
            pageOfEndList.add(pdfPageOfEnd);
        }
        if (valueType.equals("date")) {
            pdfPageOfEnd.setX(x);
            pdfPageOfEnd.setY(y);
            pdfPageOfEnd.setType(1);
            pdfPageOfEnd.setValue(valueDate);
            pageOfEndList.add(pdfPageOfEnd);
        }
        if (valueType.equals("number")) {
            pdfPageOfEnd.setX(x);
            pdfPageOfEnd.setY(y);
            pdfPageOfEnd.setType(3);
            pdfPageOfEnd.setValue(valueDate);
            pageOfEndList.add(pdfPageOfEnd);
        }
    }

    /**
     * @param consultId:
     * @param request:
     * @return ActionResult
     * <AUTHOR>
     * @description 咨询项目及一表通报表归档
     * @date 2024/8/15 10:17
     */
    @Override
    public ActionResult archiveReportByConsultId(String consultId, HttpServletRequest request) throws Exception {
        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> reportEntityQueryWrapper = new QueryWrapper<>();
        reportEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, consultId);

        List<ThsBusinessBudgetProjectDetailExcelReportEntity> reportEntityList = this.list(reportEntityQueryWrapper);

        // 根据consultId查询对应终版的建设项目id
        List<ThsBusinessBudgetProjectVersionEntity> listByConsultIdAndIsFinalize = thsBusinessBudgetProjectVersionService.getListByConsultIdAndIsFinalize(consultId, ThsBusinessBudgetProjectVersionEntity.TEMPLATE_CASE_TEMPLATE);

        if (listByConsultIdAndIsFinalize.size() == 0) {
            return ActionResult.fail("咨询项目及一表通报表归档失败，对应协同计价无终版数据，请先设置终版数据！");
        }
        String budgetProjectId = listByConsultIdAndIsFinalize.get(0).getId();
        // 获取对应终版的建设项目的子节点id
        String detailId = thsBusinessBudgetProjectDetailService.getListByBudgetProjectId(listByConsultIdAndIsFinalize.get(0).getId())
                .stream().filter(d -> ObjectUtil.isEmpty(d.getPid()) || StringUtil.isEmpty(d.getPid()))
                .map(ThsBusinessBudgetProjectDetailEntity::getId).findFirst().orElse("");
        for (ThsBusinessBudgetProjectDetailExcelReportEntity entity : reportEntityList) {
            // 只能归档一次
            if (ObjectUtil.isEmpty(entity.getIsArchived()) || entity.getIsArchived() != 1) {
                templateFileArchiving(detailId, entity.getId(), budgetProjectId);
            }
        }
        return ActionResult.success();
    }

    private String uploadFile(String objectName, String folderName, byte[] fileData, String fileDesc, String fileType) {
        FileInfo fileInfo = FileUploadUtils.uploadFile(fileData, folderName, objectName);
        return saveThsDocumectFileManagement(userProvider.get(), fileInfo.getFilename(), fileInfo.getPath(), fileDesc, fileType, fileInfo.getSize());
    }

    private String saveThsDocumectFileManagement(UserInfo userInfo, String fileName, String filePath, String fileDesc, String fileType, Long fileSize) {
        ThsDocumectFileManagementEntity documentFileManagementEntity = new ThsDocumectFileManagementEntity();
        String mainId = RandomUtil.uuId();
        documentFileManagementEntity.setId(mainId);
        documentFileManagementEntity.setFileName(fileName);
        documentFileManagementEntity.setFileSavePath(filePath);
        documentFileManagementEntity.setFileSize(fileSize);
        documentFileManagementEntity.setFileDesc(fileDesc);
        documentFileManagementEntity.setCreatedDate(DateUtil.getNowDate());
        documentFileManagementEntity.setCreatedBy(userInfo.getUserId());
        documentFileManagementEntity.setTenantId(userInfo.getTenantId());
        documentFileManagementEntity.setUploader(userInfo.getUserId());
        documentFileManagementEntity.setFileType(fileType);
        thsDocumectFileManagementService.save(documentFileManagementEntity);
        return mainId;
    }

    @Override
    public void deleteExcelReport(String name, String reportId, String folderName) {
        ThsBusinessBudgetProjectDetailExcelReportEntity info = this.getInfo(reportId);


        ThsDocumectFileManagementEntity documentFileManagementEntity = thsDocumectFileManagementService.getInfo(ObjectUtil.isEmpty(info.getExcelFileId()) ? "" : info.getExcelFileId());

        //  物理删除文件,获取全局保存路径
        String sourceFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagementEntity.getFileSavePath() + documentFileManagementEntity.getFileName();
        FileUtil.del(sourceFilePath);
        //  删除记录
        thsDocumectFileManagementService.removeById(documentFileManagementEntity.getId());
    }

    /**
     * 为PDF文件添加页眉页脚、logo图片和流水号
     *
     * @param info
     * @param pdfPath          原始PDF文件路径
     * @param tempPdfFilePath  临时PDF文件路径
     * @param tempPdfFilePath2 第二个临时PDF文件路径
     * @param reportId         报告ID
     * @param isPreview        是否为预览模式
     * @return 处理后的PDF文件路径
     * @throws IOException 如果发生I/O错误
     */
    private String pdfFileAddHeaderFooter(ThsBusinessBudgetProjectDetailExcelReportEntity info, String pdfPath, String tempPdfFilePath, String tempPdfFilePath2, String reportId, Boolean isPreview) throws IOException {
        ThsTemplateReportCategoryFileEntity templateReportCategoryFile = thsTemplateReportCategoryFileService.getInfo(info.getDataReportId());
        String finalPdfPath = pdfPath;

        // 行内工程-编制说明、审核说明 页脚添加：经办人、责任人、页码
        if (templateReportCategoryFile.getCode().contains("BZSM_BZL_HN") || templateReportCategoryFile.getCode().contains("SHSM_SHL_HN")) {
            handleCompilationOrAuditDescription(info, finalPdfPath, tempPdfFilePath, tempPdfFilePath2, reportId, isPreview, true);
        } else if (templateReportCategoryFile.getCode().contains("BZSM_BZL_HW") || templateReportCategoryFile.getCode().contains("SHSM_SHL_HW")) {
            // 行外工程-编制说明、审核说明 页脚添加：经办人、日期 流水号、页码
            handleCompilationOrAuditDescription(info, finalPdfPath, tempPdfFilePath, tempPdfFilePath2, reportId, isPreview, true);
        } else {
            // 否则按照原设置添加页眉页脚
            reportTemplatedHeader(finalPdfPath, tempPdfFilePath, reportId, isPreview);
            return tempPdfFilePath;
        }

        return tempPdfFilePath2;
    }

    /**
     * 处理编制说明或审核说明的页眉页脚
     *
     * @param info
     * @param pdfPath              原始PDF文件路径
     * @param tempPdfFilePath      临时PDF文件路径
     * @param tempPdfFilePath2     第二个临时PDF文件路径
     * @param reportId             报告ID
     * @param isPreview            是否为预览模式
     * @param addResponsiblePerson 是否添加“责任人”文字
     * @throws IOException 如果发生I/O错误
     */
    private void handleCompilationOrAuditDescription(ThsBusinessBudgetProjectDetailExcelReportEntity info, String pdfPath, String tempPdfFilePath, String tempPdfFilePath2, String reportId, Boolean isPreview, boolean addResponsiblePerson) throws IOException {
        List<ImagesVO> imagesVOList = createImagesVOList(info, addResponsiblePerson);

        // 3、往pdf中添加图片
        if (!imagesVOList.isEmpty()) {
            AsposeToPdfUtil.pdfAddImage(pdfPath, tempPdfFilePath, imagesVOList);
        } else {
            tempPdfFilePath = pdfPath;
        }

        List<PdfPageOfEnd> pageOfEndList = createPageOfEndList(addResponsiblePerson);

        // 如果是打印则添加流水号，打印预览不需要
        if (!addResponsiblePerson) {
            String serialNumber = isPreview ? "" : getSerialNumber(reportId);
            PdfPageOfEnd pdfPageOfEnd3 = new PdfPageOfEnd();
            pdfPageOfEnd3.setX(220);
            pdfPageOfEnd3.setY(20);
            pdfPageOfEnd3.setType(4);
            pdfPageOfEnd3.setValue(serialNumber);
            pageOfEndList.add(pdfPageOfEnd3);
        }

        AsposeToPdfUtil.pdfAddNumber(tempPdfFilePath, tempPdfFilePath2, pageOfEndList);
        if (!imagesVOList.isEmpty()) {
            FileUtil.del(tempPdfFilePath);
        }
    }

    /**
     * 创建编制说明或审核说明的 ImagesVO 列表
     *
     * @param info
     * @return ImagesVO 列表
     */
    private List<ImagesVO> createImagesVOList(ThsBusinessBudgetProjectDetailExcelReportEntity info, boolean addResponsiblePerson) {
        // 查询经办人即编制人
        ArrayList<String> strings = new ArrayList<>();
        // List<Map<String, Object>> prepared = getPrepared(info.getDetailId());
        // for (Map<String, Object> stringObjectMap : prepared) {
        //     String userId = stringObjectMap.get("userId").toString();
        //     String signature = this.getSignature(userId);
        //     if (ObjectUtil.isNotEmpty(signature)) {
        //         strings.add(signature);
        //     }
        // }
        //  查询报表创建人
        ThsBusinessBudgetProjectDetailExcelReportEntity detailExcelReport = getInfoByDetailIdAndReportId(info.getDetailId(), info.getDataReportId());
        if (ObjectUtil.isNotEmpty(detailExcelReport)) {
            String signature = this.getSignature(detailExcelReport.getCreatorId());
            if (ObjectUtil.isNotEmpty(signature)) {
                strings.add(signature);
            }
        }

        final int[] x = {90};
        //  经办人签名位置
        int y = 780;
        List<ImagesVO> imagesVOList = new ArrayList<>();

        strings.forEach(str -> {
            // 创建新的 ImagesVO 对象
            ImagesVO imagesVO1 = new ImagesVO();
            imagesVO1.setUrl(str);
            imagesVO1.setX(x[0]);
            imagesVO1.setY(y);
            x[0] += 70;
            if (x[0] > 280) {
                imagesVO1.setX(x[0] - 280);
                imagesVO1.setY(y - 20);
            }
            imagesVOList.add(imagesVO1);
        });

        if (addResponsiblePerson) {
            // 添加责任人签名
            String budgetProjectId = thsBusinessBudgetProjectDetailService.getInfo(info.getDetailId()).getBudgetProjectId();
            String supervisor = this.getSupervisor(budgetProjectId);
            String signature = this.getSignature(supervisor);

            if (ObjectUtil.isNotEmpty(signature)) {
                ImagesVO imagesVO3 = new ImagesVO();
                imagesVO3.setUrl(signature);
                imagesVO3.setX(280);
                imagesVO3.setY(y);
                imagesVOList.add(imagesVO3);
            }
        }

        return imagesVOList;
    }

    /**
     * 创建 PdfPageOfEnd 列表
     *
     * @param addResponsiblePerson 是否添加“责任人”文字
     * @return PdfPageOfEnd 列表
     */
    private List<PdfPageOfEnd> createPageOfEndList(boolean addResponsiblePerson) {
        List<PdfPageOfEnd> pageOfEndList = new ArrayList<>();

        // 添加 '经办人' 文字
        PdfPageOfEnd pdfPageOfEnd = new PdfPageOfEnd();
        pdfPageOfEnd.setX(50);
        pdfPageOfEnd.setY(20);
        pdfPageOfEnd.setType(5);
        pdfPageOfEnd.setValue("经办人：");
        pageOfEndList.add(pdfPageOfEnd);

        // 添加 '责任人' 文字
        if (addResponsiblePerson) {
            PdfPageOfEnd pdfPageOfEnd2 = new PdfPageOfEnd();
            pdfPageOfEnd2.setX(240);
            pdfPageOfEnd2.setY(20);
            pdfPageOfEnd2.setType(5);
            pdfPageOfEnd2.setValue("责任人：");
            pageOfEndList.add(pdfPageOfEnd2);
        }

        // 添加 页脚
        PdfPageOfEnd pdfPageOfEnd3 = new PdfPageOfEnd();
        pdfPageOfEnd3.setX(420);
        pdfPageOfEnd3.setY(20);
        pdfPageOfEnd3.setType(2);
        pdfPageOfEnd3.setValue("page1");
        pageOfEndList.add(pdfPageOfEnd3);

        return pageOfEndList;
    }

    /**
     * 获取流水号
     *
     * @param reportId 报告ID
     * @return 流水号
     */
    private String getSerialNumber(String reportId) {
        // 创建流水号打印
        ActionResult reportSerialNumber = thsBusinessBudgetProjectDetailReportSerialNumberService.createReportSerialNumber(reportId);
        ThsBusinessBudgetProjectDetailReportSerialNumberEntity thsBusinessBudgetProjectDetailReportSerialNumberEntity = (ThsBusinessBudgetProjectDetailReportSerialNumberEntity) reportSerialNumber.getData();
        return thsBusinessBudgetProjectDetailReportSerialNumberEntity.getSerialNumber().toString();
    }


    /**
     * @return String
     * <AUTHOR>
     * @description 不同的报表将/preview接口返回的html内容中添加签名
     * @date 2024/6/18 17:20
     */
    private String htmlAddSignature(String html, String budgetProjectId, String detailId, String reportId, String name) {
        if (("总封面").equals(name)) {
            Map<String, Object> data = (Map<String, Object>) this.generalCoverSignature(budgetProjectId, detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                String bsrUrl = ((List<String>) data.get("bsr")).stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\"  alt=\"\">")
                        .collect(Collectors.joining());
                String hzrUrl = ((List<String>) data.get("hzr")).stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\"  alt=\"\">")
                        .collect(Collectors.joining());
                String zgUrl = "<img src=\"" + data.get("zg").toString() + "\" style=\"height:50px;\"  alt=\"\">";
                html = html.replaceAll("<td rowspan=\"2\" colspan=\"4\" class='_B11'  >.*?</td>",
                        "<td rowspan=\"2\" colspan=\"4\" class='_B11'  >" + bsrUrl + "</td>");

                html = html.replaceAll("<td rowspan=\"2\" colspan=\"4\" class='_B13'  >.*?</td>",
                        "<td rowspan=\"2\" colspan=\"4\" class='_B13'  >" + hzrUrl);

                html = html.replaceAll("<td colspan=\"4\" class='_B15'  >.*?</td>",
                        "<td colspan=\"4\" class='_B15'  >" + zgUrl);
            }

        } else if (("造价汇总表").equals(name)) {
            Map<String, ArrayList<String>> data = (Map<String, ArrayList<String>>) this.signatureOfCostSummaryTable(budgetProjectId, detailId, reportId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                String bzrUrl = data.get("zbr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"width:100%;height:180%;\" alt=\"\">")
                        .collect(Collectors.joining());
                String jhrUrl = data.get("jhr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"width:100%;height:180%;\" alt=\"\">")
                        .collect(Collectors.joining());
                String zrrUrl = data.get("zrr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"width:100%;height:180%;\" alt=\"\">")
                        .collect(Collectors.joining());
                html = html.replaceAll("<td colspan=\"3\" class='_B11'  >.*?</td>",
                        "<td colspan=\"3\" class='_B11'  >" + bzrUrl + "</td>");

                html = html.replaceAll("<td colspan=\"2\" class='_F11'  >.*?</td>",
                        "<td colspan=\"2\" class='_F11'  >" + jhrUrl + "</td>");

                html = html.replaceAll("<td colspan=\"2\" class='_I11'  >.*?</td>",
                        "<td colspan=\"2\" class='_I11'  >" + zrrUrl + "</td>");
            }

        } else if (("小封面").equals(name)) {
            Map<String, ArrayList<String>> data = (Map<String, ArrayList<String>>) this.smallCoverSignature(detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                String shrUrl = data.get("shr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\" alt=\"\">")
                        .collect(Collectors.joining());
                String jhrUrl = data.get("jhr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\" alt=\"\">")
                        .collect(Collectors.joining());
                String hzrUrl = data.get("hzr").stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\" alt=\"\">")
                        .collect(Collectors.joining());
                html = html.replaceAll("<td colspan=\"5\" class='_B12'  >.*?</td>",
                        "<td colspan=\"5\" class='_B12'  >" + shrUrl);

                html = html.replaceAll("<td colspan=\"5\" class='_B15'  >.*?</td>",
                        "<td colspan=\"5\" class='_B15'  >" + jhrUrl);

                html = html.replaceAll("<td colspan=\"5\" class='_B18'  >.*?</td>",
                        "<td colspan=\"5\" class='_B18'  >" + hzrUrl);
            }
        } else if (("单项封面").equals(name)) {

            Map<String, Object> data = (Map<String, Object>) this.singleSignature(budgetProjectId, detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                String bsrUrl = ((List<String>) data.get("bsr")).stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\" alt=\"\">")
                        .collect(Collectors.joining());
                String hzrUrl = ((List<String>) data.get("hzr")).stream()
                        .map(s -> "<img src=\"" + s + "\" style=\"height:50px;\" alt=\"\">")
                        .collect(Collectors.joining());
                String zgUrl = "<img src=\"" + data.get("zg").toString() + "\" style=\"height:50px;\" alt=\"\">";
                html = html.replaceAll("<td colspan=\"5\" class='_B15'  >.*?</td>",
                        "<td colspan=\"5\" class='_B15'  >" + bsrUrl);

                html = html.replaceAll("<td colspan=\"5\" class='_B18'  >.*?</td>",
                        "<td colspan=\"5\" class='_B18'  >" + hzrUrl);

                html = html.replaceAll("<td colspan=\"5\" class='_B21'  >.*?</td>",
                        "<td colspan=\"5\" class='_B21'  >" + zgUrl);
            }
        }
        return html;
    }

    @Override
    public ActionResult uploadNoEditFieldExcelReport(MultipartFile file, String reportId, String fileName) throws IOException {
        try {
            ThsBusinessBudgetProjectDetailExcelReportEntity info = this.getInfo(reportId);
            if (info == null) {
                return ActionResult.fail("报表不存在");
            }
            ThsDocumectFileManagementEntity documentFileManagementEntity = thsDocumectFileManagementService.getInfo(ObjectUtil.isEmpty(info.getExcelFileId()) ? "" : info.getExcelFileId());

            if (documentFileManagementEntity == null) {
                return ActionResult.fail("文件不存在");
            }

            String sourceFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagementEntity.getFileSavePath() + documentFileManagementEntity.getFileName();

            // 删除 sourceFilePath 文件
            File sourceFile = new File(sourceFilePath);
            if (sourceFile.exists()) {
                FileUtil.del(sourceFile);
            }

            // 上传 file，保存路径为 sourceFilePath，保存指定文件名为 documentFileManagementEntity.getFileName()
            try (OutputStream outputStream = new FileOutputStream(sourceFilePath)) {
                outputStream.write(file.getBytes());
            }
            //  修改报表编辑时间
            thsBusinessBudgetProjectDetailReportSerialNumberService.createOrUpdateReportAddRecord(reportId);

            // // 检查源文件是否存在
            // File sourceFile = new File(sourceFilePath);
            // if (!sourceFile.exists()) {
            //     return ActionResult.fail("源文件不存在");
            // }
            //
            // // 使用样式复制方法：将新上传文件的数据复制到原文件中，保留原文件的样式
            // copyExcelWithStyle(sourceFilePath, file);

            return ActionResult.success();
        } catch (IOException e) {
            return ActionResult.fail("上传失败", e.getMessage());
        } catch (Exception e) {
            return ActionResult.fail("处理文件时发生错误", e.getMessage());
        }
    }

    @Override
    public void archiveReportMerge(List<String> reportIds, HttpServletResponse response) {
        // 合并报表
        try {
            // 读取现有的 pdf 文件
            List<ThsBusinessBudgetProjectDetailExcelReportEntity> reportList = reportIds.stream()
                    .map(reportId -> {
                        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getId, reportId);
                        return this.getOne(queryWrapper);
                    })
                    .collect(Collectors.toList());

            if (reportList.size() != reportIds.size()) {
                throw new Exception("报表不存在");
            }

            ArrayList<File> files = new ArrayList<>();
            for (ThsBusinessBudgetProjectDetailExcelReportEntity entity : reportList) {
                if (ObjectUtil.isEmpty(entity) || entity.getIsArchived() != 1) {
                    throw new Exception("报表" + entity.getName() + "未归档");
                }
                if (ObjectUtil.isEmpty(entity.getArchivedFileId())) {
                    throw new Exception("报表" + entity.getName() + "归档文件不存在");
                }
                ThsDocumectFileManagementEntity documectFileManagement = thsDocumectFileManagementService.getInfo(entity.getArchivedFileId());

                // 拿到pdf文件路径
                String sourceFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documectFileManagement.getFileSavePath() + documectFileManagement.getFileName();
                String pdfFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documectFileManagement.getFileSavePath() + UUID.randomUUID() + ".pdf";

                //  添加页眉页脚，logo水印、流水号
                reportTemplatedHeader(sourceFilePath, pdfFilePath, entity.getId(), false);
                files.add(new File(pdfFilePath));

            }

            File file = PdfFileUtil.mergePdf(files, BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + reportList.get(0).getDetailId() + "/" + DateUtil.dateNow("yyyyMMdd") + "合并导出.pdf");

            String fileName = "mergedFile_" + DateUtil.dateNow("yyyyMMddHHmmss") + ".pdf";

            try (BufferedInputStream is = FileUtil.getInputStream(file)) {

                // 设置内容格式
                response.setContentType("application/octet-stream");
                response.setHeader("Accept-Ranges", "bytes");
                try {
                    response.addHeader("Content-Disposition",
                            "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                    // 内容安全策略
                    response.setHeader("Content-Security-Policy", "script-src 'self' 'unsafe-inline' 'unsafe-eval';style-src 'self' 'unsafe-inline';img-src 'self' data:;connect-src 'self' http://127.0.0.1:8080 data:;font-src 'self';object-src 'self';");
                } catch (UnsupportedEncodingException e) {
                    log.error("文件编码异常 : {}" + e.getMessage());
                }

                // 设置响应头信息
                response.setContentType("application/pdf");
                response.addHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

                IOUtils.copy(is, response.getOutputStream());
                response.getOutputStream().flush();

            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                // 在finally块中进行文件删除操作
                if (file.exists()) {
                    file.delete();
                }
            }

        } catch (Exception e) {
            log.error("合并报表导出失败", e);
        }
    }


    /**
     * @return void
     * <AUTHOR>
     * @description 添加“单项封面”判断是否需要出封面
     * @date 2024/5/23 11:03
     */
    @Override
    public void isHaveCoverWhenAdd(String detailId, String name) {
        // 根据detailId获取所有子节点
        List<ThsBusinessBudgetProjectDetailEntity> allChildren = thsBusinessBudgetProjectDetailService.findAllChildren(detailId);
        ThsBusinessBudgetProjectDetailEntity info = thsBusinessBudgetProjectDetailService.getInfo(detailId);

        List<String> selfAndChildrenDetailIds = allChildren.stream().map(ThsBusinessBudgetProjectDetailEntity::getId).collect(Collectors.toList());
        selfAndChildrenDetailIds.add(detailId);
        // 判断自己以及子节点是否含有单项封面
        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, selfAndChildrenDetailIds);
        List<String> selfAndChildrenReportNames = this.list(queryWrapper).stream().map(ThsBusinessBudgetProjectDetailExcelReportEntity::getName).collect(Collectors.toList());

        List<ThsBusinessBudgetProjectDetailEntity> allParents = thsBusinessBudgetProjectDetailService.findAllParents(detailId);


        if (!selfAndChildrenReportNames.contains(name)) {
            info.setHaveCover(1);
            allParents.forEach(t -> {
                if (ObjectUtil.isEmpty(t.getHaveCover()) || t.getHaveCover() == 1) {
                    t.setHaveCover(0);
                    thsBusinessBudgetProjectDetailService.updateById(t);
                }
            });
            //（自己+子节点）都没有封面，status=1,且所有父节点status=0;
            thsBusinessBudgetProjectDetailService.updateById(info);

        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @description 删除“单项封面”判断是否需要出封面
     * @date 2024/5/23 11:03
     */
    @Override
    public void isHaveCoverWhenDelete(String detailId, String name) {
        // 根据detailId获取所有子节点
        List<ThsBusinessBudgetProjectDetailEntity> allChildren = thsBusinessBudgetProjectDetailService.findAllChildren(detailId);
        ThsBusinessBudgetProjectDetailEntity info = thsBusinessBudgetProjectDetailService.getInfo(detailId);

        // 判断自己以及子节点是否含有单项封面
        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, detailId);
        List<String> currentReportNames = this.list(queryWrapper).stream().map(ThsBusinessBudgetProjectDetailExcelReportEntity::getName).collect(Collectors.toList());

        if (allChildren.size() == 0) {
            // 如果当前节点为最底层的节点
            handleSingleNode(info, name, currentReportNames);
        } else {

            handleMultipleNodes(info, name, currentReportNames);
        }

    }

    /**
     * @Description: Todo 校核人
     * @Param: * @param detailId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:32 2024/6/17
     */
    @Override
    public List<Map<String, Object>> getCheckers(String detailId) {
        return this.getBaseMapper().getCheckers(detailId);
    }

    /**
     * @Description: Todo 核准人
     * @Param: * @param detailId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:35 2024/6/17
     */
    @Override
    public List<Map<String, Object>> getApproved(String detailId) {
        return this.getBaseMapper().getApproved(detailId);
    }

    /**
     * @Description: Todo 审核人
     * @Param: * @param detailId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:35 2024/6/17
     */
    @Override
    public List<Map<String, Object>> getReviewer(String detailId) {
        return this.getBaseMapper().getReviewer(detailId);
    }

    /**
     * @Description: Todo 编制人
     * @Param: * @param detailId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:35 2024/6/17
     */
    @Override
    public List<Map<String, Object>> getPrepared(String detailId) {
        return this.getBaseMapper().getPrepared(detailId);
    }

    /**
     * @Description: Todo 编审人
     * @Param: * @param detailId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:35 2024/6/17
     */
    @Override
    public List<Map<String, Object>> getEditorAndReviewer(String detailId) {
        return this.getBaseMapper().getEditorAndReviewer(detailId);
    }

    /**
     * @Description: Todo 主管 责任人
     * @Param: * @param versionId
     * @return: java.lang.String
     * @Author: hrj
     * @Date: 14:35 2024/6/17
     */
    @Override
    public String getSupervisor(String versionId) {
        ThsBusinessBudgetProjectVersionEntity thsBusinessBudgetProjectVersionEntity = thsBusinessBudgetProjectVersionService.getById(versionId);
        return thsBusinessBudgetProjectVersionEntity.getObligUserId();
    }

    /**
     * @Description: Todo 制表人
     * @Param: * @param id
     * @return: java.lang.String
     * @Author: hrj
     * @Date: 14:36 2024/6/17
     */
    @Override
    public String getWatchmaker(String id) {
        ThsBusinessBudgetProjectDetailExcelReportEntity thsBusinessBudgetProjectDetailExcelReportEntity = this.getById(id);
        return thsBusinessBudgetProjectDetailExcelReportEntity.getCreatorId();
    }


    /**
     * @param detailId
     * @param id
     * @Description: Todo 造价汇总表签名
     * @Param versionId
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Author: hrj
     * @Date: 14:40 2024/6/17
     */
    @Override
    public ActionResult signatureOfCostSummaryTable(String versionId, String detailId, String id) {
        Map<String, Object> objectMap = new HashMap<>();
        //  制表人
        String watchmakerId = this.getWatchmaker(id);

        //  校核人
        List<Map<String, Object>> approved = this.getCheckers(detailId);

        ArrayList<String> signatureZbr = new ArrayList();
        ArrayList<String> signaturejhr = new ArrayList();
        ArrayList<String> signatureZrr = new ArrayList();

        //  责任人
        String supervisor = this.getSupervisor(versionId);

        for (Map<String, Object> stringObjectMap : approved) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturejhr.add(signature);
            }
        }
        signatureZbr.add(this.getSignature(watchmakerId));
        signatureZrr.add(this.getSignature(supervisor));
        objectMap.put("zbr", signatureZbr);
        objectMap.put("fhr", signaturejhr);
        objectMap.put("zrr", signatureZrr);

        return ActionResult.success(objectMap);
    }


    /**
     * @param detailId
     * @Description: Todo 小封面签名
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:00 2024/6/18
     */
    @Override
    public ActionResult smallCoverSignature(String detailId) {
        Map<String, Object> objectMap = new HashMap<>();

        List<Map<String, Object>> prepared = this.getPrepared(detailId);
        List<Map<String, Object>> reviewer = this.getReviewer(detailId);
        List<Map<String, Object>> checkers = this.getCheckers(detailId);
        List<Map<String, Object>> approved = this.getApproved(detailId);

        ArrayList<String> signaturebzr = new ArrayList();
        ArrayList<String> signatureshr = new ArrayList();
        ArrayList<String> signaturejhr = new ArrayList();
        ArrayList<String> signaturehzr = new ArrayList();

        for (Map<String, Object> stringObjectMap : prepared) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturebzr.add(signature);
            }
        }

        for (Map<String, Object> stringObjectMap : reviewer) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signatureshr.add(signature);
            }
        }
        for (Map<String, Object> stringObjectMap : checkers) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturejhr.add(signature);
            }
        }
        for (Map<String, Object> stringObjectMap : approved) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturehzr.add(signature);
            }
        }
        objectMap.put("bzr", signaturebzr);
        objectMap.put("shr", signatureshr);
        objectMap.put("jhr", signaturejhr);
        objectMap.put("hzr", signaturehzr);

        return ActionResult.success(objectMap);
    }

    @Override
    public ActionResult singleSignature(String versionId, String detailId) {
        Map<String, Object> objectMap = new HashMap<>();

        List<Map<String, Object>> editorAndReviewer = this.getEditorAndReviewer(detailId);
        List<Map<String, Object>> approved = this.getApproved(detailId);
        String supervisor = this.getSupervisor(versionId);

        ArrayList<String> signaturebsr = new ArrayList();
        ArrayList<String> signaturehzr = new ArrayList();
        ArrayList<String> signaturezg = new ArrayList();


        for (Map<String, Object> stringObjectMap : editorAndReviewer) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturebsr.add(signature);
            }
        }
        for (Map<String, Object> stringObjectMap : approved) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturehzr.add(signature);
            }
        }

        signaturezg.add(this.getSignature(supervisor));
        objectMap.put("bsr", signaturebsr);
        objectMap.put("hzr", signaturehzr);

        objectMap.put("zg", signaturezg);

        return ActionResult.success(objectMap);
    }

    /**
     * @param versionId
     * @param detailId
     * @Description: Todo 总封面签名
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:18 2024/6/18
     */
    @Override
    public ActionResult generalCoverSignature(String versionId, String detailId) {
        Map<String, Object> objectMap = new HashMap<>();

        List<Map<String, Object>> editorAndReviewer = this.getEditorAndReviewer(detailId);
        List<Map<String, Object>> approved = this.getApproved(detailId);
        List<Map<String, Object>> checkers = this.getCheckers(detailId);
        List<Map<String, Object>> reviewer = this.getReviewer(detailId);

        ArrayList<String> signaturebsr = new ArrayList();
        ArrayList<String> signaturehzr = new ArrayList();
        ArrayList<String> signaturezg = new ArrayList();


        for (Map<String, Object> stringObjectMap : editorAndReviewer) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturebsr.add(signature);
            }
        }
        for (Map<String, Object> stringObjectMap : approved) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturehzr.add(signature);
            }
        }

        //  加入校核人
        for (Map<String, Object> stringObjectMap : checkers) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturebsr.add(signature);
            }
        }

        //  将主管改成审批人
        for (Map<String, Object> stringObjectMap : reviewer) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturezg.add(signature);
            }
        }

        // signaturezg.add(this.getSignature(supervisor));
        objectMap.put("bsr", signaturebsr);
        objectMap.put("hzr", signaturehzr);
        objectMap.put("zg", signaturezg);

        return ActionResult.success(objectMap);
    }

    /**
     * @param versionId
     * @param detailId
     * @Description: Todo 工程量清单签名
     * @return: jnpf.base.ActionResult
     * @Author: hrj
     * @Date: 9:28 2024/6/18
     */
    @Override
    public ActionResult signatureOfBillOfQuantities(String versionId, String detailId) {
        Map<String, Object> objectMap = new HashMap<>();

        List<Map<String, Object>> prepared = this.getPrepared(detailId);
        List<Map<String, Object>> approved = this.getApproved(detailId);

        ArrayList<String> signaturebzr = new ArrayList();
        ArrayList<String> signaturehzr = new ArrayList();

        for (Map<String, Object> stringObjectMap : prepared) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturebzr.add(signature);
            }
        }
        for (Map<String, Object> stringObjectMap : approved) {
            String userId = stringObjectMap.get("userId").toString();
            String signature = this.getSignature(userId);
            if (ObjectUtil.isNotEmpty(signature)) {
                signaturehzr.add(signature);
            }
        }

        objectMap.put("bzr", signaturebzr);
        objectMap.put("hzr", signaturehzr);


        return ActionResult.success(objectMap);
    }

    /**
     * @param detailId:
     * @return List<String>
     * <AUTHOR>
     * @description 根据detialId查询经办人的签名 加入到页脚中
     * @date 2024/9/5 10:59
     */
    private List<String> signatureOfOperator(String detailId) {
        // 编制说明添加页脚:经办人 责任人
        ArrayList<String> signatureJbr = new ArrayList<>();
        String consultProjectIdByDetailId = this.getBaseMapper().getConsultProjectIdByDetailId(detailId);
        List<ThsBusinessConsultProjectHandlingOperatorEntity> handlingOperatorEntities = thsBusinessConsultProjectHandlingOperatorService.getListByProjectId(consultProjectIdByDetailId);

        for (ThsBusinessConsultProjectHandlingOperatorEntity handlingOperatorEntity : handlingOperatorEntities) {
            String signature = this.getSignature(handlingOperatorEntity.getOperatorId());
            if (ObjectUtil.isNotEmpty(signature)) {
                signatureJbr.add(signature);
            }
        }
        return signatureJbr;
    }

    @Override
    public void createThsBusinessBudgetProjectDetailExcelReportDetails(ThsBusinessBudgetProjectDetailExcelReportEntity thsBusinessBudgetProjectDetailExcelReportEntity) {

        ActionResult listByDataReportId = thsDataReportDetailsService.getListByDataReportId(thsBusinessBudgetProjectDetailExcelReportEntity.getDataReportId());
        List<ThsDataReportDetailsEntity> thsDataReportDetailsEntityList = (List<ThsDataReportDetailsEntity>) listByDataReportId.getData();

        List<ThsBusinessBudgetProjectDetailExcelReportDetailsEntity> thsBusinessBudgetProjectDetailExcelReportDetailsEntities = new ArrayList<>();

        //  循环
        for (ThsDataReportDetailsEntity thsDataReportDetailsEntity : thsDataReportDetailsEntityList) {
            ThsBusinessBudgetProjectDetailExcelReportDetailsEntity thsBusinessBudgetProjectDetailExcelReportDetailsEntity = new ThsBusinessBudgetProjectDetailExcelReportDetailsEntity();

            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setId(UUID.randomUUID().toString());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setValueType(thsDataReportDetailsEntity.getValueType());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setName(thsDataReportDetailsEntity.getName());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setValueData(thsDataReportDetailsEntity.getValueData());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setDetailId(thsBusinessBudgetProjectDetailExcelReportEntity.getDetailId());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setReportId(thsBusinessBudgetProjectDetailExcelReportEntity.getDataReportId());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setExcelReportId(thsBusinessBudgetProjectDetailExcelReportEntity.getId());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setCreateTime(cn.hutool.core.date.DateUtil.date());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setCreatorId(userProvider.get().getUserId());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntity.setSequence(thsDataReportDetailsEntity.getSequence());
            thsBusinessBudgetProjectDetailExcelReportDetailsEntities.add(thsBusinessBudgetProjectDetailExcelReportDetailsEntity);
        }

        thsBusinessBudgetProjectDetailExcelReportDetailsService.saveBatch(thsBusinessBudgetProjectDetailExcelReportDetailsEntities);
    }

    /**
     * @param detailId
     * @description: 迁移过来的一表通xls转换为Xlsx
     * @param: @param budgetProjectId
     * @return: boolean
     * @author: wxy
     * @date: 2024/8/10 9:30
     */
    @Override
    public ActionResult convertExcelReport(String budgetProjectId, String detailId) throws IOException {


        ThsBusinessBudgetProjectDetailEntity thsBusinessBudgetProjectDetail = thsBusinessBudgetProjectDetailService.getById(detailId);
        if (ObjectUtil.isEmpty(thsBusinessBudgetProjectDetail)) {
            return ActionResult.fail("记录不存在");
        }
        try {
            QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> reportEntityQueryWrapper = new QueryWrapper<>();
            reportEntityQueryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, detailId);

            List<ThsBusinessBudgetProjectDetailExcelReportEntity> reportEntityList = this.list(reportEntityQueryWrapper);

            String filePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH;
            for (ThsBusinessBudgetProjectDetailExcelReportEntity entity : reportEntityList) {

                ThsDocumectFileManagementEntity documentFileManagementEntity = thsDocumectFileManagementService.getInfo(ObjectUtil.isEmpty(entity.getExcelFileId()) ? "" : entity.getExcelFileId());
                if (ObjectUtil.isNotEmpty(documentFileManagementEntity) && documentFileManagementEntity.getFileType().equals("xls")) {
                    // xls 转换为xlsx
                    String xlsFilePath = filePath + documentFileManagementEntity.getFileSavePath() + documentFileManagementEntity.getFileName();

                    String fileName = documentFileManagementEntity.getFileName().substring(0, documentFileManagementEntity.getFileName().lastIndexOf('.')) + ".xlsx";

                    String xlsxFilePath = filePath + documentFileManagementEntity.getFileSavePath() + fileName;
                    ExcelConverterUtil.convertXlsToXlsx(xlsFilePath, xlsxFilePath);

                    documentFileManagementEntity.setFileName(fileName);
                    thsDocumectFileManagementService.updateById(documentFileManagementEntity);
                }
            }

        } catch (RuntimeException exception) {
            throw new BadRequestAlertException("convertXlsToXlsx failed", "convertXlsToXlsx failed", exception.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ActionResult.success("转换成功");
    }

    /**
     * @Description: Todo 根据用户id获取签名
     * @Param: * @param userId
     * @return: java.lang.String
     * @Author: hrj
     * @Date: 14:51 2024/6/17
     */
    public String getSignature(String userId) {
        return this.getBaseMapper().getSignimg(userId);
    }

    /**
     * 处理单个子节点的情况
     */
    private void handleSingleNode(ThsBusinessBudgetProjectDetailEntity info, String name, List<String> currentReportNames) {
        // 判断当前节点是否仅这一个单项封面
        if (!currentReportNames.contains(name) && (ObjectUtil.isEmpty(info.getHaveCover()) || info.getHaveCover() == 1)) {
            info.setHaveCover(0);
            thsBusinessBudgetProjectDetailService.updateById(info);
            if (judgeBrotherCoverStatus(info.getPid(), name)) {
                // 如果兄弟节点不含单项封面，则更新父节点状态
                // 当前节点及其父节点逐层判断是否含有封面，找到含有封面的节点则更新其状态为1
                updateAncestorCoverStatus(info.getPid(), name);
            }
        }
    }

    /**
     * 处理多个子节点的情况
     */
    private void handleMultipleNodes(ThsBusinessBudgetProjectDetailEntity info, String name, List<String> currentReportNames) {
        if (!currentReportNames.contains(name) && ObjectUtil.isNotEmpty(info.getHaveCover()) && info.getHaveCover() == 1) {
            info.setHaveCover(0);
            thsBusinessBudgetProjectDetailService.updateById(info);
        }
    }

    private boolean judgeBrotherCoverStatus(String pid, String name) {
        // 判断兄弟节点是否含有单项封面
        QueryWrapper<ThsBusinessBudgetProjectDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailEntity::getPid, pid);
        List<String> brotherDetailIds = thsBusinessBudgetProjectDetailService.list(queryWrapper).stream().map(ThsBusinessBudgetProjectDetailEntity::getId).collect(Collectors.toList());

        QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> reportEntityQueryWrapper = new QueryWrapper<>();
        reportEntityQueryWrapper.lambda().in(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, brotherDetailIds);
        List<String> brotherReportNames = this.list(reportEntityQueryWrapper).stream().map(ThsBusinessBudgetProjectDetailExcelReportEntity::getName).collect(Collectors.toList());

        return !brotherReportNames.contains(name);
    }

    /**
     * 递归更新祖先节点的封面状态
     */
    private void updateAncestorCoverStatus(String detailId, String name) {
        if (StrUtil.isNotEmpty(detailId)) {
            ThsBusinessBudgetProjectDetailEntity parentInfo = thsBusinessBudgetProjectDetailService.getInfo(detailId);
            QueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, detailId);
            List<String> parentReportNames = this.list(queryWrapper).stream().map(ThsBusinessBudgetProjectDetailExcelReportEntity::getName).collect(Collectors.toList());
            if (parentReportNames.contains(name) || ObjectUtil.isNotEmpty(parentInfo.getHaveCover())) {
                parentInfo.setHaveCover(1);
                thsBusinessBudgetProjectDetailService.updateById(parentInfo);
            } else {
                updateAncestorCoverStatus(parentInfo.getPid(), name);
            }
        }
    }

    /**
     * 复制Excel样式
     *
     * @param sourceFilePath
     * @param destinationFile
     * @return
     * <AUTHOR>
     * @date 2025/6/10 14:58
     */
    public void copyExcelWithStyle(String sourceFilePath, MultipartFile destinationFile) throws IOException {
        // 读取现有的 Excel 文件
        Workbook existingWorkbook = WorkbookFactory.create(Files.newInputStream(Paths.get(sourceFilePath)));

        // 将前端上传的 Excel 文件转换为临时文件
        File tempFile = convertMultipartFileToTempFile(destinationFile);

        // 读取前端上传的 Excel 文件
        FileInputStream fileInputStream = new FileInputStream(tempFile);
        XSSFWorkbook workbook = new XSSFWorkbook(fileInputStream);

        // 复制上传的 Excel 中的数据和样式到现有的 Excel 文件
        copyExcelDataWithStyle(workbook, existingWorkbook);

        // 计算目标 Excel 文件中的公式
        evaluateFormulas(existingWorkbook);

        // 写回现有的 Excel 文件
        try (OutputStream outputStream = new FileOutputStream(sourceFilePath)) {
            existingWorkbook.write(outputStream);
        }

        // 删除临时文件
        tempFile.delete();
    }

    /**
     * 复制数据样式（保留目标文件的原有样式，只更新数据）
     *
     * @param sourceWorkbook 源数据（新上传的文件）
     * @param destWorkbook   目标文件（原有样式的文件）
     * <AUTHOR>
     * @date 2024/4/17 9:55
     */
    private void copyExcelDataWithStyle(XSSFWorkbook sourceWorkbook, Workbook destWorkbook) {
        int numSheets = Math.min(sourceWorkbook.getNumberOfSheets(), destWorkbook.getNumberOfSheets());

        for (int i = 0; i < numSheets; i++) {
            Sheet sourceSheet = sourceWorkbook.getSheetAt(i);
            Sheet destSheet = destWorkbook.getSheetAt(i);

            if (destSheet == null) {
                continue;
            }

            // 1. 删除目标工作表的合并区域（以源文件结构为准）
            for (int j = destSheet.getNumMergedRegions() - 1; j >= 0; j--) {
                destSheet.removeMergedRegion(j);
            }

            // 2. 复制源工作表的合并区域
            for (CellRangeAddress mergedRegion : sourceSheet.getMergedRegions()) {
                destSheet.addMergedRegion(mergedRegion);
            }

            // 3. 复制行高（以源文件行高为准）
            for (int rowIndex = 0; rowIndex <= sourceSheet.getLastRowNum(); rowIndex++) {
                Row sourceRow = sourceSheet.getRow(rowIndex);
                Row destRow = destSheet.getRow(rowIndex);
                if (sourceRow != null) {
                    // 创建行（如果不存在）
                    if (destRow == null) {
                        destRow = destSheet.createRow(rowIndex);
                    }
                    // 复制行高和隐藏属性
                    destRow.setHeight(sourceRow.getHeight());
                    destRow.setZeroHeight(sourceRow.getZeroHeight());
                }
            }

            // 4. 复制列宽（以源文件列宽为准）
            int maxColumnIndex = 0;
            for (Row row : sourceSheet) {
                if (row != null && row.getLastCellNum() > maxColumnIndex) {
                    maxColumnIndex = row.getLastCellNum();
                }
            }
            for (int columnIndex = 0; columnIndex < maxColumnIndex; columnIndex++) {
                int columnWidth = sourceSheet.getColumnWidth(columnIndex);
                destSheet.setColumnWidth(columnIndex, columnWidth);
                destSheet.setColumnHidden(columnIndex, sourceSheet.isColumnHidden(columnIndex));
            }

            // 5. 复制单元格数据（保留目标文件的样式）
            for (Row sourceRow : sourceSheet) {
                int rowNum = sourceRow.getRowNum();
                Row destRow = destSheet.getRow(rowNum);
                if (destRow == null) {
                    destRow = destSheet.createRow(rowNum);
                }

                for (Cell sourceCell : sourceRow) {
                    int colNum = sourceCell.getColumnIndex();
                    Cell destCell = destRow.getCell(colNum);
                    if (destCell == null) {
                        destCell = destRow.createCell(colNum);
                    }

                    // 保存目标单元格的原有样式
                    CellStyle originalStyle = destCell.getCellStyle();

                    // 仅复制源单元格的数据
                    copyOnlyData(sourceCell, destCell);

                    // 恢复目标单元格的原有样式
                    destCell.setCellStyle(originalStyle);
                }
            }
        }
    }

    /**
     * 评估公式
     *
     * @param workbook
     * <AUTHOR>
     * @date 2024/4/18 11:15
     */
    private void evaluateFormulas(Workbook workbook) {
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        for (Sheet sheet : workbook) {
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if (cell.getCellType() == CellType.FORMULA) {
                        evaluator.evaluateFormulaCell(cell);
                    }
                }
            }
        }
    }


    /**
     * 获取或创建工作表
     *
     * @param workbook
     * @param sheetName
     * @return {@link Sheet}
     * <AUTHOR>
     * @date 2024/4/17 9:56
     */
    private Sheet getOrCreateSheet(Workbook workbook, String sheetName) {
        Sheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
            sheet = workbook.createSheet(sheetName);
        }
        return sheet;
    }

    /**
     * 获取或创建行
     *
     * @param sheet
     * @param rowIndex
     * @return {@link Row}
     * <AUTHOR>
     * @date 2024/4/17 9:56
     */
    private Row getOrCreateRow(Sheet sheet, int rowIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        return row;
    }

    /**
     * 获取或创建单元格
     *
     * @param row
     * @param columnIndex
     * @return {@link Cell}
     * <AUTHOR>
     * @date 2024/4/17 9:55
     */
    private Cell getOrCreateCell(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        return cell;
    }

    /**
     * 复制单元格（包含数据和样式）
     *
     * @param sourceCell
     * @param destCell
     * @param destWorkbook
     * <AUTHOR>
     * @date 2024/4/17 9:56
     */
    private void copyCell(Cell sourceCell, Cell destCell, Workbook destWorkbook) {
        // 复制单元格值
        switch (sourceCell.getCellType()) {
            case STRING:
                destCell.setCellValue(sourceCell.getRichStringCellValue());
                break;
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(sourceCell)) {
                    destCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    destCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                destCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                try {
                    destCell.setCellFormula(sourceCell.getCellFormula());
                } catch (Exception e) {
                    // 如果公式复制失败，则复制计算后的值
                    try {
                        destCell.setCellValue(sourceCell.getNumericCellValue());
                    } catch (Exception ex) {
                        destCell.setCellValue(sourceCell.getStringCellValue());
                    }
                }
                break;
            case BLANK:
                destCell.setBlank();
                break;
            default:
                // 处理其他类型
                try {
                    destCell.setCellValue(sourceCell.getStringCellValue());
                } catch (Exception e) {
                    destCell.setBlank();
                }
                break;
        }

        // 复制单元格样式（包括边框、字体、背景色等）
        if (sourceCell.getCellStyle() != null) {
            CellStyle newCellStyle = destWorkbook.createCellStyle();
            newCellStyle.cloneStyleFrom(sourceCell.getCellStyle());
            destCell.setCellStyle(newCellStyle);
        }
    }

    /**
     * 只复制单元格数据，不复制样式
     *
     * @param sourceCell 源单元格
     * @param destCell   目标单元格
     * <AUTHOR>
     * @date 2024/4/17 9:56
     */
    private void copyOnlyData(Cell sourceCell, Cell destCell) {
        switch (sourceCell.getCellType()) {
            case STRING:
                destCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(sourceCell)) {
                    destCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    destCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                destCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                try {
                    destCell.setCellFormula(sourceCell.getCellFormula());
                } catch (Exception e) {
                    // 如果公式复制失败，则复制计算后的值
                    try {
                        destCell.setCellValue(sourceCell.getNumericCellValue());
                    } catch (Exception ex) {
                        destCell.setCellValue(sourceCell.getStringCellValue());
                    }
                }
                break;
            case BLANK:
                destCell.setBlank();
                break;
            default:
                // 处理其他类型
                try {
                    destCell.setCellValue(sourceCell.getStringCellValue());
                } catch (Exception e) {
                    destCell.setBlank();
                }
                break;
        }
    }

    /**
     * 将多部分文件转换为临时文件
     *
     * @param multipartFile
     * @return {@link File}
     * <AUTHOR>
     * @date 2024/4/17 9:56
     */
    private File convertMultipartFileToTempFile(MultipartFile multipartFile) throws IOException {
        File tempFile = File.createTempFile("temp", ".xlsx");
        try (OutputStream outputStream = new FileOutputStream(tempFile)) {
            outputStream.write(multipartFile.getBytes());
        }
        return tempFile;
    }


    /**
     * @param reportId:
     * @param detailId:
     * @param budgetProjectId:
     * @return void
     * <AUTHOR>
     * @description 使用easyExcel预览报表
     * @date 2024/8/10 9:50
     */
    @Override
    public void preview(String reportId, String detailId, String budgetProjectId) throws IOException {
        HttpServletResponse response = ServletUtil.getResponse();

        // 1、根据reportId获取报表记录
        ThsBusinessBudgetProjectDetailExcelReportEntity excelReportEntity = this.getInfo(reportId);

        // 2、根据excelFileId获取对应的模板文件
        ThsDocumectFileManagementEntity documentFileManagement = thsDocumectFileManagementService.getInfo(excelReportEntity.getExcelFileId());

        String templateFileName = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagement.getFileSavePath() + documentFileManagement.getFileName();

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(excelReportEntity.getName() + cn.hutool.core.util.RandomUtil.randomNumbers(4) + ".xlsx", "UTF-8"));

        // 3、根据数据源读取数据
        List<ThsTemplateReportCategoryFileSourceEntity> sourceEntities = thsTemplateReportCategoryFileSourceService.getListByFileId(excelReportEntity.getDataReportId());

        // 3.1 普通变量
        List<ThsTemplateReportCategoryFileSourceEntity> mapVarSource = sourceEntities.stream().filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_SQL) && s.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_MAP)).collect(Collectors.toList());
        HashMap<String, Object> varMap = new HashMap<>();

        for (ThsTemplateReportCategoryFileSourceEntity source : mapVarSource) {
            String sqlText = sqlReplaceParam(detailId, budgetProjectId, source.getSqlText());
            List<Map<String, Object>> mapList = executeSqlUtils.executeQuerySqlReturnASName(sqlText);
            fieldAddSourceName(source, mapList);
            if (!mapList.isEmpty()) {
                varMap.putAll(mapList.get(0));
            }
        }
        varMap.put("日期", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        ThsReportSysSettingEntity reportSysSettingEntity = thsReportSysSettingService.getByItemNameAndStatus("资质证号", 1);
        if (ObjectUtil.isNotEmpty(reportSysSettingEntity)) {
            varMap.put("资质证号", reportSysSettingEntity.getItemValue());
        }

        // 3.2 列表变量
        List<ThsTemplateReportCategoryFileSourceEntity> listVarSource = sourceEntities.stream()
                .filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_SQL) && s.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_LIST))
                .sorted(Comparator.comparing(ThsTemplateReportCategoryFileSourceEntity::getCreateTime)).collect(Collectors.toList());

        // 3.3 api接口变量
        List<ThsTemplateReportCategoryFileSourceEntity> apiVarSource = sourceEntities.stream().filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_API)).collect(Collectors.toList());

        // 3.4 根据报表名称查询对应的人员 加入签名图片
        ThsBusinessBudgetProjectDetailEntity thsBusinessBudgetProjectDetail = thsBusinessBudgetProjectDetailService.getById(detailId);
        if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectDetail.getStatus()) && ObjectUtil.equals(thsBusinessBudgetProjectDetail.getStatus(), 3)) {
            addSignatureByReportName(excelReportEntity.getName(), reportId, detailId, budgetProjectId, varMap);
        }

        // 4、往模板写入数据
        ExcelWriterBuilder writerBuilder = EasyExcel.write(response.getOutputStream()).registerWriteHandler(new ExcelImageModifyHandler()).withTemplate(templateFileName);
        // 根据条件注册处理器
        if (Arrays.asList("造价汇总表", "编制说明").contains(excelReportEntity.getName())) {
            writerBuilder.registerWriteHandler(new AutoRowHeightHandler());//.registerWriteHandler(new AutoColumnWidthHandler());
        }

        // 构建并使用 ExcelWriter
        try (ExcelWriter excelWriter = writerBuilder.build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 自动向下填充
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 填充变量
            for (int i = 0; i < listVarSource.size(); i++) {
                ThsTemplateReportCategoryFileSourceEntity source = listVarSource.get(i);
                String sqlText = sqlReplaceParam(detailId, budgetProjectId, source.getSqlText());
                List<Map<String, Object>> mapList = executeSqlUtils.executeQuerySqlReturnASName(sqlText);
                int startIndex = 1;
                for (int j = 0; j < mapList.size(); j++) {
                    mapList.get(j).put("序号", String.valueOf(startIndex + j));
                    mapList.get(j).put("边框占位符", " ");
                }

                excelWriter.fill(new FillWrapper(source.getName(), mapList), fillConfig, writeSheet);
            }

            // 填充api接口变量
            for (ThsTemplateReportCategoryFileSourceEntity source : apiVarSource) {
                String functionName = source.getApiFuncName();
                try {
                    // 检查 functionName 是否为 null
                    if (functionName == null) {
                        throw new RuntimeException("方法名称不能为空");
                    }
                    // 检查 thsBusinessProjectDetailUtil 是否为 null
                    if (thsBusinessProjectDetailUtil == null) {
                        throw new RuntimeException("thsBusinessProjectDetailUtil 对象不能为空");
                    }
                    // 根据方法名称调用不同的方法 获取thsBusinessProjectDetailUtil类中名为functionName的方法
                    Method method = thsBusinessProjectDetailUtil.getClass().getMethod(functionName, String.class, String.class);

                    if (source.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_LIST)) {
                        // 调用该方法
                        List<Map<String, Object>> apiList = (List<Map<String, Object>>) method.invoke(thsBusinessProjectDetailUtil, detailId, budgetProjectId);

                        excelWriter.fill(new FillWrapper(source.getName(), apiList), fillConfig, writeSheet);
                    } else {
                        Map<String, Object> objectMap = (Map<String, Object>) method.invoke(thsBusinessProjectDetailUtil, detailId, budgetProjectId);
                        varMap.putAll(objectMap);
                    }
                } catch (NoSuchMethodException e) {
                    throw new RuntimeException(source.getName() + "对应的方法： " + functionName + " 不存在");
                } catch (NullPointerException e) {
                    throw new RuntimeException("调用方法 " + functionName + " 时发生空指针异常", e);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("调用方法 " + functionName + " 时发生错误", e);
                }

            }
            // 填充普通变量
            excelWriter.fill(varMap, fillConfig, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("构建 ExcelWriter 异常:{}", e.getMessage(), e);
        }

    }

    /**
     * @param reportName:
     * @param varMap:
     * @return void
     * <AUTHOR>
     * @description 根据报表名称添加签名图片
     * @date 2024/8/19 11:57
     */
    private void addSignatureByReportName(String reportName, String reportId, String detailId, String budgetProjectId, HashMap<String, Object> varMap) {

        if (reportName.contains("总封面") || reportName.contains("单项封面")) {
            Map<String, Object> data = (Map<String, Object>) this.generalCoverSignature(budgetProjectId, detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                List<String> bsrList = (List<String>) data.get("bsr");
                WriteCellData<Void> voidWriteCellData1 = imageCells(bsrList);
                varMap.put("bsr", voidWriteCellData1);
                List<String> hzrList = (List<String>) data.get("hzr");
                WriteCellData<Void> voidWriteCellData2 = imageCells(hzrList);
                varMap.put("hzr", voidWriteCellData2);
                List<String> zgList = (List<String>) data.get("zg");
                WriteCellData<Void> voidWriteCellData3 = imageCells(zgList);
                varMap.put("zg", voidWriteCellData3);
            }
        } else if (reportName.equals("工程量清单")) {
            Map<String, Object> data = (Map<String, Object>) this.signatureOfBillOfQuantities(budgetProjectId, detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                List<String> bsrList = (List<String>) data.get("bsr");
                WriteCellData<Void> voidWriteCellData1 = imageCells(bsrList);
                varMap.put("bzr", voidWriteCellData1);
                List<String> hzrList = (List<String>) data.get("hzr");
                WriteCellData<Void> voidWriteCellData2 = imageCells(hzrList);
                varMap.put("hzr", voidWriteCellData2);
            }
        } else if (reportName.contains("造价汇总表")) {
            Map<String, Object> data = (Map<String, Object>) this.signatureOfCostSummaryTable(budgetProjectId, detailId, reportId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                List<String> zbrList = (List<String>) data.get("zbr");
                WriteCellData<Void> voidWriteCellData1 = imageCells(zbrList);
                varMap.put("zbr", voidWriteCellData1);
                List<String> jhrList = (List<String>) data.get("fhr");
                WriteCellData<Void> voidWriteCellData2 = imageCells(jhrList);
                varMap.put("fhr", voidWriteCellData2);
                List<String> zrrList = (List<String>) data.get("zrr");
                WriteCellData<Void> voidWriteCellData3 = imageCells(zrrList);
                varMap.put("zrr", voidWriteCellData3);
            }
        } else if (reportName.contains("小封面")) {
            Map<String, Object> data = (Map<String, Object>) this.smallCoverSignature(detailId).getData();
            if (ObjectUtil.isNotEmpty(data)) {
                List<String> shrList = (List<String>) data.get("shr");
                WriteCellData<Void> voidWriteCellData1 = imageCells(shrList);
                varMap.put("shr", voidWriteCellData1);
                List<String> jhrList = (List<String>) data.get("jhr");
                WriteCellData<Void> voidWriteCellData2 = imageCells(jhrList);
                varMap.put("jhr", voidWriteCellData2);
                List<String> hzrList = (List<String>) data.get("hzr");
                WriteCellData<Void> voidWriteCellData3 = imageCells(hzrList);
                varMap.put("hzr", voidWriteCellData3);
                List<String> bzrList = (List<String>) data.get("bzr");
                WriteCellData<Void> voidWriteCellData4 = imageCells(bzrList);
                varMap.put("bzr", voidWriteCellData4);
            }
        }


    }

    /**
     * @param detailId:
     * @param budgetProjectId:
     * @param sql:
     * @return String
     * <AUTHOR>
     * @description sql参数替换
     * @date 2024/8/5 15:31
     */
    private String sqlReplaceParam(String detailId, String budgetProjectId, String sql) {
        if (sql.contains("${detailId}")) {
            sql = sql.replace("${detailId}", "'" + detailId + "'");
        } else if (sql.contains("${budgetProjectId}")) {
            sql = sql.replace("${budgetProjectId}", "'" + budgetProjectId + "'");
        }
        return sql;
    }

    /**
     * @param mapList:
     * <AUTHOR>
     * @description 字段添加来源名称，例如拼接成：{数据来源_字段名} {数据来源.字段名}
     * @date 2024/8/8 19:35
     */
    private void fieldAddSourceName(ThsTemplateReportCategoryFileSourceEntity source, List<Map<String, Object>> mapList) {
        String sourceName = source.getName();
        String separator = "_";

        if (ObjectUtil.isNotEmpty(source.getDataSourceType())) {
            mapList.forEach(map -> {
                // 创建一个新的 Map 来存储修改后的键值对
                Map<String, Object> newMap = new HashMap<>();
                map.forEach((key, value) -> {
                    String newKey = sourceName + separator + key;
                    newMap.put(newKey, value);
                });
                // 将原 map 替换为新的 map
                map.clear();
                map.putAll(newMap);
            });
        }
    }

    /**
     * @param reportForm:
     * @return String
     * <AUTHOR>
     * @description 每个工程节点从系统模板中添加对应的模板文件
     * @date 2024/8/9 11:43
     */
    @Override
    public String createReportFormTemplateFile(ThsBusinessBudgetProjectDetailExcelReportForm reportForm) {
        ThsTemplateReportCategoryFileEntity templateReportFile = thsTemplateReportCategoryFileService.getInfo(reportForm.getDataReportId());
        if (templateReportFile != null) {
            ThsDocumectFileManagementEntity document = thsDocumectFileManagementService.getInfo(templateReportFile.getFileId());
            String templateFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + document.getFileSavePath() + document.getFileName();
            File file = new File(templateFilePath);
            ThsDocumectFileManagementEntity documentFileManagement = thsFileUtil.uploadFile(FileUtil.readBytes(file), templateReportFile.getName() + "." + document.getFileType(), document.getFileType(), BUDGET_FILES_ACHIEVEMENT_FILES_PATH + reportForm.getDetailId() + "/", templateReportFile.getName() + " 模板");

            return documentFileManagement.getId();
        }
        return "";
    }

    /**
     * 填充图片：将多个图片水平拼接成一张图片
     *
     * @param byteList
     * @return {@link WriteCellData< Void>}
     * <AUTHOR>
     * @date 2025/7/29 17:28
     */
    public WriteCellData<Void> imageCells(List<String> byteList) {
        if (byteList == null || byteList.isEmpty()) {
            return new WriteCellData<>();
        }

        // 过滤出有效的图片
        List<String> validImages = byteList.stream()
                .filter(img -> img != null && img.length() > 0)
                .collect(Collectors.toList());

        if (validImages.isEmpty()) {
            return new WriteCellData<>();
        }

        try {
            String mergedImageBase64;

            // 如果只有一张图片，统一缩放到合适尺寸
            if (validImages.size() == 1) {
                String imageBase64 = validImages.get(0);
                String cleanBase64 = imageBase64.replaceAll("data:image/png;base64,", "")
                        .replaceAll("data:image/jpeg;base64,", "");

                // 统一缩放到合适尺寸，确保预览和打印一致
                mergedImageBase64 = preprocessSignatureImage(cleanBase64);
                log.debug("单张图片统一预处理完成");
            } else {
                // 多张图片需要拼接
                log.debug("开始拼接{}张图片", validImages.size());
                mergedImageBase64 = mergeImagesHorizontally(validImages);
                log.debug("拼接完成，结果长度：{}", mergedImageBase64.length());
            }

            ImageData imageData = new ImageData();
            imageData.setImage(Base64.getDecoder().decode(mergedImageBase64));

            // 设置图片的边距，控制图片在单元格中的显示大小
            // 边距设置类似CSS的margin，值越小图片显示越大
            // 对于拼接后的图片，使用0边距让图片完全填满占据的单元格范围
            int margin = 0; // 设置0边距，让拼接后的图片完全填满单元格

            imageData.setTop(margin);      // 上边距
            imageData.setRight(margin);    // 右边距
            imageData.setBottom(margin);   // 下边距
            imageData.setLeft(margin);     // 左边距

            // 不设置单元格范围，让图片按原始尺寸显示
            // 移除强制的单元格范围设置，避免图片被拉伸

            List<ImageData> imageDataList = new ArrayList<>();
            imageDataList.add(imageData);

            WriteCellData<Void> writeCellData = new WriteCellData<>();
            writeCellData.setImageDataList(imageDataList);
            writeCellData.setType(CellDataTypeEnum.STRING);

            return writeCellData;

        } catch (Exception e) {
            log.error("填充图片异常，图片数量：{}，异常信息：{}", validImages.size(), e.getMessage(), e);
            // 如果拼接失败，使用原来的逻辑（会重叠，但至少能显示）
            return createFallbackImageCell(validImages.get(0));
        }
    }

    /**
     * 将多个base64图片拼接成一张图片，支持自动换行
     * 每行最多8个签名，超过的自动换到下一行，保持原始图片尺寸
     *
     * @param imageBase64List base64图片列表
     * @return {@link String} 拼接后的base64图片
     * <AUTHOR>
     * @date 2025/7/29 17:29
     */
    private String mergeImagesHorizontally(List<String> imageBase64List) throws Exception {
        log.info("开始拼接图片 - 输入图片数量: {}", imageBase64List.size());
        List<BufferedImage> images = new ArrayList<>();

        // 解码所有图片，统一预处理到合适尺寸
        for (int i = 0; i < imageBase64List.size(); i++) {
            String base64 = imageBase64List.get(i);
            String cleanBase64 = base64.replaceAll("data:image/png;base64,", "")
                    .replaceAll("data:image/jpeg;base64,", "");

            // 统一预处理每张图片
            String processedBase64 = preprocessSignatureImage(cleanBase64);
            byte[] imageBytes = Base64.getDecoder().decode(processedBase64);
            BufferedImage processedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));

            if (processedImage != null) {
                log.debug("处理第{}张图片 - 预处理后尺寸: {}x{}", i + 1, processedImage.getWidth(), processedImage.getHeight());
                images.add(processedImage);
            }
        }

        if (images.isEmpty()) {
            throw new Exception("No valid images to merge");
        }

        // 配置参数
        int maxImagesPerRow = 8;  // 每行最多8个签名
        int horizontalSpacing = 3; // 水平间距
        int verticalSpacing = 3;   // 垂直间距（减少换行间隙）

        log.debug("拼接参数 - 垂直间距: {}, 水平间距: {}", verticalSpacing, horizontalSpacing);

        // 计算布局
        int rows = (int) Math.ceil((double) images.size() / maxImagesPerRow);
        log.debug("拼接布局计算 - 图片总数: {}, 每行最多: {}, 计算行数: {}", images.size(), maxImagesPerRow, rows);

        // 计算每行的最大宽度和总高度
        int maxRowWidth = 0;
        int totalHeight = 0;

        for (int row = 0; row < rows; row++) {
            int startIdx = row * maxImagesPerRow;
            int endIdx = Math.min(startIdx + maxImagesPerRow, images.size());

            int rowWidth = 0;
            int rowHeight = 0;

            for (int i = startIdx; i < endIdx; i++) {
                BufferedImage img = images.get(i);
                rowWidth += img.getWidth();
                if (i < endIdx - 1) rowWidth += horizontalSpacing; // 添加间距
                rowHeight = Math.max(rowHeight, img.getHeight());
            }

            maxRowWidth = Math.max(maxRowWidth, rowWidth);
            totalHeight += rowHeight;
            if (row < rows - 1) totalHeight += verticalSpacing; // 添加行间距
        }

        // 创建合并后的图片
        BufferedImage mergedImage = new BufferedImage(maxRowWidth, totalHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = mergedImage.createGraphics();

        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制图片
        int currentY = 0;

        for (int row = 0; row < rows; row++) {
            int startIdx = row * maxImagesPerRow;
            int endIdx = Math.min(startIdx + maxImagesPerRow, images.size());

            // 计算当前行的高度
            int rowHeight = 0;
            for (int i = startIdx; i < endIdx; i++) {
                rowHeight = Math.max(rowHeight, images.get(i).getHeight());
            }

            // 绘制当前行的图片
            int currentX = 0;
            for (int i = startIdx; i < endIdx; i++) {
                BufferedImage img = images.get(i);
                // 垂直居中对齐
                int y = currentY + (rowHeight - img.getHeight()) / 2;
                g2d.drawImage(img, currentX, y, null);
                currentX += img.getWidth() + horizontalSpacing;
            }

            currentY += rowHeight + verticalSpacing;
        }

        g2d.dispose();

        // 转换为base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(mergedImage, "PNG", baos);
        byte[] mergedBytes = baos.toByteArray();

        log.info("拼接完成 - 最终图片尺寸: {}x{}, 数据长度: {}", mergedImage.getWidth(), mergedImage.getHeight(), mergedBytes.length);

        return Base64.getEncoder().encodeToString(mergedBytes);
    }

    /**
     * 统一预处理签名图片 - 确保预览和打印一致
     *
     * @param base64Image
     * @return {@link String}
     * <AUTHOR>
     * @date 2025/7/30 17:09
     */
    private String preprocessSignatureImage(String base64Image) throws Exception {
        byte[] imageBytes = Base64.getDecoder().decode(base64Image);
        BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));

        if (originalImage == null) {
            return base64Image;
        }

        // 统一缩放到固定尺寸，确保所有图片高度一致
        int targetWidth = 100;  // 目标宽度
        int targetHeight = 50;  // 固定目标高度，确保拼接时行高一致

        BufferedImage resizedImage = resizeImageToFixedSize(originalImage, targetWidth, targetHeight);

        // 转换回base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(resizedImage, "PNG", baos);
        String processedBase64 = Base64.getEncoder().encodeToString(baos.toByteArray());

        log.debug("图片预处理完成 - 原始: {}x{}, 处理后: {}x{}", originalImage.getWidth(), originalImage.getHeight(), resizedImage.getWidth(), resizedImage.getHeight());

        return processedBase64;
    }

    /**
     * 等比缩放图片，保持宽高比，不超过最大尺寸
     *
     * @param originalImage 原始图片
     * @param maxWidth      最大宽度
     * @param maxHeight     最大高度
     * @return 缩放后的图片
     */
    private BufferedImage resizeImageProportionally(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 计算缩放比例，取较小的比例以确保不超过最大尺寸
        double widthRatio = (double) maxWidth / originalWidth;
        double heightRatio = (double) maxHeight / originalHeight;
        double scaleFactor = Math.min(widthRatio, heightRatio);

        // 如果图片已经小于最大尺寸，不需要缩放
        if (scaleFactor >= 1.0) {
            return originalImage;
        }

        // 计算缩放后的尺寸
        int targetWidth = (int) (originalWidth * scaleFactor);
        int targetHeight = (int) (originalHeight * scaleFactor);

        // 创建目标尺寸的图片
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = resizedImage.createGraphics();

        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制缩放后的图片
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 创建备用的图片单元格（当拼接失败时使用）
     *
     * @param imageBase64 图片base64
     * @return {@link WriteCellData< Void>}
     * <AUTHOR>
     * @date 2025/7/29 17:28
     */
    private WriteCellData<Void> createFallbackImageCell(String imageBase64) {
        try {
            String cleanBase64 = imageBase64.replaceAll("data:image/png;base64,", "")
                    .replaceAll("data:image/jpeg;base64,", "");

            // 统一预处理图片
            String processedBase64 = preprocessSignatureImage(cleanBase64);
            byte[] resizedImageBytes = Base64.getDecoder().decode(processedBase64);

            ImageData imageData = new ImageData();
            imageData.setImage(resizedImageBytes);

            // 为单张图片设置边距，让图片显示得更大
            int margin = 0; // 设置0边距，让图片完全填满单元格

            imageData.setTop(margin);      // 上边距
            imageData.setRight(margin);    // 右边距
            imageData.setBottom(margin);   // 下边距
            imageData.setLeft(margin);     // 左边距

            List<ImageData> imageDataList = new ArrayList<>();
            imageDataList.add(imageData);

            WriteCellData<Void> writeCellData = new WriteCellData<>();
            writeCellData.setImageDataList(imageDataList);
            writeCellData.setType(CellDataTypeEnum.STRING);

            return writeCellData;
        } catch (Exception e) {
            return new WriteCellData<>();
        }
    }


    /**
     * 将图片缩放到固定尺寸（用于拼接时保持一致的高度）
     *
     * @param originalImage
     * @param targetWidth
     * @param targetHeight
     * @return {@link BufferedImage}
     * <AUTHOR>
     * @date 2025/7/30 17:10
     */
    private BufferedImage resizeImageToFixedSize(BufferedImage originalImage, int targetWidth, int targetHeight) {
        // 先等比缩放到合适大小
        BufferedImage scaledImage = resizeImageProportionally(originalImage, targetWidth, targetHeight);

        // 创建固定尺寸的画布
        BufferedImage fixedSizeImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = fixedSizeImage.createGraphics();

        // 设置透明背景
        g2d.setComposite(AlphaComposite.Clear);
        g2d.fillRect(0, 0, targetWidth, targetHeight);
        g2d.setComposite(AlphaComposite.SrcOver);

        // 将缩放后的图片居中绘制到固定尺寸画布上
        int x = (targetWidth - scaledImage.getWidth()) / 2;
        int y = (targetHeight - scaledImage.getHeight()) / 2;
        g2d.drawImage(scaledImage, x, y, null);
        g2d.dispose();

        return fixedSizeImage;
    }

    /**
     * @param detailId:
     * @param reportId:
     * @param budgetProjectId:
     * @return void
     * <AUTHOR>
     * @description 报表模板文件归档，转成pdf文件
     * @date 2024/8/15 10:26
     */
    @Override
    public void templateFileArchiving(String detailId, String reportId, String budgetProjectId) throws Exception {

        ThsBusinessBudgetProjectDetailExcelReportEntity info = this.getInfo(reportId);
        if (info == null) {
            throw new Exception("报表不存在");
        }
        UserInfo userInfo = userProvider.get();
        // 更新归档状态
        info.setIsArchived(1);

        String pdfPath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + detailId + "/" + UUID.randomUUID() + ".pdf";

        String tempFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + BUDGET_FILES_ACHIEVEMENT_FILES_PATH + detailId + "/temp.xlsx";


        // 加载业务数据填充到报表, 存为临时excel文件
        previewBeforeArchiving(reportId, detailId, budgetProjectId, tempFilePath);
        // 将excel转为pdf
        if (info.getName().equals("项目归档统计表")) {
            // 横向分四页打印
            AsposeToPdfUtil.trans(tempFilePath, pdfPath, "excel");
        } else {
            ExcelToPdfUtil.excelToPdf(tempFilePath, pdfPath);
        }


        File file = new File(pdfPath);

        if (file.exists()) {
            String archivedFileId = saveThsDocumectFileManagement(userInfo, file.getName(), BUDGET_FILES_ACHIEVEMENT_FILES_PATH + detailId + "/", reportId + "归档", "pdf", file.length());
            info.setArchivedFileId(archivedFileId);
        }

        FileUtil.del(tempFilePath);

        this.updateById(info);
    }

    /**
     * @param reportId:
     * @param detailId:
     * @param budgetProjectId:
     * @param sourceFilePath:
     * @return void
     * <AUTHOR>
     * @description 归档时加载业务数据
     * @date 2024/8/16 16:15
     */
    public void previewBeforeArchiving(String reportId, String detailId, String budgetProjectId, String sourceFilePath) throws IOException {


        // 1、根据reportId获取报表记录
        ThsBusinessBudgetProjectDetailExcelReportEntity excelReportEntity = this.getInfo(reportId);

        // 2、根据excelFileId获取对应的模板文件
        ThsDocumectFileManagementEntity documentFileManagement = thsDocumectFileManagementService.getInfo(excelReportEntity.getExcelFileId());

        String templateFileName = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + documentFileManagement.getFileSavePath() + documentFileManagement.getFileName();


        // 3、根据数据源读取数据
        List<ThsTemplateReportCategoryFileSourceEntity> sourceEntities = thsTemplateReportCategoryFileSourceService.getListByFileId(excelReportEntity.getDataReportId());

        // 3.1 普通变量
        List<ThsTemplateReportCategoryFileSourceEntity> mapVarSource = sourceEntities.stream().filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_SQL) && s.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_MAP)).collect(Collectors.toList());

        HashMap<String, Object> varMap = new HashMap<>();

        for (ThsTemplateReportCategoryFileSourceEntity source : mapVarSource) {
            String sqlText = sqlReplaceParam(detailId, budgetProjectId, source.getSqlText());
            List<Map<String, Object>> mapList = executeSqlUtils.executeQuerySqlReturnASName(sqlText);
            fieldAddSourceName(source, mapList);

            if (!mapList.isEmpty()) {
                varMap.putAll(mapList.get(0));
            }
            // 去重
        }
        varMap.put("日期", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        ThsReportSysSettingEntity reportSysSettingEntity = thsReportSysSettingService.getByItemNameAndStatus("资质证号", 1);
        if (ObjectUtil.isNotEmpty(reportSysSettingEntity)) {
            varMap.put("资质证号", reportSysSettingEntity.getItemValue());
        }

        // 3.2 列表变量
        List<ThsTemplateReportCategoryFileSourceEntity> listVarSource = sourceEntities.stream()
                .filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_SQL) && s.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_LIST))
                .sorted(Comparator.comparing(ThsTemplateReportCategoryFileSourceEntity::getCreateTime)).collect(Collectors.toList());

        // 3.3 api接口变量
        List<ThsTemplateReportCategoryFileSourceEntity> apiVarSource = sourceEntities.stream().filter(s -> s.getQueryMethod().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_API)).collect(Collectors.toList());

        // 3.4 根据报表名称查询对应的人员 加入签名图片
        ThsBusinessBudgetProjectDetailEntity thsBusinessBudgetProjectDetail = thsBusinessBudgetProjectDetailService.getById(detailId);
        if (ObjectUtil.isNotEmpty(thsBusinessBudgetProjectDetail.getStatus()) && ObjectUtil.equals(thsBusinessBudgetProjectDetail.getStatus(), 3)) {
            addSignatureByReportName(excelReportEntity.getName(), reportId, detailId, budgetProjectId, varMap);
        }

        // 4、往模板写入数据
        try (ExcelWriter excelWriter = EasyExcel.write(sourceFilePath).registerWriteHandler(new ExcelImageModifyHandler()).withTemplate(templateFileName).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 自动向下填充
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 填充变量
            for (int i = 0; i < listVarSource.size(); i++) {
                ThsTemplateReportCategoryFileSourceEntity source = listVarSource.get(i);
                String sqlText = sqlReplaceParam(detailId, budgetProjectId, source.getSqlText());
                List<Map<String, Object>> mapList = executeSqlUtils.executeQuerySqlReturnASName(sqlText);
                int startIndex = 1;
                for (int j = 0; j < mapList.size(); j++) {
                    // 生成序号
                    mapList.get(j).put("序号", String.valueOf(startIndex + j));
                    mapList.get(j).put("边框占位符", " ");
                }

                excelWriter.fill(new FillWrapper(source.getName(), mapList), fillConfig, writeSheet);
            }

            // 填充api接口变量
            for (ThsTemplateReportCategoryFileSourceEntity source : apiVarSource) {
                String functionName = source.getApiFuncName();
                try {
                    // 检查 functionName 是否为 null
                    if (functionName == null) {
                        throw new RuntimeException("方法名称不能为空");
                    }
                    // 检查 thsBusinessProjectDetailUtil 是否为 null
                    if (thsBusinessProjectDetailUtil == null) {
                        throw new RuntimeException("thsBusinessProjectDetailUtil 对象不能为空");
                    }
                    // 根据方法名称调用不同的方法 获取thsBusinessProjectDetailUtil类中名为functionName的方法
                    Method method = thsBusinessProjectDetailUtil.getClass().getMethod(functionName, String.class, String.class);

                    if (source.getDataSourceType().equals(THS_TEMPLATE_DATA_SOURCE_TYPE_LIST)) {
                        // 调用该方法
                        List<Map<String, Object>> apiList = (List<Map<String, Object>>) method.invoke(thsBusinessProjectDetailUtil, detailId, budgetProjectId);

                        excelWriter.fill(new FillWrapper(source.getName(), apiList), fillConfig, writeSheet);
                    } else {
                        Map<String, Object> objectMap = (Map<String, Object>) method.invoke(thsBusinessProjectDetailUtil, detailId, budgetProjectId);
                        varMap.putAll(objectMap);
                    }
                } catch (NoSuchMethodException e) {
                    throw new RuntimeException(source.getName() + "对应的方法： " + functionName + " 不存在");
                } catch (NullPointerException e) {
                    throw new RuntimeException("调用方法 " + functionName + " 时发生空指针异常", e);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("调用方法 " + functionName + " 时发生错误", e);
                }

            }
            // 填充普通变量
            excelWriter.fill(varMap, fillConfig, writeSheet);
            excelWriter.finish();
        }
    }


    /**
     * 根据条件查询
     *
     * @param detailId
     * @return {@link List< ThsBusinessBudgetProjectDetailExcelReportEntity>}
     * <AUTHOR>
     * @date 2025/5/15 17:16
     */
    @Override
    public List<ThsBusinessBudgetProjectDetailExcelReportEntity> findListByDetailIds(Collection<String> detailIds) {
        LambdaQueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, detailIds);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 复制报表
     *
     * @param reportForm
     * @return {@link String}
     * <AUTHOR>
     * @date 2025/5/15 18:49
     */
    @Override
    public String copyReportFormTemplateFile(ThsBusinessBudgetProjectDetailExcelReportForm reportForm) {
        // 1、根据reportId获取报表记录
        ThsBusinessBudgetProjectDetailExcelReportEntity excelReportEntity = this.getById(reportForm.getDataReportId());
        if (excelReportEntity != null) {
            // 2、根据excelFileId获取对应的模板文件
            ThsDocumectFileManagementEntity document = thsDocumectFileManagementService.getInfo(excelReportEntity.getExcelFileId());
            //  3、获取模板信息
            ThsTemplateReportCategoryFileEntity templateReportFile = thsTemplateReportCategoryFileService.getInfo(excelReportEntity.getDataReportId());
            String templateFilePath = BUDGET_FILES_ACHIEVEMENT_ROOT_PATH + document.getFileSavePath() + document.getFileName();
            File file = new File(templateFilePath);
            ThsDocumectFileManagementEntity documentFileManagement = thsFileUtil.uploadFile(FileUtil.readBytes(file), templateReportFile.getName() + "." + document.getFileType(), document.getFileType(), BUDGET_FILES_ACHIEVEMENT_FILES_PATH + reportForm.getDetailId() + "/", templateReportFile.getName() + " 模板");
            return documentFileManagement.getId();
        }
        return "";
    }

    /**
     * 获取报表编辑后的文件
     *
     * @param reportId
     * <AUTHOR>
     * @date 2025/6/27 15:12
     */
    @Override
    public void downloadEditFile(String reportId) {
        HttpServletResponse response = ServletUtil.getResponse();

        ThsBusinessBudgetProjectDetailExcelReportEntity info = this.getInfo(reportId);

        if (info == null) {
            throw new DataException("报表不存在");
        }

        if (info.getEditFile() == null) {
            throw new DataException("编辑文件不存在");
        }

        try {
            // 获取字节数据
            byte[] fileBytes = info.getEditFile();

            // 设置响应头
            String fileName = info.getName() + "_编辑版.xlsx";
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");
            response.setContentLength(fileBytes.length);

            // 写入响应流
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                outputStream.write(fileBytes);
                outputStream.flush();
            }

        } catch (Exception e) {
            log.error("下载编辑文件异常：{}", e.getMessage(), e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":400,\"msg\":\"下载失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 根据detailId和reportId获取报表信息
     *
     * @param detailId
     * @param reportId
     * @return {@link ThsBusinessBudgetProjectDetailExcelReportEntity}
     * <AUTHOR>
     * @date 2025/7/1 11:18
     */
    @Override
    public ThsBusinessBudgetProjectDetailExcelReportEntity getInfoByDetailIdAndReportId(String detailId, String reportId) {
        LambdaQueryWrapper<ThsBusinessBudgetProjectDetailExcelReportEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDetailId, detailId)
                .eq(ThsBusinessBudgetProjectDetailExcelReportEntity::getDataReportId, reportId);
        return this.getOne(wrapper);
    }
}
