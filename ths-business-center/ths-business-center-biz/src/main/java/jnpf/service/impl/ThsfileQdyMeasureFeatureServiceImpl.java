package jnpf.service.impl;

import jnpf.entity.*;
import jnpf.mapper.ThsfileQdyMeasureFeatureMapper;
import jnpf.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jnpf.model.thsfileqdymeasurefeature.*;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jnpf.util.*;
import java.util.*;
/**
 *
 * ThsfileQdyMeasureFeature
 * 版本： V3.5
 * 版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * 作者： JNPF开发平台组
 * 日期： 2023-11-16
 */
@Service
public class ThsfileQdyMeasureFeatureServiceImpl extends ServiceImpl<ThsfileQdyMeasureFeatureMapper, ThsfileQdyMeasureFeatureEntity> implements ThsfileQdyMeasureFeatureService{
    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Override
    public ThsfileQdyMeasureFeatureEntity getInfo(String id){
        QueryWrapper<ThsfileQdyMeasureFeatureEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsfileQdyMeasureFeatureEntity::getId,id);
        return this.getOne(queryWrapper);
    }
    @Override
    public void create(ThsfileQdyMeasureFeatureEntity entity){
        this.save(entity);
    }
    @Override
    public boolean update(String id, ThsfileQdyMeasureFeatureEntity entity){
        return this.updateById(entity);
    }
    @Override
    public void delete(ThsfileQdyMeasureFeatureEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    /** 验证表单唯一字段，正则，非空 i-0新增-1修改*/
    @Override
    public String checkForm(ThsfileQdyMeasureFeatureForm form,int i) {
        boolean isUp =StringUtil.isNotEmpty(form.getId()) && !form.getId().equals("0");
        String id="";
        String countRecover = "";
        if (isUp){
            id = form.getId();
        }
        //主表字段验证
        return countRecover;
    }


    /**
     * 根据fileID获取工料机汇总数据
     *
     * @param fileId
     * @return
     */
    @Override
    public List<ThsfileQdyMeasureFeatureEntity> getListByFileId(String fileId) {
        QueryWrapper<ThsfileQdyMeasureFeatureEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ThsfileQdyMeasureFeatureEntity::getFileId, fileId);
        return this.list(queryWrapper);
    }

    /**
     * 获取措施项目项目特征列表数据，根据fileIds
     * @param fileIds
     * @Date 2024-01-04
     * <AUTHOR>
     * @return
     */
    @Override
    public List<ThsfileQdyMeasureFeatureEntity> findListByFileIds(List<String> fileIds) {

        if (ObjectUtil.isNotEmpty(fileIds) && fileIds.size() > 0) {
            QueryWrapper<ThsfileQdyMeasureFeatureEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ThsfileQdyMeasureFeatureEntity::getFileId, fileIds);
            return this.list(queryWrapper);
        } else {
            return Collections.emptyList();
        }
    }
}
