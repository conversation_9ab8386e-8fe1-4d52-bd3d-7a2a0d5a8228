package jnpf.model.thssysflow;

import jnpf.util.JsonUtil;
import java.util.Map;

/**
 * 流程管理配置json
 *
 * @版本： V3.5
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2023-08-28
 */
public class ThsSysFlowConstant{
    /** 数据库链接 */
    public static final String  DBLINKID = "0";
    /** 表别名 map */
    public static final Map<String,String>  TABLERENAMES = JsonUtil.getJsonToBean("{\"ths_sys_flow\":\"ThsSysFlow\",\"ths_sys_flow_details\":\"ThsSysFlowDetails\"}",Map.class);
    /** 子表model map */
    public static final Map<String,String>  TABLEFIELDKEY = JsonUtil.getJsonToBean("{\"tableField108\":\"ths_sys_flow_details\"}",Map.class);
    /** 整个表单配置json */
    public static final String  getFormData(){
        StringBuilder sb = new StringBuilder();
        sb.append("{\"formRef\":\"formRef\",\"formModel\":\"dataForm\",\"size\":\"default\",\"labelPosition\":\"right\",\"labelWidth\":100,\"formRules\":\"rules\",\"popupType\":\"general\",\"generalWidth\":\"600px\",\"fullScreenWidth\":\"100%\",\"drawerWidth\":\"600px\",\"gutter\":15,\"disabled\":false,\"span\":24,\"colon\":false,\"hasCancelBtn\":true,\"cancelButtonText\":\"取消\",\"hasConfirmBtn\":true,\"confirmButtonText\":\"确定\",\"hasPrintBtn\":false,\"printButtonText\":\"打印\",\"primaryKeyPolicy\":1,\"concurrencyLock\":false,\"logicalDelete\":false,\"printId\":\"\",\"formStyle\":\"\",\"classNames\":[],\"className\":[],\"classJson\":\"\",\"funcs\":{\"onLoad\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"beforeSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    return new Promise((resolve, reject) => {\\n        // 在此编写代码\\n        \\n        // 继续执行\\n        resolve()\\n    })\\n}\",\"afterSubmit\":\"({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"idGlobal\":114,\"fields\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"最后一次修改时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1693222708093},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"lastModifyTime\"},{\"__config__\":{\"jnpfKey\":\"inputNumber\",\"label\":\"是否默認\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInputNumber\",\"tagIcon\":\"icon-ym icon-ym-generator-number\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":[\"blur\",\"change\"],\"formId\":114,\"renderKey\":1708649974259},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"controls\":false,\"addonBefore\":\"\",\"addonAfter\":\"\",\"thousands\":false,\"isAmountChinese\":false,\"step\":1,\"disabled\":false,\"__vModel__\":\"defaultFlag\"},{\"__config__\":{\"jnpfKey\":\"table\",\"label\":\"设计子表\",\"tipLabel\":\"\",\"showLabel\":false,\"tagIcon\":\"icon-ym icon-ym-generator-table\",\"className\":[],\"tag\":\"JnpfInputTable\",\"defaultValue\":[],\"layout\":\"rowFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"noShow\":false,\"showTitle\":true,\"type\":\"table\",\"children\":[{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":110,\"renderKey\":1693222735877,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"sys_flow id\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":112,\"renderKey\":1693225541531,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"flowId\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":111,\"renderKey\":1693222737743,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"是否允许编审，0：不允许：:1：允许编审\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1693222733626,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"allowEditorial\"},{\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":113,\"renderKey\":1693225550947,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"}],\"tableName\":\"ths_sys_flow_details\",\"formId\":108,\"renderKey\":1693222724216,\"componentName\":\"table108\"},\"disabled\":false,\"actionText\":\"添加\",\"showSummary\":0,\"addType\":0,\"addTableConf\":{\"popupTitle\":\"选择数据\",\"popupType\":\"dialog\",\"popupWidth\":\"800px\",\"interfaceId\":\"\",\"interfaceName\":\"\",\"templateJson\":[],\"hasPage\":true,\"pageSize\":20,\"columnOptions\":[],\"relationOptions\":[]},\"summaryField\":[],\"tableConf\":{},\"defaultValue\":[],\"__vModel__\":\"tableField108\"}]}");
        return sb.toString();
    }
    /** 列表字段配置json */
    public static final String  getColumnData(){
        StringBuilder sb = new StringBuilder();
        sb.append("{\"ruleList\":[],\"searchList\":[{\"label\":\"名称\",\"prop\":\"name\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"代号\",\"prop\":\"code\",\"jnpfKey\":\"input\",\"value\":\"\",\"searchType\":2,\"searchMultiple\":false,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"}],\"hasSuperQuery\":false,\"childTableStyle\":1,\"showSummary\":false,\"summaryField\":[],\"columnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\"},{\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\"},{\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"},{\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"最后一次修改时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1693222708093},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"lastModifyTime\"},{\"id\":\"tableField108-code\",\"fullName\":\"设计子表-代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":110,\"renderKey\":1693222735877,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"tableField108-name\",\"fullName\":\"设计子表-名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":111,\"renderKey\":1693222737743,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"tableField108-allowEditorial\",\"fullName\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"是否允许编审，0：不允许：:1：允许编审\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1693222733626,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"allowEditorial\"}],\"defaultColumnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":true},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":true},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\",\"checked\":true},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\",\"checked\":true},{\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\",\"checked\":true},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\",\"checked\":true},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"最后一次修改时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1693222708093},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"lastModifyTime\",\"checked\":false},{\"label\":\"设计子表-代号\",\"prop\":\"tableField108-code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-code\",\"fullName\":\"设计子表-代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":110,\"renderKey\":1693222735877,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":false},{\"label\":\"设计子表-名称\",\"prop\":\"tableField108-name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-name\",\"fullName\":\"设计子表-名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":111,\"renderKey\":1693222737743,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":false},{\"label\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"prop\":\"tableField108-allowEditorial\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-allowEditorial\",\"fullName\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"是否允许编审，0：不允许：:1：允许编审\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1693222733626,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"allowEditorial\",\"checked\":false}],\"type\":1,\"defaultSidx\":\"sequence\",\"sort\":\"desc\",\"hasPage\":false,\"pageSize\":20,\"hasTreeQuery\":false,\"treeTitle\":\"左侧标题\",\"treeDataSource\":\"dictionary\",\"treeDictionary\":\"\",\"treeRelation\":\"\",\"treeSynType\":0,\"treeInterfaceId\":\"\",\"treeInterfaceName\":\"\",\"treeTemplateJson\":[],\"treePropsUrl\":\"\",\"treePropsName\":\"\",\"treePropsValue\":\"id\",\"treePropsChildren\":\"children\",\"treePropsLabel\":\"fullName\",\"groupField\":\"\",\"parentField\":\"\",\"printIds\":[],\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\",\"rowStyle\":\"(record, index) => {\\r\\n   \\r\\n}\",\"cellStyle\":\"(record, rowIndex, column) => {\\r\\n   \\r\\n}\"},\"uploaderTemplateJson\":{}}");
        return sb.toString();
    }
    /** app列表字段配置json */
    public static final String  getAppColumnData(){
        StringBuilder sb = new StringBuilder();
        sb.append("{\"ruleListApp\":[],\"searchList\":[],\"hasSuperQuery\":false,\"columnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\"},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\"},{\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"}],\"columnOptions\":[{\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\"},{\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\"},{\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\"},{\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\"},{\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"最后一次修改时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1693222708093},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"lastModifyTime\"},{\"id\":\"tableField108-code\",\"fullName\":\"设计子表-代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":110,\"renderKey\":1693222735877,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\"},{\"id\":\"tableField108-name\",\"fullName\":\"设计子表-名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":111,\"renderKey\":1693222737743,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\"},{\"id\":\"tableField108-allowEditorial\",\"fullName\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"是否允许编审，0：不允许：:1：允许编审\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1693222733626,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"allowEditorial\"}],\"defaultColumnList\":[{\"label\":\"代号\",\"prop\":\"code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"code\",\"fullName\":\"代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":101,\"renderKey\":1693222644779},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":false},{\"label\":\"名称\",\"prop\":\"name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"name\",\"fullName\":\"名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":102,\"renderKey\":1693222645909},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":false},{\"label\":\"创建人\",\"prop\":\"creatorId\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"creatorId\",\"fullName\":\"创建人\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建人\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":104,\"renderKey\":1693222648060},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"creatorId\",\"checked\":false},{\"label\":\"创建时间\",\"prop\":\"createTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"createTime\",\"fullName\":\"创建时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"创建时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":103,\"renderKey\":1693222647012},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"createTime\",\"checked\":false},{\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"prop\":\"status\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"status\",\"fullName\":\"状态 0：编制中 1:启用中 2：停用\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"状态 0：编制中 1:启用中 2：停用\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":105,\"renderKey\":1693222649628},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"status\",\"checked\":false},{\"label\":\"顺序码\",\"prop\":\"sequence\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"sequence\",\"fullName\":\"顺序码\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"顺序码\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":106,\"renderKey\":1693222694915},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"sequence\",\"checked\":false},{\"label\":\"最后一次修改时间\",\"prop\":\"lastModifyTime\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"lastModifyTime\",\"fullName\":\"最后一次修改时间\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"最后一次修改时间\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":107,\"renderKey\":1693222708093},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"lastModifyTime\",\"checked\":false},{\"label\":\"设计子表-代号\",\"prop\":\"tableField108-code\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-code\",\"fullName\":\"设计子表-代号\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"代号\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":110,\"renderKey\":1693222735877,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"code\",\"checked\":false},{\"label\":\"设计子表-名称\",\"prop\":\"tableField108-name\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-name\",\"fullName\":\"设计子表-名称\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"名称\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":111,\"renderKey\":1693222737743,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"name\",\"checked\":false},{\"label\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"prop\":\"tableField108-allowEditorial\",\"fixed\":\"none\",\"align\":\"left\",\"jnpfKey\":\"input\",\"sortable\":false,\"width\":null,\"id\":\"tableField108-allowEditorial\",\"fullName\":\"设计子表-是否允许编审，0：不允许：:1：允许编审\",\"__config__\":{\"jnpfKey\":\"input\",\"label\":\"是否允许编审，0：不允许：:1：允许编审\",\"tipLabel\":\"\",\"showLabel\":true,\"tag\":\"JnpfInput\",\"tagIcon\":\"icon-ym icon-ym-generator-input\",\"className\":[],\"required\":false,\"layout\":\"colFormItem\",\"span\":24,\"dragDisabled\":false,\"visibility\":[\"pc\",\"app\"],\"tableName\":\"ths_sys_flow\",\"noShow\":false,\"regList\":[],\"trigger\":\"blur\",\"formId\":109,\"renderKey\":1693222733626,\"isSubTable\":true,\"parentVModel\":\"tableField108\",\"relationTable\":\"ths_sys_flow_details\"},\"on\":{\"change\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\",\"blur\":\"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request }) => {\\n    // 在此编写代码\\n    \\n}\"},\"style\":{\"width\":\"100%\"},\"placeholder\":\"请输入\",\"clearable\":true,\"addonBefore\":\"\",\"addonAfter\":\"\",\"prefixIcon\":\"\",\"suffixIcon\":\"\",\"maxlength\":null,\"showPassword\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"allowEditorial\",\"checked\":false}],\"sortList\":[],\"defaultSidx\":\"\",\"sort\":\"desc\",\"hasPage\":true,\"pageSize\":20,\"useColumnPermission\":false,\"useFormPermission\":false,\"useBtnPermission\":false,\"useDataPermission\":false,\"customBtnsList\":[],\"btnsList\":[{\"value\":\"add\",\"icon\":\"icon-ym icon-ym-btn-add\",\"label\":\"新增\"}],\"columnBtnsList\":[{\"value\":\"edit\",\"icon\":\"icon-ym icon-ym-btn-edit\",\"label\":\"编辑\"},{\"value\":\"remove\",\"icon\":\"icon-ym icon-ym-btn-clearn\",\"label\":\"删除\"}],\"funcs\":{\"afterOnload\":\"({ data, tableRef, request }) => {\\r\\n   \\r\\n}\"}}");
        return sb.toString();
    }
    /** 表列表 */
    public static final String  getTableList(){
        StringBuilder sb = new StringBuilder();
        sb.append("[{\"relationField\":\"\",\"relationTable\":\"\",\"table\":\"ths_sys_flow\",\"tableName\":\"系统流程设定表\",\"tableField\":\"\",\"typeId\":\"1\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"code\",\"field\":\"code\",\"fieldName\":\"代号\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"name\",\"field\":\"name\",\"fieldName\":\"名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"creator_id\",\"field\":\"creatorId\",\"fieldName\":\"创建人\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"create_time\",\"field\":\"createTime\",\"fieldName\":\"创建时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"default_flag\",\"field\":\"defaultFlag\",\"fieldName\":\"是否默認\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"status\",\"field\":\"status\",\"fieldName\":\"状态 0：编制中 1:启用中 2：停用\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"language_type\",\"field\":\"languageType\",\"fieldName\":\"语种类型  0： chinese   1: english\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"last_modify_time\",\"field\":\"lastModifyTime\",\"fieldName\":\"最后一次修改时间\",\"dataType\":\"datetime\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowid\",\"field\":\"flowid\",\"fieldName\":\"流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]},{\"relationField\":\"id\",\"relationTable\":\"ths_sys_flow\",\"table\":\"ths_sys_flow_details\",\"tableName\":\"系统流程详情表\",\"tableField\":\"flowId\",\"typeId\":\"0\",\"fields\":[{\"columnName\":\"id\",\"field\":\"id\",\"fieldName\":\"\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":1,\"allowNull\":0,\"autoIncrement\":0},{\"columnName\":\"flow_id\",\"field\":\"flowId\",\"fieldName\":\"sys_flow id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"prev_id\",\"field\":\"prevId\",\"fieldName\":\"前一节点id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"next_id\",\"field\":\"nextId\",\"fieldName\":\"下一节点id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"code\",\"field\":\"code\",\"fieldName\":\"代号\",\"dataType\":\"varchar\",\"dataLength\":\"100\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"name\",\"field\":\"name\",\"fieldName\":\"名称\",\"dataType\":\"varchar\",\"dataLength\":\"300\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"sequence\",\"field\":\"sequence\",\"fieldName\":\"顺序码\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"allow_editorial\",\"field\":\"allowEditorial\",\"fieldName\":\"是否允许编审，0：不允许：:1：允许编审\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"f_flowid\",\"field\":\"flowid\",\"fieldName\":\"流程id\",\"dataType\":\"varchar\",\"dataLength\":\"50\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0},{\"columnName\":\"role_type\",\"field\":\"roleType\",\"fieldName\":\"角色类型 \\r\\n编审的角色类型（0，编制，1，审核，2，核准，3校核，4审批）\",\"dataType\":\"int\",\"dataLength\":\"默认\",\"primaryKey\":0,\"allowNull\":1,\"autoIncrement\":0}]}]");
        return sb.toString();
    }
}
